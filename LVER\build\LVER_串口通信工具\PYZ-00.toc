('D:\\code\\haha\\LVER\\build\\LVER_串口通信工具\\PYZ-00.pyz',
 [('PIL',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsStubImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\FitsStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.Image',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('PIL.ImageFilter',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL._binary',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL._util',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._version',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('PIL.features',
   'c:\\program files\\python\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('__future__', 'c:\\program files\\python\\lib\\__future__.py', 'PYMODULE'),
  ('_compat_pickle',
   'c:\\program files\\python\\lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'c:\\program files\\python\\lib\\_compression.py',
   'PYMODULE'),
  ('_dummy_thread',
   'c:\\program files\\python\\lib\\_dummy_thread.py',
   'PYMODULE'),
  ('_markupbase', 'c:\\program files\\python\\lib\\_markupbase.py', 'PYMODULE'),
  ('_osx_support',
   'c:\\program files\\python\\lib\\_osx_support.py',
   'PYMODULE'),
  ('_py_abc', 'c:\\program files\\python\\lib\\_py_abc.py', 'PYMODULE'),
  ('_pydecimal', 'c:\\program files\\python\\lib\\_pydecimal.py', 'PYMODULE'),
  ('_sitebuiltins',
   'c:\\program files\\python\\lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('_strptime', 'c:\\program files\\python\\lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'c:\\program files\\python\\lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'c:\\program files\\python\\lib\\argparse.py', 'PYMODULE'),
  ('ast', 'c:\\program files\\python\\lib\\ast.py', 'PYMODULE'),
  ('asyncio',
   'c:\\program files\\python\\lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'c:\\program files\\python\\lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'c:\\program files\\python\\lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'c:\\program files\\python\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'c:\\program files\\python\\lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.constants',
   'c:\\program files\\python\\lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'c:\\program files\\python\\lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.events',
   'c:\\program files\\python\\lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'c:\\program files\\python\\lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'c:\\program files\\python\\lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.futures',
   'c:\\program files\\python\\lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.locks',
   'c:\\program files\\python\\lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.log',
   'c:\\program files\\python\\lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'c:\\program files\\python\\lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'c:\\program files\\python\\lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.queues',
   'c:\\program files\\python\\lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'c:\\program files\\python\\lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'c:\\program files\\python\\lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'c:\\program files\\python\\lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'c:\\program files\\python\\lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.streams',
   'c:\\program files\\python\\lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'c:\\program files\\python\\lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'c:\\program files\\python\\lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.transports',
   'c:\\program files\\python\\lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'c:\\program files\\python\\lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'c:\\program files\\python\\lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'c:\\program files\\python\\lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'c:\\program files\\python\\lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('base64', 'c:\\program files\\python\\lib\\base64.py', 'PYMODULE'),
  ('bdb', 'c:\\program files\\python\\lib\\bdb.py', 'PYMODULE'),
  ('bisect', 'c:\\program files\\python\\lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'c:\\program files\\python\\lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'c:\\program files\\python\\lib\\calendar.py', 'PYMODULE'),
  ('cffi',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.api',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.cparser',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('cffi.error',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.lock',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.model',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.verifier',
   'c:\\program files\\python\\lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('cgi', 'c:\\program files\\python\\lib\\cgi.py', 'PYMODULE'),
  ('cmd', 'c:\\program files\\python\\lib\\cmd.py', 'PYMODULE'),
  ('code', 'c:\\program files\\python\\lib\\code.py', 'PYMODULE'),
  ('codeop', 'c:\\program files\\python\\lib\\codeop.py', 'PYMODULE'),
  ('colorsys', 'c:\\program files\\python\\lib\\colorsys.py', 'PYMODULE'),
  ('commctrl',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\commctrl.py',
   'PYMODULE'),
  ('concurrent',
   'c:\\program files\\python\\lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'c:\\program files\\python\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'c:\\program files\\python\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'c:\\program files\\python\\lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'c:\\program files\\python\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('configparser',
   'c:\\program files\\python\\lib\\configparser.py',
   'PYMODULE'),
  ('contextlib', 'c:\\program files\\python\\lib\\contextlib.py', 'PYMODULE'),
  ('contextvars', 'c:\\program files\\python\\lib\\contextvars.py', 'PYMODULE'),
  ('copy', 'c:\\program files\\python\\lib\\copy.py', 'PYMODULE'),
  ('csv', 'c:\\program files\\python\\lib\\csv.py', 'PYMODULE'),
  ('ctypes', 'c:\\program files\\python\\lib\\ctypes\\__init__.py', 'PYMODULE'),
  ('ctypes._aix',
   'c:\\program files\\python\\lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes._endian',
   'c:\\program files\\python\\lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'c:\\program files\\python\\lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('ctypes.util',
   'c:\\program files\\python\\lib\\ctypes\\util.py',
   'PYMODULE'),
  ('dataclasses', 'c:\\program files\\python\\lib\\dataclasses.py', 'PYMODULE'),
  ('datetime', 'c:\\program files\\python\\lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'c:\\program files\\python\\lib\\decimal.py', 'PYMODULE'),
  ('difflib', 'c:\\program files\\python\\lib\\difflib.py', 'PYMODULE'),
  ('dis', 'c:\\program files\\python\\lib\\dis.py', 'PYMODULE'),
  ('distutils',
   'c:\\program files\\python\\lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'c:\\program files\\python\\lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'c:\\program files\\python\\lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'c:\\program files\\python\\lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.cmd',
   'c:\\program files\\python\\lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.command',
   'c:\\program files\\python\\lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'c:\\program files\\python\\lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'c:\\program files\\python\\lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils.command.build_scripts',
   'c:\\program files\\python\\lib\\distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('distutils.command.install',
   'c:\\program files\\python\\lib\\distutils\\command\\install.py',
   'PYMODULE'),
  ('distutils.command.install_scripts',
   'c:\\program files\\python\\lib\\distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'c:\\program files\\python\\lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('distutils.config',
   'c:\\program files\\python\\lib\\distutils\\config.py',
   'PYMODULE'),
  ('distutils.core',
   'c:\\program files\\python\\lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.debug',
   'c:\\program files\\python\\lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'c:\\program files\\python\\lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.dir_util',
   'c:\\program files\\python\\lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.dist',
   'c:\\program files\\python\\lib\\distutils\\dist.py',
   'PYMODULE'),
  ('distutils.errors',
   'c:\\program files\\python\\lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.extension',
   'c:\\program files\\python\\lib\\distutils\\extension.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'c:\\program files\\python\\lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('distutils.file_util',
   'c:\\program files\\python\\lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.filelist',
   'c:\\program files\\python\\lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('distutils.log',
   'c:\\program files\\python\\lib\\distutils\\log.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'c:\\program files\\python\\lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.spawn',
   'c:\\program files\\python\\lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'c:\\program files\\python\\lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('distutils.text_file',
   'c:\\program files\\python\\lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils.util',
   'c:\\program files\\python\\lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.version',
   'c:\\program files\\python\\lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'c:\\program files\\python\\lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('doctest', 'c:\\program files\\python\\lib\\doctest.py', 'PYMODULE'),
  ('dummy_threading',
   'c:\\program files\\python\\lib\\dummy_threading.py',
   'PYMODULE'),
  ('email', 'c:\\program files\\python\\lib\\email\\__init__.py', 'PYMODULE'),
  ('email._encoded_words',
   'c:\\program files\\python\\lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'c:\\program files\\python\\lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'c:\\program files\\python\\lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'c:\\program files\\python\\lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'c:\\program files\\python\\lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'c:\\program files\\python\\lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'c:\\program files\\python\\lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'c:\\program files\\python\\lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'c:\\program files\\python\\lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'c:\\program files\\python\\lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'c:\\program files\\python\\lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'c:\\program files\\python\\lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'c:\\program files\\python\\lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'c:\\program files\\python\\lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'c:\\program files\\python\\lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'c:\\program files\\python\\lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'c:\\program files\\python\\lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'c:\\program files\\python\\lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'c:\\program files\\python\\lib\\email\\utils.py',
   'PYMODULE'),
  ('et_xmlfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('et_xmlfile.incremental_tree',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('fnmatch', 'c:\\program files\\python\\lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'c:\\program files\\python\\lib\\fractions.py', 'PYMODULE'),
  ('ftplib', 'c:\\program files\\python\\lib\\ftplib.py', 'PYMODULE'),
  ('getopt', 'c:\\program files\\python\\lib\\getopt.py', 'PYMODULE'),
  ('getpass', 'c:\\program files\\python\\lib\\getpass.py', 'PYMODULE'),
  ('gettext', 'c:\\program files\\python\\lib\\gettext.py', 'PYMODULE'),
  ('glob', 'c:\\program files\\python\\lib\\glob.py', 'PYMODULE'),
  ('gzip', 'c:\\program files\\python\\lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'c:\\program files\\python\\lib\\hashlib.py', 'PYMODULE'),
  ('hmac', 'c:\\program files\\python\\lib\\hmac.py', 'PYMODULE'),
  ('html', 'c:\\program files\\python\\lib\\html\\__init__.py', 'PYMODULE'),
  ('html.entities',
   'c:\\program files\\python\\lib\\html\\entities.py',
   'PYMODULE'),
  ('html.parser',
   'c:\\program files\\python\\lib\\html\\parser.py',
   'PYMODULE'),
  ('http', 'c:\\program files\\python\\lib\\http\\__init__.py', 'PYMODULE'),
  ('http.client',
   'c:\\program files\\python\\lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'c:\\program files\\python\\lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http.server',
   'c:\\program files\\python\\lib\\http\\server.py',
   'PYMODULE'),
  ('imp', 'c:\\program files\\python\\lib\\imp.py', 'PYMODULE'),
  ('importlib',
   'c:\\program files\\python\\lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'c:\\program files\\python\\lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'c:\\program files\\python\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'c:\\program files\\python\\lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'c:\\program files\\python\\lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'c:\\program files\\python\\lib\\importlib\\metadata.py',
   'PYMODULE'),
  ('importlib.util',
   'c:\\program files\\python\\lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'c:\\program files\\python\\lib\\inspect.py', 'PYMODULE'),
  ('json', 'c:\\program files\\python\\lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'c:\\program files\\python\\lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'c:\\program files\\python\\lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'c:\\program files\\python\\lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'c:\\program files\\python\\lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lxml',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('lxml.cssselect',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('lxml.doctestcompare',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('lxml.html',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('lxml.html.ElementSoup',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.includes',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'c:\\program '
   'files\\python\\lib\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'c:\\program files\\python\\lib\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lzma', 'c:\\program files\\python\\lib\\lzma.py', 'PYMODULE'),
  ('mimetypes', 'c:\\program files\\python\\lib\\mimetypes.py', 'PYMODULE'),
  ('multiprocessing',
   'c:\\program files\\python\\lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'c:\\program files\\python\\lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'c:\\program files\\python\\lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'c:\\program files\\python\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'c:\\program files\\python\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'c:\\program files\\python\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'c:\\program files\\python\\lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'c:\\program files\\python\\lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'c:\\program files\\python\\lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'c:\\program files\\python\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'c:\\program files\\python\\lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'c:\\program files\\python\\lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'c:\\program files\\python\\lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'c:\\program files\\python\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'c:\\program files\\python\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'c:\\program files\\python\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'c:\\program files\\python\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'c:\\program files\\python\\lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'c:\\program files\\python\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'c:\\program files\\python\\lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netbios',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\netbios.py',
   'PYMODULE'),
  ('netrc', 'c:\\program files\\python\\lib\\netrc.py', 'PYMODULE'),
  ('nturl2path', 'c:\\program files\\python\\lib\\nturl2path.py', 'PYMODULE'),
  ('numbers', 'c:\\program files\\python\\lib\\numbers.py', 'PYMODULE'),
  ('numpy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('numpy.__config__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy._globals',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy.array_api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.core.records',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.distutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.fft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.lib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.ma',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.random',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.testing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('numpy.typing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('opcode', 'c:\\program files\\python\\lib\\opcode.py', 'PYMODULE'),
  ('openpyxl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.descriptors.container',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('optparse', 'c:\\program files\\python\\lib\\optparse.py', 'PYMODULE'),
  ('packaging',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('packaging._elffile',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging.licenses',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE'),
  ('packaging.licenses._spdx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE'),
  ('packaging.markers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging.metadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.requirements',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.tags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python38\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('pathlib', 'c:\\program files\\python\\lib\\pathlib.py', 'PYMODULE'),
  ('pdb', 'c:\\program files\\python\\lib\\pdb.py', 'PYMODULE'),
  ('pickle', 'c:\\program files\\python\\lib\\pickle.py', 'PYMODULE'),
  ('pkg_resources',
   'c:\\program files\\python\\lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.appdirs',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\appdirs.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.__about__',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.pyparsing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('pkg_resources._vendor.six',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\_vendor\\six.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('pkg_resources.py2_warn',
   'c:\\program files\\python\\lib\\site-packages\\pkg_resources\\py2_warn.py',
   'PYMODULE'),
  ('pkg_resources.py31compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pkg_resources\\py31compat.py',
   'PYMODULE'),
  ('pkgutil', 'c:\\program files\\python\\lib\\pkgutil.py', 'PYMODULE'),
  ('platform', 'c:\\program files\\python\\lib\\platform.py', 'PYMODULE'),
  ('plistlib', 'c:\\program files\\python\\lib\\plistlib.py', 'PYMODULE'),
  ('pprint', 'c:\\program files\\python\\lib\\pprint.py', 'PYMODULE'),
  ('psutil',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._compat',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'c:\\program files\\python\\lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile', 'c:\\program files\\python\\lib\\py_compile.py', 'PYMODULE'),
  ('pycparser',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'c:\\program '
   'files\\python\\lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.ply',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'c:\\program files\\python\\lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pydoc', 'c:\\program files\\python\\lib\\pydoc.py', 'PYMODULE'),
  ('pydoc_data',
   'c:\\program files\\python\\lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'c:\\program files\\python\\lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pythoncom',
   'c:\\program files\\python\\lib\\site-packages\\pythoncom.py',
   'PYMODULE'),
  ('pywin',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\__init__.py',
   'PYMODULE'),
  ('pywin.dialogs.list',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\list.py',
   'PYMODULE'),
  ('pywin.dialogs.status',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\dialogs\\status.py',
   'PYMODULE'),
  ('pywin.mfc',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\__init__.py',
   'PYMODULE'),
  ('pywin.mfc.dialog',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\dialog.py',
   'PYMODULE'),
  ('pywin.mfc.object',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\object.py',
   'PYMODULE'),
  ('pywin.mfc.thread',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\thread.py',
   'PYMODULE'),
  ('pywin.mfc.window',
   'c:\\program '
   'files\\python\\lib\\site-packages\\Pythonwin\\pywin\\mfc\\window.py',
   'PYMODULE'),
  ('pywintypes',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\pywintypes.py',
   'PYMODULE'),
  ('queue', 'c:\\program files\\python\\lib\\queue.py', 'PYMODULE'),
  ('quopri', 'c:\\program files\\python\\lib\\quopri.py', 'PYMODULE'),
  ('random', 'c:\\program files\\python\\lib\\random.py', 'PYMODULE'),
  ('rlcompleter', 'c:\\program files\\python\\lib\\rlcompleter.py', 'PYMODULE'),
  ('runpy', 'c:\\program files\\python\\lib\\runpy.py', 'PYMODULE'),
  ('secrets', 'c:\\program files\\python\\lib\\secrets.py', 'PYMODULE'),
  ('selectors', 'c:\\program files\\python\\lib\\selectors.py', 'PYMODULE'),
  ('setuptools',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._deprecation_warning',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_deprecation_warning.py',
   'PYMODULE'),
  ('setuptools._imp',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.__about__',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\__about__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.pyparsing',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\_vendor\\pyparsing.py',
   'PYMODULE'),
  ('setuptools._vendor.six',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\_vendor\\six.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools.command',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.easy_install',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\easy_install.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.command.install_scripts',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools.command.py36compat',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\py36compat.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.config',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\config.py',
   'PYMODULE'),
  ('setuptools.depends',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools.dist',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.extension',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.extern',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools.glob',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.installer',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools.package_index',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\package_index.py',
   'PYMODULE'),
  ('setuptools.py27compat',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\py27compat.py',
   'PYMODULE'),
  ('setuptools.py31compat',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\py31compat.py',
   'PYMODULE'),
  ('setuptools.py33compat',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\py33compat.py',
   'PYMODULE'),
  ('setuptools.py34compat',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\py34compat.py',
   'PYMODULE'),
  ('setuptools.sandbox',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\sandbox.py',
   'PYMODULE'),
  ('setuptools.ssl_support',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\ssl_support.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.version',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.wheel',
   'c:\\program files\\python\\lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'c:\\program '
   'files\\python\\lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('shlex', 'c:\\program files\\python\\lib\\shlex.py', 'PYMODULE'),
  ('shutil', 'c:\\program files\\python\\lib\\shutil.py', 'PYMODULE'),
  ('signal', 'c:\\program files\\python\\lib\\signal.py', 'PYMODULE'),
  ('site', 'c:\\program files\\python\\lib\\site.py', 'PYMODULE'),
  ('socket', 'c:\\program files\\python\\lib\\socket.py', 'PYMODULE'),
  ('socketserver',
   'c:\\program files\\python\\lib\\socketserver.py',
   'PYMODULE'),
  ('ssl', 'c:\\program files\\python\\lib\\ssl.py', 'PYMODULE'),
  ('string', 'c:\\program files\\python\\lib\\string.py', 'PYMODULE'),
  ('stringprep', 'c:\\program files\\python\\lib\\stringprep.py', 'PYMODULE'),
  ('subprocess', 'c:\\program files\\python\\lib\\subprocess.py', 'PYMODULE'),
  ('sysconfig', 'c:\\program files\\python\\lib\\sysconfig.py', 'PYMODULE'),
  ('tarfile', 'c:\\program files\\python\\lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'c:\\program files\\python\\lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'c:\\program files\\python\\lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'c:\\program files\\python\\lib\\threading.py', 'PYMODULE'),
  ('tkinter',
   'c:\\program files\\python\\lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'c:\\program files\\python\\lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'c:\\program files\\python\\lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'c:\\program files\\python\\lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'c:\\program files\\python\\lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'c:\\program files\\python\\lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'c:\\program files\\python\\lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'c:\\program files\\python\\lib\\token.py', 'PYMODULE'),
  ('tokenize', 'c:\\program files\\python\\lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc', 'c:\\program files\\python\\lib\\tracemalloc.py', 'PYMODULE'),
  ('tty', 'c:\\program files\\python\\lib\\tty.py', 'PYMODULE'),
  ('typing', 'c:\\program files\\python\\lib\\typing.py', 'PYMODULE'),
  ('unittest',
   'c:\\program files\\python\\lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'c:\\program files\\python\\lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.case',
   'c:\\program files\\python\\lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest.loader',
   'c:\\program files\\python\\lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.main',
   'c:\\program files\\python\\lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.result',
   'c:\\program files\\python\\lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.runner',
   'c:\\program files\\python\\lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.signals',
   'c:\\program files\\python\\lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.suite',
   'c:\\program files\\python\\lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.util',
   'c:\\program files\\python\\lib\\unittest\\util.py',
   'PYMODULE'),
  ('urllib', 'c:\\program files\\python\\lib\\urllib\\__init__.py', 'PYMODULE'),
  ('urllib.error',
   'c:\\program files\\python\\lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'c:\\program files\\python\\lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'c:\\program files\\python\\lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'c:\\program files\\python\\lib\\urllib\\response.py',
   'PYMODULE'),
  ('uu', 'c:\\program files\\python\\lib\\uu.py', 'PYMODULE'),
  ('uuid', 'c:\\program files\\python\\lib\\uuid.py', 'PYMODULE'),
  ('webbrowser', 'c:\\program files\\python\\lib\\webbrowser.py', 'PYMODULE'),
  ('win32com',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\__init__.py',
   'PYMODULE'),
  ('win32com.client',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\__init__.py',
   'PYMODULE'),
  ('win32com.client.CLSIDToClass',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\CLSIDToClass.py',
   'PYMODULE'),
  ('win32com.client.build',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\build.py',
   'PYMODULE'),
  ('win32com.client.dynamic',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\dynamic.py',
   'PYMODULE'),
  ('win32com.client.gencache',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\gencache.py',
   'PYMODULE'),
  ('win32com.client.genpy',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\genpy.py',
   'PYMODULE'),
  ('win32com.client.makepy',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\makepy.py',
   'PYMODULE'),
  ('win32com.client.selecttlb',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\client\\selecttlb.py',
   'PYMODULE'),
  ('win32com.client.util',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\client\\util.py',
   'PYMODULE'),
  ('win32com.server',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\server\\__init__.py',
   'PYMODULE'),
  ('win32com.server.dispatcher',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\server\\dispatcher.py',
   'PYMODULE'),
  ('win32com.server.exception',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32com\\server\\exception.py',
   'PYMODULE'),
  ('win32com.server.policy',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\server\\policy.py',
   'PYMODULE'),
  ('win32com.server.util',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\server\\util.py',
   'PYMODULE'),
  ('win32com.shell',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32comext\\shell\\__init__.py',
   'PYMODULE'),
  ('win32com.shell.shellcon',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32comext\\shell\\shellcon.py',
   'PYMODULE'),
  ('win32com.universal',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\universal.py',
   'PYMODULE'),
  ('win32com.util',
   'c:\\program files\\python\\lib\\site-packages\\win32com\\util.py',
   'PYMODULE'),
  ('win32con',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('win32traceutil',
   'c:\\program '
   'files\\python\\lib\\site-packages\\win32\\lib\\win32traceutil.py',
   'PYMODULE'),
  ('winerror',
   'c:\\program files\\python\\lib\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('xml', 'c:\\program files\\python\\lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.etree',
   'c:\\program files\\python\\lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'c:\\program files\\python\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'c:\\program files\\python\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.parsers',
   'c:\\program files\\python\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'c:\\program files\\python\\lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'c:\\program files\\python\\lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'c:\\program files\\python\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'c:\\program files\\python\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'c:\\program files\\python\\lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'c:\\program files\\python\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'c:\\program files\\python\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc', 'c:\\program files\\python\\lib\\xmlrpc\\__init__.py', 'PYMODULE'),
  ('xmlrpc.client',
   'c:\\program files\\python\\lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile', 'c:\\program files\\python\\lib\\zipfile.py', 'PYMODULE'),
  ('zipimport', 'c:\\program files\\python\\lib\\zipimport.py', 'PYMODULE')])
