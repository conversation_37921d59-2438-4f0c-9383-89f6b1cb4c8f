#!/usr/bin/env python3
"""
测试修复功能 - 验证图表显示和数据导出
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import random
import time
import csv
from datetime import datetime

# 尝试导入matplotlib
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
    MATPLOTLIB_AVAILABLE = True
    print("✓ matplotlib 可用 - 将测试完整图表功能")
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️  matplotlib 不可用 - 将测试文本图表功能")

class FixTestApp:
    def __init__(self, root):
        self.root = root
        self.root.title("功能修复测试 - 图表显示和数据导出")
        self.root.geometry("1200x800")
        
        self.test_data = []
        self.current_values = {
            'V': '--',
            'R_sei': '--', 
            'R_ct': '--',
            'R_ohm': '--'
        }
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建测试界面"""
        # 控制区域
        control_frame = ttk.LabelFrame(self.root, text="功能测试控制", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(control_frame, text="生成测试数据", command=self.generate_test_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="添加单个数据", command=self.add_single_data).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="测试导出功能", command=self.test_export).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清空数据", command=self.clear_data).pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        status_frame = ttk.LabelFrame(self.root, text="测试状态", padding=10)
        status_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.status_label = ttk.Label(status_frame, text="准备就绪 - 点击按钮开始测试")
        self.status_label.pack()
        
        # 主要内容区域
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        # 使用PanedWindow分割图表和数据
        paned = ttk.PanedWindow(main_frame, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True)
        
        # 左侧：图表区域
        chart_frame = ttk.LabelFrame(paned, text="图表显示测试", padding=5)
        paned.add(chart_frame, weight=3)
        
        if MATPLOTLIB_AVAILABLE:
            self.create_matplotlib_chart(chart_frame)
        else:
            self.create_simple_chart(chart_frame)
        
        # 右侧：数据列表
        data_frame = ttk.LabelFrame(paned, text="数据记录", padding=5)
        paned.add(data_frame, weight=1)
        
        # 数据表格
        columns = ('时间', '参数类型', '测量值', '单位')
        self.data_tree = ttk.Treeview(data_frame, columns=columns, show='headings', height=20)
        
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=80)
        
        scrollbar = ttk.Scrollbar(data_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        self.data_tree.configure(yscrollcommand=scrollbar.set)
        
        self.data_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_matplotlib_chart(self, parent):
        """创建matplotlib图表"""
        try:
            # 创建图表框架
            chart_container = ttk.Frame(parent)
            chart_container.pack(fill=tk.BOTH, expand=True)
            
            # 创建matplotlib图表
            self.fig = plt.figure(figsize=(10, 6), facecolor='white')
            self.ax = self.fig.add_subplot(111)
            
            # 创建画布
            self.canvas = FigureCanvasTkAgg(self.fig, chart_container)
            self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 添加工具栏
            toolbar = NavigationToolbar2Tk(self.canvas, chart_container)
            toolbar.update()
            
            # 初始化图表
            self.ax.set_title("多参数测量图表测试", fontsize=16, fontweight='bold')
            self.ax.set_xlabel("测量序号")
            self.ax.set_ylabel("测量值")
            self.ax.grid(True, alpha=0.3)
            self.ax.set_facecolor('#f8f9fa')
            
            self.ax.text(0.5, 0.5, '点击"生成测试数据"开始测试图表功能', 
                        horizontalalignment='center', verticalalignment='center',
                        transform=self.ax.transAxes, fontsize=12,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgreen", alpha=0.5))
            
            self.fig.tight_layout()
            self.canvas.draw()
            
            self.status_label.config(text="✓ matplotlib图表创建成功")
            
        except Exception as e:
            self.status_label.config(text=f"❌ matplotlib图表创建失败: {e}")
            self.create_simple_chart(parent)
    
    def create_simple_chart(self, parent):
        """创建简单文本图表"""
        self.chart_text = tk.Text(parent, height=20, font=("Consolas", 10))
        self.chart_text.pack(fill=tk.BOTH, expand=True)
        
        self.chart_text.insert(1.0, """
文本图表模式测试

当前状态: 等待测试数据...

功能说明:
- 这是matplotlib不可用时的备用显示
- 将显示数据统计和最新记录
- 点击"生成测试数据"开始测试

测试项目:
✓ 文本图表创建
□ 数据显示更新
□ 统计信息计算
□ 数据导出功能
""")
        self.chart_text.config(state=tk.DISABLED)
        
        self.status_label.config(text="✓ 文本图表创建成功 (matplotlib不可用)")
    
    def generate_test_data(self):
        """生成测试数据"""
        self.status_label.config(text="正在生成测试数据...")
        
        # 生成20组测试数据
        for i in range(20):
            # 模拟测试命令返回4种参数
            measurements = [
                ("R_sei", random.randint(270, 290)),
                ("R_ct", random.randint(150, 170)),
                ("V", random.randint(3200, 3400)),
                ("R_ohm", random.randint(500, 530))
            ]
            
            for measure_type, value in measurements:
                self.add_measurement_data(measure_type, value)
            
            # 模拟时间间隔
            time.sleep(0.01)
        
        self.status_label.config(text=f"✓ 生成完成 - 共{len(self.test_data)}条测试数据")
    
    def add_single_data(self):
        """添加单个测试数据"""
        measure_type = random.choice(['V', 'R_sei', 'R_ct', 'R_ohm'])
        
        if measure_type == 'V':
            value = random.randint(3200, 3400)
        else:
            value = random.randint(150, 530)
        
        self.add_measurement_data(measure_type, value)
        self.status_label.config(text=f"✓ 添加数据: {measure_type} = {value}")
    
    def add_measurement_data(self, measure_type, value):
        """添加测量数据"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 确定单位
        unit = "mV" if measure_type == "V" else "μΩ"
        
        # 添加到数据列表
        self.test_data.append({
            'time': timestamp,
            'type': measure_type,
            'value': value,
            'unit': unit
        })
        
        # 更新数据表格
        self.data_tree.insert('', 0, values=(timestamp, measure_type, value, unit))
        
        # 更新当前值
        self.current_values[measure_type] = value
        
        # 更新图表
        self.update_chart()
    
    def update_chart(self):
        """更新图表显示"""
        if MATPLOTLIB_AVAILABLE and hasattr(self, 'ax'):
            try:
                if not self.test_data:
                    return
                
                # 提取数据
                voltages = [(i, float(d['value'])) for i, d in enumerate(self.test_data) if d['type'] == 'V']
                r_sei_values = [(i, float(d['value'])) for i, d in enumerate(self.test_data) if d['type'] == 'R_sei']
                r_ct_values = [(i, float(d['value'])) for i, d in enumerate(self.test_data) if d['type'] == 'R_ct']
                r_ohm_values = [(i, float(d['value'])) for i, d in enumerate(self.test_data) if d['type'] == 'R_ohm']
                
                # 清除并重绘
                self.ax.clear()
                self.ax.set_title("多参数测量图表测试", fontsize=16, fontweight='bold')
                self.ax.set_xlabel("测量序号")
                self.ax.set_ylabel("测量值")
                self.ax.grid(True, alpha=0.3)
                self.ax.set_facecolor('#f8f9fa')
                
                # 绘制数据线
                if voltages:
                    v_times, v_values = zip(*voltages)
                    self.ax.plot(v_times, v_values, 'b-', label='电压 (mV)', marker='o', linewidth=2)
                
                if r_sei_values:
                    sei_times, sei_vals = zip(*r_sei_values)
                    self.ax.plot(sei_times, sei_vals, 'g-', label='SEI电阻 (μΩ)', marker='s', linewidth=2)
                
                if r_ct_values:
                    ct_times, ct_vals = zip(*r_ct_values)
                    self.ax.plot(ct_times, ct_vals, 'purple', label='CT电阻 (μΩ)', marker='^', linewidth=2)
                
                if r_ohm_values:
                    ohm_times, ohm_vals = zip(*r_ohm_values)
                    self.ax.plot(ohm_times, ohm_vals, 'r-', label='欧姆电阻 (μΩ)', marker='d', linewidth=2)
                
                if any([voltages, r_sei_values, r_ct_values, r_ohm_values]):
                    self.ax.legend()
                
                self.fig.tight_layout()
                self.canvas.draw()
                
            except Exception as e:
                print(f"图表更新失败: {e}")
        
        elif hasattr(self, 'chart_text'):
            # 更新文本图表
            chart_content = f"""
文本图表模式测试

当前测量值:
- 电压 (V): {self.current_values['V']} mV
- SEI电阻: {self.current_values['R_sei']} μΩ
- CT电阻: {self.current_values['R_ct']} μΩ
- 欧姆电阻: {self.current_values['R_ohm']} μΩ

数据统计:
- 总数据点: {len(self.test_data)}
- 电压测量: {len([d for d in self.test_data if d['type'] == 'V'])} 次
- SEI电阻: {len([d for d in self.test_data if d['type'] == 'R_sei'])} 次
- CT电阻: {len([d for d in self.test_data if d['type'] == 'R_ct'])} 次
- 欧姆电阻: {len([d for d in self.test_data if d['type'] == 'R_ohm'])} 次

最近5条数据:
"""
            for data in self.test_data[-5:]:
                chart_content += f"  {data['time']} - {data['type']}: {data['value']} {data['unit']}\n"
            
            self.chart_text.config(state=tk.NORMAL)
            self.chart_text.delete(1.0, tk.END)
            self.chart_text.insert(1.0, chart_content)
            self.chart_text.config(state=tk.DISABLED)
    
    def test_export(self):
        """测试数据导出功能"""
        if not self.test_data:
            messagebox.showwarning("警告", "没有数据可导出\n\n请先生成测试数据。")
            return
        
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            default_filename = f"测试导出数据_{timestamp}.csv"
            
            filename = filedialog.asksaveasfilename(
                title="测试数据导出功能",
                defaultextension=".csv",
                filetypes=[("CSV 文件", "*.csv"), ("所有文件", "*.*")],
                initialname=default_filename
            )
            
            if not filename:
                self.status_label.config(text="用户取消了导出测试")
                return
            
            # 导出数据
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['时间', '参数类型', '测量值', '单位'])
                
                for data in self.test_data:
                    writer.writerow([data['time'], data['type'], data['value'], data['unit']])
            
            # 统计信息
            stats = {
                'total': len(self.test_data),
                'voltage': len([d for d in self.test_data if d['type'] == 'V']),
                'r_sei': len([d for d in self.test_data if d['type'] == 'R_sei']),
                'r_ct': len([d for d in self.test_data if d['type'] == 'R_ct']),
                'r_ohm': len([d for d in self.test_data if d['type'] == 'R_ohm'])
            }
            
            success_msg = f"""✓ 导出功能测试成功！

文件: {filename}
总记录: {stats['total']} 条
电压: {stats['voltage']} 条
SEI电阻: {stats['r_sei']} 条
CT电阻: {stats['r_ct']} 条
欧姆电阻: {stats['r_ohm']} 条"""
            
            messagebox.showinfo("导出测试成功", success_msg)
            self.status_label.config(text=f"✓ 导出测试成功 - {stats['total']} 条记录")
            
        except Exception as e:
            messagebox.showerror("导出测试失败", f"导出测试失败: {e}")
            self.status_label.config(text=f"❌ 导出测试失败: {e}")
    
    def clear_data(self):
        """清空测试数据"""
        self.test_data.clear()
        
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)
        
        for key in self.current_values:
            self.current_values[key] = '--'
        
        self.update_chart()
        self.status_label.config(text="✓ 测试数据已清空")

def main():
    print("="*60)
    print("功能修复测试程序")
    print("="*60)
    print("测试项目:")
    print("1. 图表显示功能")
    print("2. 数据导出功能")
    print("3. 多参数数据处理")
    print("="*60)
    
    root = tk.Tk()
    app = FixTestApp(root)
    
    print("✓ 测试程序启动成功")
    print("✓ 请在GUI中测试各项功能")
    
    root.mainloop()

if __name__ == "__main__":
    main()
