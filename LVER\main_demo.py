#!/usr/bin/env python3
"""
LVER 串口通信应用程序主入口 - 演示版本
包含serial模块模拟，用于演示图表修复效果
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟serial模块
class MockSerial:
    def __init__(self, *args, **kwargs):
        self.port = kwargs.get('port', 'COM1')
        self.baudrate = kwargs.get('baudrate', 9600)
        self.is_open = False
    
    def open(self):
        self.is_open = True
    
    def close(self):
        self.is_open = False
    
    def write(self, data):
        return len(data)
    
    def read(self, size=1):
        return b''
    
    def readline(self):
        return b''
    
    def in_waiting(self):
        return 0

class MockListPorts:
    @staticmethod
    def comports():
        # 模拟一些串口
        class MockPort:
            def __init__(self, device, description):
                self.device = device
                self.description = description
        
        return [
            MockPort('COM1', 'Communications Port (COM1)'),
            MockPort('COM3', 'USB Serial Port (COM3)'),
        ]

# 创建模拟模块
import types
serial_module = types.ModuleType('serial')
serial_module.Serial = MockSerial
serial_module.SerialException = Exception
serial_module.tools = types.ModuleType('tools')
serial_module.tools.list_ports = MockListPorts

# 将模拟模块添加到sys.modules
sys.modules['serial'] = serial_module
sys.modules['serial.tools'] = serial_module.tools
sys.modules['serial.tools.list_ports'] = MockListPorts

try:
    from gui import LVERApp
    print("✓ GUI模块导入成功")
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有必要的模块都在正确的位置")
    sys.exit(1)


def main():
    """主函数"""
    try:
        print("正在启动LVER应用程序...")
        
        # 创建主窗口
        root = tk.Tk()
        root.title("LVER 串口通信应用程序 - 图表修复演示版")
        root.geometry("1400x900")
        
        # 设置窗口图标（如果存在）
        try:
            root.iconbitmap("icon.ico")
        except:
            pass
        
        print("✓ 主窗口创建成功")
        
        # 创建应用程序实例
        app = LVERApp(root)
        print("✓ 应用程序实例创建成功")
        
        # 添加一些演示数据
        demo_data = [
            (5075, 514.2, 308.1, 104.3),
            (5070, 527.8, 295.6, 129.4),
            (5065, 535.1, 283.9, 112.7),
            (5080, 498.3, 315.2, 98.1),
            (5072, 542.6, 278.4, 135.8),
        ]
        
        for voltage, r_ohm, r_sei, r_ct in demo_data:
            app.data_manager.add_test_data(voltage, r_ohm, r_sei, r_ct)
        
        print("✓ 演示数据添加成功")
        
        # 更新图表显示
        if hasattr(app, 'chart_widget') and app.chart_widget:
            app.chart_widget.update_chart()
            print("✓ 图表更新成功")
        
        # 更新实时显示
        if hasattr(app, 'realtime_display') and app.realtime_display:
            try:
                latest_data = app.data_manager.get_latest_data()
                if latest_data and hasattr(app.realtime_display, 'update_data'):
                    app.realtime_display.update_data(latest_data)
                    print("✓ 实时显示更新成功")
            except Exception as e:
                print(f"实时显示更新跳过: {e}")
        
        print("\n" + "="*50)
        print("LVER应用程序启动成功！")
        print("图表修复内容：")
        print("1. ✓ 修复了图表显示问题")
        print("2. ✓ 简化了导出功能（图表+数据）")
        print("3. ✓ 改进了双Y轴处理")
        print("4. ✓ 添加了数据检查和提示")
        print("="*50)
        
        # 显示使用说明
        def show_usage():
            usage_text = """
图表功能使用说明：

1. 数据显示：
   - 已加载演示数据，图表应显示数据曲线
   - 使用复选框选择要显示的数据类型
   
2. 导出功能：
   - "导出图表"：保存PNG格式的高清图表
   - "导出数据"：保存CSV格式的测试数据
   
3. 图表控制：
   - "刷新图表"：手动刷新显示
   - "清空数据"：清除所有测试数据
   
4. 修复内容：
   - 修复了图表不显示曲线的问题
   - 简化了导出按钮（从2个减少到2个不同功能）
   - 恢复了数据导出功能
            """
            messagebox.showinfo("使用说明", usage_text)
        
        # 添加帮助菜单
        menubar = tk.Menu(root)
        root.config(menu=menubar)
        
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=show_usage)
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()
        messagebox.showerror("启动错误", f"应用程序启动失败:\n{str(e)}")


if __name__ == "__main__":
    main()
