# 鲸测云LCER电池测试仪 - COM5权限问题解决脚本

Write-Host ""
Write-Host "🔋 鲸测云LCER电池测试仪 - COM5权限问题解决" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Gray
Write-Host ""

# 检查是否以管理员身份运行
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "⚠️  当前未以管理员身份运行" -ForegroundColor Yellow
    Write-Host "🔧 正在以管理员身份重新启动..." -ForegroundColor Cyan
    
    # 以管理员身份重新启动脚本
    Start-Process PowerShell -Verb RunAs -ArgumentList "-File `"$PSCommandPath`""
    exit
}

Write-Host "✅ 已获得管理员权限" -ForegroundColor Green
Write-Host ""

# 检查COM5端口
Write-Host "🔍 检查COM5端口状态..." -ForegroundColor Cyan
try {
    $comPorts = [System.IO.Ports.SerialPort]::getportnames()
    if ($comPorts -contains "COM5") {
        Write-Host "✅ COM5端口已检测到" -ForegroundColor Green
    } else {
        Write-Host "❌ COM5端口未检测到" -ForegroundColor Red
        Write-Host "💡 请检查USB设备是否正确连接" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  无法检查COM端口状态" -ForegroundColor Yellow
}

Write-Host ""

# 结束可能占用COM5的进程
Write-Host "🔧 结束可能占用COM5的进程..." -ForegroundColor Cyan
$processesToKill = @("sscom32", "putty", "teraterm", "hyperterminal", "serialport", "comtool")

foreach ($processName in $processesToKill) {
    try {
        $processes = Get-Process -Name $processName -ErrorAction SilentlyContinue
        if ($processes) {
            $processes | Stop-Process -Force
            Write-Host "✓ 已结束进程: $processName" -ForegroundColor Green
        }
    } catch {
        # 忽略错误
    }
}

Write-Host "✅ 进程清理完成" -ForegroundColor Green
Write-Host ""

# 重置COM5端口
Write-Host "🔄 重置COM5端口..." -ForegroundColor Cyan
try {
    # 查找COM5相关的PnP设备
    $com5Device = Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like "*COM5*"}
    
    if ($com5Device) {
        Write-Host "📍 找到COM5设备: $($com5Device.Name)" -ForegroundColor Yellow
        
        # 禁用设备
        $com5Device.Disable()
        Start-Sleep -Seconds 2
        
        # 启用设备
        $com5Device.Enable()
        Start-Sleep -Seconds 2
        
        Write-Host "✅ COM5端口重置完成" -ForegroundColor Green
    } else {
        Write-Host "⚠️  未找到COM5设备" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ 端口重置失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 启动应用程序
Write-Host "🚀 启动应用程序..." -ForegroundColor Cyan
try {
    Set-Location "D:\code\haha\LVER"
    
    $pythonPath = "D:\code\haha\.venv\Scripts\python.exe"
    $scriptPath = "main_fixed_complete.py"
    
    if (Test-Path $pythonPath) {
        Write-Host "✓ Python环境已找到" -ForegroundColor Green
        
        if (Test-Path $scriptPath) {
            Write-Host "✓ 应用程序脚本已找到" -ForegroundColor Green
            Write-Host "🎯 正在启动应用程序..." -ForegroundColor Cyan
            
            # 启动应用程序
            Start-Process -FilePath $pythonPath -ArgumentList $scriptPath -WorkingDirectory (Get-Location)
            
            Write-Host "✅ 应用程序已启动" -ForegroundColor Green
            Write-Host "💡 请在新窗口中测试COM5连接" -ForegroundColor Yellow
        } else {
            Write-Host "❌ 未找到应用程序脚本: $scriptPath" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ 未找到Python环境: $pythonPath" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 启动应用程序失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "================================================================" -ForegroundColor Gray
Write-Host "🎯 如果问题仍未解决，请尝试:" -ForegroundColor Yellow
Write-Host "1. 重新插拔USB设备" -ForegroundColor White
Write-Host "2. 重启计算机" -ForegroundColor White
Write-Host "3. 更新USB转串口驱动程序" -ForegroundColor White
Write-Host "================================================================" -ForegroundColor Gray
Write-Host ""

Read-Host "按回车键退出"
