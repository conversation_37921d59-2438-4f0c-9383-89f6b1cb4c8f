#!/usr/bin/env python3
"""
测试导出功能修复 - 验证 initialfile 参数修复
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import csv
import os
from datetime import datetime

def test_export_function():
    """测试修复后的导出功能"""
    print("=" * 60)
    print("测试导出功能修复")
    print("=" * 60)
    print("问题: 'bad option \"-initialname\"' 错误")
    print("修复: 将 initialname 改为 initialfile")
    print("=" * 60)
    
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    try:
        # 生成默认文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_filename = f"LVER_导出测试_{timestamp}.csv"
        
        print(f"默认文件名: {default_filename}")
        
        # 测试修复后的文件保存对话框
        filename = filedialog.asksaveasfilename(
            title="测试导出功能修复",
            defaultextension=".csv",
            filetypes=[
                ("CSV 文件", "*.csv"),
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ],
            initialfile=default_filename  # 修复: 使用 initialfile 而不是 initialname
        )
        
        if filename:
            print(f"✓ 文件对话框正常工作")
            print(f"✓ 用户选择的文件: {filename}")
            
            # 创建测试数据
            test_data = [
                {'time': '10:03:00', 'type': 'V', 'value': 3327, 'unit': 'mV'},
                {'time': '10:03:00', 'type': 'R_sei', 'value': 285, 'unit': 'μΩ'},
                {'time': '10:03:00', 'type': 'R_ct', 'value': 304, 'unit': 'μΩ'},
                {'time': '10:03:00', 'type': 'R_ohm', 'value': 270, 'unit': 'μΩ'},
                {'time': '10:03:01', 'type': 'V', 'value': 3341, 'unit': 'mV'},
                {'time': '10:03:01', 'type': 'R_sei', 'value': 296, 'unit': 'μΩ'},
                {'time': '10:03:01', 'type': 'R_ct', 'value': 161, 'unit': 'μΩ'},
                {'time': '10:03:01', 'type': 'R_ohm', 'value': 285, 'unit': 'μΩ'},
            ]
            
            # 写入CSV文件
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['时间', '参数类型', '测量值', '单位'])
                
                for data in test_data:
                    writer.writerow([data['time'], data['type'], data['value'], data['unit']])
            
            print(f"✓ 文件写入成功: {filename}")
            
            # 验证文件
            if os.path.exists(filename):
                file_size = os.path.getsize(filename)
                print(f"✓ 文件验证成功，大小: {file_size} 字节")
                
                # 读取并显示内容
                with open(filename, 'r', encoding='utf-8-sig') as f:
                    content = f.read()
                    print("文件内容预览:")
                    print("-" * 40)
                    lines = content.split('\n')
                    for i, line in enumerate(lines[:6]):  # 显示前6行
                        print(f"{i+1:2d}: {line}")
                    if len(lines) > 6:
                        print(f"... (共 {len(lines)} 行)")
                    print("-" * 40)
                
                # 统计信息
                stats = {
                    'total': len(test_data),
                    'voltage': len([d for d in test_data if d['type'] == 'V']),
                    'r_sei': len([d for d in test_data if d['type'] == 'R_sei']),
                    'r_ct': len([d for d in test_data if d['type'] == 'R_ct']),
                    'r_ohm': len([d for d in test_data if d['type'] == 'R_ohm'])
                }
                
                success_msg = f"""✓ 导出功能测试成功！

文件: {filename}
大小: {file_size} 字节

数据统计:
• 总记录数: {stats['total']} 条
• 电压测量: {stats['voltage']} 条
• SEI电阻: {stats['r_sei']} 条
• CT电阻: {stats['r_ct']} 条
• 欧姆电阻: {stats['r_ohm']} 条

修复验证:
✓ initialfile 参数正常工作
✓ 文件对话框无错误
✓ CSV 文件正确生成
✓ 中文编码正确处理"""
                
                print(success_msg)
                messagebox.showinfo("测试成功", success_msg)
                
                # 询问是否打开文件
                if messagebox.askyesno("打开文件", "是否要打开测试文件验证内容？"):
                    try:
                        os.startfile(filename)
                        print("✓ 文件已在默认程序中打开")
                    except Exception as e:
                        print(f"❌ 无法打开文件: {e}")
                        messagebox.showerror("打开失败", f"无法打开文件: {e}")
                        
                print("\n" + "=" * 60)
                print("✓ 导出功能修复验证完成")
                print("✓ initialfile 参数修复成功")
                print("✓ 文件对话框正常工作")
                print("✓ CSV 导出功能正常")
                print("=" * 60)
                
            else:
                print("❌ 文件验证失败")
                messagebox.showerror("测试失败", "文件创建失败")
        else:
            print("用户取消了文件选择")
            messagebox.showinfo("取消", "用户取消了测试")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        messagebox.showerror("测试失败", f"导出功能测试失败:\n\n{str(e)}")
        import traceback
        traceback.print_exc()
    
    root.destroy()

def show_fix_comparison():
    """显示修复前后的代码对比"""
    print("\n" + "=" * 60)
    print("修复前后代码对比")
    print("=" * 60)
    
    print("❌ 修复前 (错误代码):")
    print("-" * 30)
    print("""filename = filedialog.asksaveasfilename(
    title="导出测量数据",
    defaultextension=".csv",
    filetypes=[("CSV 文件", "*.csv")],
    initialname=default_filename  # ❌ 错误参数
)""")
    
    print("\n✓ 修复后 (正确代码):")
    print("-" * 30)
    print("""filename = filedialog.asksaveasfilename(
    title="导出测量数据",
    defaultextension=".csv",
    filetypes=[("CSV 文件", "*.csv")],
    initialfile=default_filename  # ✓ 正确参数
)""")
    
    print("\n修复说明:")
    print("• tkinter.filedialog.asksaveasfilename() 使用 'initialfile' 参数")
    print("• 'initialname' 参数不存在，会导致 'bad option' 错误")
    print("• 修复后文件对话框可以正常显示默认文件名")
    print("=" * 60)

def main():
    print("LVER 导出功能修复测试工具")
    show_fix_comparison()
    
    # 询问是否进行实际测试
    root = tk.Tk()
    root.withdraw()
    
    if messagebox.askyesno("测试确认", "是否要进行导出功能的实际测试？\n\n这将打开文件保存对话框。"):
        test_export_function()
    else:
        print("用户跳过了实际测试")
    
    root.destroy()

if __name__ == "__main__":
    main()
