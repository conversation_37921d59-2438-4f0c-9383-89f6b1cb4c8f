#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
pyserial库安装脚本
自动检测Python环境并安装pyserial库
"""

import sys
import subprocess
import os
from datetime import datetime

def get_python_info():
    """获取Python环境信息"""
    print("🐍 Python环境信息:")
    print(f"   版本: {sys.version}")
    print(f"   路径: {sys.executable}")
    print(f"   平台: {sys.platform}")
    return sys.executable

def check_pyserial():
    """检查pyserial是否已安装"""
    print("\n🔍 检查pyserial库...")
    try:
        import serial
        print("✅ pyserial已安装")
        print(f"   版本: {serial.VERSION}")
        return True
    except ImportError:
        print("❌ pyserial未安装")
        return False

def install_pyserial(python_exe):
    """安装pyserial库"""
    print(f"\n📦 正在安装pyserial库...")
    
    # 尝试多种安装方法
    install_commands = [
        [python_exe, "-m", "pip", "install", "pyserial"],
        [python_exe, "-m", "pip", "install", "pyserial", "--user"],
        ["pip", "install", "pyserial"],
        ["pip", "install", "pyserial", "--user"]
    ]
    
    for i, cmd in enumerate(install_commands, 1):
        try:
            print(f"\n🔄 尝试方法 {i}: {' '.join(cmd)}")
            
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True, 
                timeout=120
            )
            
            if result.returncode == 0:
                print("✅ 安装成功!")
                print(f"输出: {result.stdout}")
                return True
            else:
                print(f"❌ 安装失败 (返回码: {result.returncode})")
                print(f"错误: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏰ 安装超时")
        except FileNotFoundError:
            print(f"❌ 命令未找到: {cmd[0]}")
        except Exception as e:
            print(f"❌ 安装异常: {e}")
    
    return False

def verify_installation():
    """验证安装结果"""
    print("\n🧪 验证安装结果...")
    
    try:
        import serial
        import serial.tools.list_ports
        
        print("✅ pyserial导入成功")
        print(f"   版本: {serial.VERSION}")
        
        # 测试串口列表功能
        try:
            ports = list(serial.tools.list_ports.comports())
            print(f"   发现串口: {len(ports)} 个")
            for port in ports:
                print(f"     📍 {port.device}: {port.description}")
        except Exception as e:
            print(f"   ⚠️  串口列表功能异常: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def create_test_script():
    """创建测试脚本"""
    test_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
import serial
import serial.tools.list_ports

print("🔋 pyserial测试脚本")
print(f"pyserial版本: {serial.VERSION}")

print("\\n📋 可用串口:")
ports = list(serial.tools.list_ports.comports())
for port in ports:
    print(f"  {port.device}: {port.description}")

if 'COM5' in [p.device for p in ports]:
    print("\\n✅ 发现COM5端口")
else:
    print("\\n⚠️  未发现COM5端口")

print("\\n🎉 pyserial安装验证完成!")
"""
    
    with open("测试pyserial.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("📝 已创建测试脚本: 测试pyserial.py")

def main():
    """主安装函数"""
    print("🔋 鲸测云LCER电池测试仪 - pyserial库安装工具")
    print("=" * 60)
    print(f"安装时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 获取Python信息
    python_exe = get_python_info()
    
    # 2. 检查当前状态
    if check_pyserial():
        print("\n🎉 pyserial已经安装，无需重复安装")
        verify_installation()
        create_test_script()
        return True
    
    # 3. 安装pyserial
    print("\n🚀 开始安装pyserial库...")
    success = install_pyserial(python_exe)
    
    if not success:
        print("\n❌ 自动安装失败")
        print("\n💡 手动安装方法:")
        print("1. 打开命令提示符 (cmd)")
        print("2. 运行以下命令之一:")
        print("   pip install pyserial")
        print("   python -m pip install pyserial")
        print("   conda install pyserial  (如果使用Anaconda)")
        print("\n3. 安装完成后重新运行主程序")
        return False
    
    # 4. 验证安装
    if verify_installation():
        print("\n🎉 pyserial安装成功!")
        create_test_script()
        
        print("\n📋 下一步操作:")
        print("1. 重新运行主程序: python main_fixed_complete.py")
        print("2. 连接COM5设备")
        print("3. 执行测试命令获取真实数据")
        print("4. 验证电压值不再是固定的3290 mV")
        
        return True
    else:
        print("\n❌ 安装验证失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        
        if success:
            print(f"\n✅ 安装完成! 现在可以获取真实设备数据了")
        else:
            print(f"\n❌ 安装失败! 请手动安装pyserial库")
            
        input("\n按回车键退出...")
        
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断安装")
    except Exception as e:
        print(f"\n❌ 安装过程出现异常: {e}")
        import traceback
        traceback.print_exc()
