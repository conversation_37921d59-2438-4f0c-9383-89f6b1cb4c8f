#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证品牌更新脚本
检查main_fixed_complete.py中的所有LVER文本是否已更新为鲸测云LCER电池测试仪
"""

def check_brand_updates():
    """检查品牌更新状态"""
    print("=" * 70)
    print("🔍 验证品牌更新状态")
    print("=" * 70)
    
    try:
        with open('main_fixed_complete.py', 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        print("📋 检查LVER文本替换:")
        print()
        
        # 查找剩余的LVER文本
        lver_lines = []
        for i, line in enumerate(lines, 1):
            if 'LVER' in line and '鲸测云LCER' not in line:
                lver_lines.append((i, line.strip()))
        
        if lver_lines:
            print("⚠️  发现未更新的LVER文本:")
            for line_num, line_content in lver_lines:
                print(f"  第{line_num}行: {line_content}")
            print()
        else:
            print("✅ 所有LVER文本已成功更新为鲸测云LCER")
            print()
        
        # 检查羽毛图标
        print("🪶 检查羽毛图标:")
        feather_lines = []
        for i, line in enumerate(lines, 1):
            if '🪶' in line:
                feather_lines.append((i, line.strip()))
        
        if feather_lines:
            print("⚠️  发现羽毛图标:")
            for line_num, line_content in feather_lines:
                print(f"  第{line_num}行: {line_content}")
            print()
        else:
            print("✅ 未发现羽毛图标")
            print()
        
        # 检查窗口标题
        print("🔋 检查窗口标题:")
        title_lines = [line for line in lines if 'title(' in line and '鲸测云LCER电池测试仪' in line]
        if title_lines:
            print("✅ 窗口标题已正确设置:")
            for line in title_lines:
                print(f"  {line.strip()}")
            print()
        else:
            print("⚠️  窗口标题可能未正确设置")
            print()
        
        # 检查导出文件名
        print("📁 检查导出文件名:")
        export_lines = []
        for i, line in enumerate(lines, 1):
            if 'default_filename' in line and '鲸测云LCER' in line:
                export_lines.append((i, line.strip()))
        
        print(f"✅ 发现 {len(export_lines)} 个导出文件名已更新:")
        for line_num, line_content in export_lines:
            print(f"  第{line_num}行: {line_content}")
        print()
        
        # 检查Excel内容
        print("📊 检查Excel内容:")
        excel_content_lines = []
        for i, line in enumerate(lines, 1):
            if ('ws[' in line or 'writer.writerow' in line) and '鲸测云LCER' in line:
                excel_content_lines.append((i, line.strip()))
        
        print(f"✅ 发现 {len(excel_content_lines)} 个Excel内容已更新:")
        for line_num, line_content in excel_content_lines:
            print(f"  第{line_num}行: {line_content}")
        print()
        
        # 总结
        print("📋 更新总结:")
        print(f"  • 剩余LVER文本: {len(lver_lines)} 处")
        print(f"  • 羽毛图标: {len(feather_lines)} 处")
        print(f"  • 窗口标题: {len(title_lines)} 处")
        print(f"  • 导出文件名: {len(export_lines)} 处")
        print(f"  • Excel内容: {len(excel_content_lines)} 处")
        print()
        
        if len(lver_lines) == 0 and len(feather_lines) == 0:
            print("🎉 品牌更新验证通过！")
            print("✅ 所有LVER文本已更新为鲸测云LCER")
            print("✅ 所有羽毛图标已移除")
            print("✅ 应用程序品牌标识统一")
        else:
            print("⚠️  品牌更新未完全完成，请检查上述问题")
        
        print("=" * 70)
        
    except FileNotFoundError:
        print("❌ 错误: 找不到main_fixed_complete.py文件")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    check_brand_updates()
