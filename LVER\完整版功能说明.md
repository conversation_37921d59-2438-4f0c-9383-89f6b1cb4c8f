# LVER 串口通信工具 - 完整多参数版

## 🎯 功能概述

这是一个专业的串口通信工具，专门设计用于与LVER设备进行多参数测量通信。应用程序完全符合您在其他上位机中看到的多参数显示要求。

## 📊 多参数显示功能

### 支持的测量参数
- **电压 (V)**: 单位 mV，蓝色显示
- **SEI电阻 (R_sei)**: 单位 μΩ，绿色显示  
- **CT电阻 (R_ct)**: 单位 μΩ，紫色显示
- **欧姆电阻 (R_ohm)**: 单位 μΩ，红色显示

### 实时显示卡片
应用程序包含6个专业数据卡片：
1. **电压 (V)** - 蓝色，显示电压测量值
2. **SEI电阻** - 绿色，显示SEI电阻值
3. **CT电阻** - 紫色，显示CT电阻值
4. **欧姆电阻** - 红色，显示欧姆电阻值
5. **测试次数** - 橙色，显示总测试次数
6. **连接状态** - 灰色，显示串口连接状态

## 🔧 设备通信协议

### 测试命令 (0xAA)
- **功能**: 获取完整的多参数测量
- **返回**: 4种参数 (R_sei, R_ct, V, R_ohm)
- **数值范围**:
  - R_sei: 270-290 μΩ
  - R_ct: 150-170 μΩ
  - V: 3200-3400 mV
  - R_ohm: 500-530 μΩ

### 电压测量命令 (0x55)
- **功能**: 单独获取电压测量
- **返回**: 电压值 (V)
- **数值范围**: 3200-3400 mV

### 复位命令 (0xA0)
- **功能**: 设备复位
- **返回**: 无响应

## 🚀 使用方法

### 1. 启动应用程序
- 双击 `启动完整版.bat` 或
- 运行 `python main_complete_final.py`

### 2. 连接设备
1. 选择 **COM5** 串口
2. 设置波特率为 **115200**
3. 点击 **连接** 按钮

### 3. 进行测量
- **单次测试**: 点击"测试命令 (0xAA)"获取4种参数
- **电压测量**: 点击"电压测量 (0x55)"获取电压值
- **连续测试**: 勾选"连续测试"进行自动监控

### 4. 查看数据
- **实时卡片**: 查看6个大字体数据卡片
- **数据表格**: 右侧显示详细测量记录
- **图表显示**: 多线图表显示参数趋势

### 5. 数据管理
- **导出数据**: 点击"导出数据"保存为CSV文件
- **清空数据**: 点击"清空数据"重置所有记录

## 📈 图表功能

### matplotlib 图表 (推荐)
- 多条彩色数据线
- 实时更新显示
- 专业图表样式
- 支持缩放和导航

### 文本图表 (备用)
- 当matplotlib不可用时自动启用
- 显示当前测量值
- 显示数据统计信息
- 显示最近测量记录

## 💾 数据导出

### CSV 格式
导出的数据包含以下列：
- **时间**: 测量时间戳
- **参数类型**: V, R_sei, R_ct, R_ohm
- **测量值**: 数值
- **单位**: mV 或 μΩ

### 文件命名
自动生成文件名格式：`LVER_多参数数据_YYYYMMDD_HHMMSS.csv`

## 🔄 连续测试功能

### 自动监控
- 可设置测试间隔 (0.5-10.0秒)
- 自动循环执行测试命令
- 实时更新所有显示
- 可随时停止

### 适用场景
- 长期监控设备状态
- 参数变化趋势分析
- 自动数据收集

## 🛠️ 技术特性

### 自包含设计
- 内置串口模拟功能
- 内置图表显示功能
- 无需额外依赖安装
- 开箱即用

### 兼容性
- 支持 Windows 系统
- 自动检测 COM5 串口
- 兼容多种波特率
- 支持中文界面

### 错误处理
- 完善的异常处理
- 友好的错误提示
- 自动重连机制
- 日志记录功能

## 📋 系统要求

- **操作系统**: Windows 7/8/10/11
- **Python**: 3.6 或更高版本
- **串口**: COM5 (您的设备串口)
- **波特率**: 115200 (推荐)

## 🎨 界面特色

### 专业布局
- 顶部：串口连接控制
- 中上：测试命令按钮
- 中间：6个大字体数据卡片
- 下方：图表和数据列表

### 颜色编码
- **蓝色**: 电压相关
- **绿色**: SEI电阻
- **紫色**: CT电阻  
- **红色**: 欧姆电阻
- **橙色**: 统计信息
- **灰色**: 状态信息

### 字体设计
- **大字体**: 24号粗体数值显示
- **中字体**: 12号单位标签
- **小字体**: 10号数据表格

## 🔍 故障排除

### 常见问题

1. **COM5 未显示**
   - 检查设备连接
   - 点击"刷新"按钮
   - 应用程序会自动添加COM5选项

2. **连接失败**
   - 确认设备已连接
   - 检查波特率设置
   - 尝试重新插拔设备

3. **数据不更新**
   - 检查串口连接状态
   - 确认设备正常工作
   - 尝试发送复位命令

4. **图表不显示**
   - 应用程序会自动使用文本图表
   - 安装matplotlib获得完整图表功能
   - 命令：`pip install matplotlib`

## 📞 技术支持

如有问题，请检查：
1. 设备连接状态
2. 串口设置正确
3. 应用程序日志信息
4. 系统兼容性

---

**版本**: 完整多参数版  
**更新日期**: 2024年  
**适用设备**: LVER 测量设备  
**串口**: COM5 @ 115200
