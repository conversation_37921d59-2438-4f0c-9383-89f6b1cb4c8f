"""
GUI主界面模块
使用tkinter创建用户界面
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
from datetime import datetime
from typing import Optional, List, Tuple

# 导入原有模块
from serial_communication import SerialCommunication
from device_protocol import DeviceProtocol

# 导入新增模块
from enhanced_serial import EnhancedSerialManager
from data_manager import DataManager, ContinuousTestManager
from realtime_display import RealtimeDisplayWidget

# 尝试导入图表组件
try:
    from chart_widget import ChartWidget, SimpleChartWidget
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    # 如果导入失败，创建一个简单的替代组件
    MATPLOTLIB_AVAILABLE = False
    class SimpleChartWidget:
        def __init__(self, parent, data_manager):
            self.parent = parent
            self.data_manager = data_manager
            import tkinter as tk
            from tkinter import ttk

            # 创建图表框架
            self.chart_frame = ttk.LabelFrame(parent, text="图表显示 (matplotlib不可用)", padding="10")

            # 创建内容区域
            content_frame = ttk.Frame(self.chart_frame)
            content_frame.pack(fill=tk.BOTH, expand=True)

            # 显示提示信息
            info_label = tk.Label(
                content_frame,
                text="请安装matplotlib以启用完整图表功能\n\n当前显示数据统计信息：",
                font=("Arial", 12),
                fg="blue"
            )
            info_label.pack(pady=10)

            # 创建统计信息显示区域
            self.stats_text = tk.Text(
                content_frame,
                height=15,
                width=60,
                font=("Consolas", 10),
                bg="white",
                fg="black",
                state=tk.DISABLED
            )
            self.stats_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 添加滚动条
            scrollbar = ttk.Scrollbar(content_frame, orient="vertical", command=self.stats_text.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            self.stats_text.config(yscrollcommand=scrollbar.set)

        def pack(self, **kwargs):
            """打包图表框架"""
            self.chart_frame.pack(**kwargs)

        def update_chart(self):
            """更新统计信息显示"""
            try:
                stats = self.data_manager.get_statistics()
                latest = self.data_manager.get_latest_data()

                self.stats_text.config(state=tk.NORMAL)
                self.stats_text.delete(1.0, tk.END)

                content = f"""数据统计信息 (实时更新)
{'='*40}
总测试次数: {stats['total_tests']}

电压统计:
  平均值: {stats['voltage_avg']/1000:.3f} V ({stats['voltage_avg']:.0f} mV)
  最小值: {stats['voltage_min']/1000:.3f} V ({stats['voltage_min']:.0f} mV)
  最大值: {stats['voltage_max']/1000:.3f} V ({stats['voltage_max']:.0f} mV)

电阻统计:
  平均电阻: {stats['r_ohm_avg']:.3f} μΩ
  平均SEI电阻: {stats['r_sei_avg']:.3f} μΩ
  平均CT电阻: {stats['r_ct_avg']:.3f} μΩ
"""

                if latest:
                    content += f"""
最新测试数据:
  时间: {latest.timestamp.strftime('%H:%M:%S')}
  电压: {latest.voltage/1000:.3f} V ({latest.voltage:.0f} mV)
  电阻: {latest.r_ohm:.3f} μΩ
  SEI电阻: {latest.r_sei:.3f} μΩ
  CT电阻: {latest.r_ct:.3f} μΩ

提示: 安装matplotlib后可查看数据曲线图
命令: pip install matplotlib
"""
                else:
                    content += "\n暂无测试数据，请先进行测试"

                self.stats_text.insert(1.0, content)
                self.stats_text.config(state=tk.DISABLED)

            except Exception as e:
                self.stats_text.config(state=tk.NORMAL)
                self.stats_text.delete(1.0, tk.END)
                self.stats_text.insert(1.0, f"统计信息更新失败: {str(e)}")
                self.stats_text.config(state=tk.DISABLED)


class LVERApp:
    """LVER应用程序主界面类"""

    def __init__(self, root):
        self.root = root
        self.root.title("LVER 串口通信工具 - 增强版")
        self.root.geometry("1400x1000")  # 增大窗口尺寸
        self.root.resizable(True, True)

        # 初始化核心组件
        self.serial_comm = SerialCommunication()
        self.protocol = DeviceProtocol()
        self.enhanced_serial = EnhancedSerialManager()
        self.data_manager = DataManager()
        self.continuous_test_manager = ContinuousTestManager(self.data_manager)

        # 界面状态变量
        self.is_auto_connecting = False
        self.test_interval_var = tk.DoubleVar(value=2.0)

        # 创建界面
        self.create_enhanced_widgets()
        self.setup_port_monitoring()
        self.update_status()

        # 定期更新状态
        self.root.after(1000, self.periodic_update)

        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_enhanced_widgets(self):
        """创建增强版界面组件"""
        # 创建主容器 - 优化布局比例，给图表更多空间
        main_container = ttk.PanedWindow(self.root, orient=tk.VERTICAL)
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 上部分：控制区域 (减少权重)
        top_frame = ttk.Frame(main_container)
        main_container.add(top_frame, weight=1)

        # 下部分：图表区域 (大幅增加权重，让图表占据更多空间)
        bottom_frame = ttk.Frame(main_container)
        main_container.add(bottom_frame, weight=5)  # 从3增加到5，图表区域占83%

        # 创建上部分的左右分割
        top_paned = ttk.PanedWindow(top_frame, orient=tk.HORIZONTAL)
        top_paned.pack(fill=tk.BOTH, expand=True)

        # 左侧：连接和控制
        left_frame = ttk.Frame(top_paned)
        top_paned.add(left_frame, weight=1)

        # 右侧：实时数据显示
        right_frame = ttk.Frame(top_paned)
        top_paned.add(right_frame, weight=1)

        # 创建各个区域
        self.create_connection_section(left_frame)
        self.create_test_control_section(left_frame)
        self.create_enhanced_data_display_section(right_frame)
        self.create_chart_section(bottom_frame)

    def create_connection_section(self, parent):
        """创建连接控制区域"""
        # 串口连接区域
        conn_frame = ttk.LabelFrame(parent, text="串口连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 10))

        # 串口选择
        port_frame = ttk.Frame(conn_frame)
        port_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(port_frame, text="串口:").pack(side=tk.LEFT)
        self.port_var = tk.StringVar()
        self.port_combo = ttk.Combobox(port_frame, textvariable=self.port_var, width=20)
        self.port_combo.pack(side=tk.LEFT, padx=(5, 10))

        # 刷新按钮
        ttk.Button(port_frame, text="刷新", command=self.refresh_ports).pack(side=tk.LEFT)

        # 波特率选择
        baud_frame = ttk.Frame(conn_frame)
        baud_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(baud_frame, text="波特率:").pack(side=tk.LEFT)
        self.baudrate_var = tk.StringVar(value="115200")
        self.baudrate_combo = ttk.Combobox(
            baud_frame, textvariable=self.baudrate_var,
            values=["115200", "9600", "38400", "57600", "19200", "4800"],
            width=15
        )
        self.baudrate_combo.pack(side=tk.LEFT, padx=(5, 0))

        # 连接按钮区域
        button_frame = ttk.Frame(conn_frame)
        button_frame.pack(fill=tk.X, pady=(5, 0))

        self.connect_btn = ttk.Button(
            button_frame, text="连接",
            command=self.toggle_connection
        )
        self.connect_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.auto_connect_btn = ttk.Button(
            button_frame, text="自动连接",
            command=self.auto_connect
        )
        self.auto_connect_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 连接状态
        self.status_label = ttk.Label(conn_frame, text="未连接", foreground="red")
        self.status_label.pack(pady=(5, 0))

    def create_test_control_section(self, parent):
        """创建测试控制区域"""
        # 测试控制区域
        test_frame = ttk.LabelFrame(parent, text="测试控制", padding="10")
        test_frame.pack(fill=tk.X, pady=(0, 10))

        # 单次测试按钮
        single_test_frame = ttk.Frame(test_frame)
        single_test_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(
            single_test_frame, text="测试命令 (0xAA)",
            command=self.send_test_command
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            single_test_frame, text="电压测量 (0x55)",
            command=self.send_voltage_command
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            single_test_frame, text="重置 (0xA0)",
            command=self.send_reset_command
        ).pack(side=tk.LEFT, padx=(0, 5))

        # 连续测试控制
        continuous_frame = ttk.LabelFrame(test_frame, text="连续测试", padding="5")
        continuous_frame.pack(fill=tk.X, pady=(0, 5))

        # 测试间隔设置
        interval_frame = ttk.Frame(continuous_frame)
        interval_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(interval_frame, text="间隔(秒):").pack(side=tk.LEFT)
        interval_spinbox = ttk.Spinbox(
            interval_frame, from_=0.5, to=60.0, increment=0.5,
            textvariable=self.test_interval_var, width=8
        )
        interval_spinbox.pack(side=tk.LEFT, padx=(5, 0))

        # 连续测试按钮
        test_btn_frame = ttk.Frame(continuous_frame)
        test_btn_frame.pack(fill=tk.X, pady=(5, 0))

        self.start_test_btn = ttk.Button(
            test_btn_frame, text="开始连续测试",
            command=self.start_continuous_test
        )
        self.start_test_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.stop_test_btn = ttk.Button(
            test_btn_frame, text="停止测试",
            command=self.stop_continuous_test,
            state=tk.DISABLED
        )
        self.stop_test_btn.pack(side=tk.LEFT, padx=(0, 5))

        # 数据操作
        data_frame = ttk.LabelFrame(test_frame, text="数据操作", padding="5")
        data_frame.pack(fill=tk.X)

        ttk.Button(
            data_frame, text="导出数据",
            command=self.export_data
        ).pack(side=tk.LEFT, padx=(0, 5))

        ttk.Button(
            data_frame, text="清空数据",
            command=self.clear_data
        ).pack(side=tk.LEFT, padx=(0, 5))

    def create_enhanced_data_display_section(self, parent):
        """创建增强的实时数据显示区域"""
        # 创建垂直分割的面板
        data_paned = ttk.PanedWindow(parent, orient=tk.VERTICAL)
        data_paned.pack(fill=tk.BOTH, expand=True)

        # 上部：实时数值显示
        self.realtime_display = RealtimeDisplayWidget(data_paned, self.data_manager)
        data_paned.add(self.realtime_display.main_frame, weight=2)

        # 下部：操作日志
        log_frame = ttk.LabelFrame(data_paned, text="操作日志", padding="5")
        data_paned.add(log_frame, weight=1)

        self.log_text = scrolledtext.ScrolledText(
            log_frame, height=6, width=50,
            font=("Consolas", 9)
        )
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 配置日志文本标签
        self.log_text.tag_configure("timestamp", foreground="gray")
        self.log_text.tag_configure("command", foreground="blue", font=("Consolas", 9, "bold"))
        self.log_text.tag_configure("success", foreground="green")
        self.log_text.tag_configure("error", foreground="red")
        self.log_text.tag_configure("data", foreground="black")

    def create_chart_section(self, parent):
        """创建图表区域 - 增强版，支持更大显示区域和导出功能"""
        try:
            if MATPLOTLIB_AVAILABLE:
                self.chart_widget = ChartWidget(parent, self.data_manager)
                self.log_message("图表组件已加载 (增强版 - 支持导出功能)")
            else:
                self.chart_widget = SimpleChartWidget(parent, self.data_manager)
                self.log_message("图表组件已加载 (简化版)")

            self.chart_widget.pack(fill=tk.BOTH, expand=True)

        except Exception as e:
            # 如果图表创建失败，创建一个简单的替代组件
            self.log_message(f"图表组件加载失败: {str(e)}")

            fallback_frame = ttk.LabelFrame(parent, text="图表显示区域", padding="10")
            fallback_frame.pack(fill=tk.BOTH, expand=True)

            ttk.Label(
                fallback_frame,
                text=f"图表组件加载失败: {e}\n请安装 matplotlib 以启用图表功能",
                justify=tk.CENTER
            ).pack(expand=True)

            # 创建一个简单的替代组件，确保有update_chart方法
            class FallbackChartWidget:
                def __init__(self):
                    pass
                def update_chart(self):
                    pass
                def clear_chart_data(self):
                    pass

            self.chart_widget = FallbackChartWidget()

    # 新增功能方法
    def setup_port_monitoring(self):
        """设置串口监控"""
        self.enhanced_serial.start_port_monitoring(self.on_port_list_changed)
        self.refresh_ports()

    def on_port_list_changed(self, port_list):
        """串口列表变化回调"""
        def update_ui():
            try:
                current_values = [f"{port} - {desc}" for port, desc in port_list]
                self.port_combo['values'] = current_values

                # 如果当前选择的端口不在列表中，清空选择
                current_selection = self.port_var.get()
                if current_selection and current_selection not in current_values:
                    self.port_var.set("")
                    self.log_message("检测到串口设备变化")
            except Exception as e:
                # 忽略UI更新错误，避免影响主程序
                pass

        # 安全地在主线程中更新UI
        try:
            self.root.after(0, update_ui)
        except Exception as e:
            # 如果主循环还没开始，直接更新
            try:
                update_ui()
            except:
                pass

    def refresh_ports(self):
        """刷新串口列表"""
        port_list = self.enhanced_serial.get_available_ports()
        self.on_port_list_changed(port_list)

    def auto_connect(self):
        """自动连接"""
        if self.is_auto_connecting:
            return

        self.is_auto_connecting = True
        self.auto_connect_btn.config(state=tk.DISABLED)

        def auto_connect_worker():
            try:
                self.log_message("开始自动连接...")

                # 获取首选端口
                preferred_port = None
                current_selection = self.port_var.get()
                if current_selection and " - " in current_selection:
                    preferred_port = current_selection.split(" - ")[0]

                # 尝试自动连接
                if self.enhanced_serial.auto_connect(preferred_port):
                    # 连接成功，更新UI
                    conn_info = self.enhanced_serial.get_connection_info()
                    if conn_info:
                        port, baudrate = conn_info
                        self.root.after(0, lambda: self.on_auto_connect_success(port, baudrate))
                else:
                    self.root.after(0, self.on_auto_connect_failed)

            except Exception as e:
                self.root.after(0, lambda: self.on_auto_connect_failed(str(e)))
            finally:
                self.is_auto_connecting = False
                self.root.after(0, lambda: self.auto_connect_btn.config(state=tk.NORMAL))

        threading.Thread(target=auto_connect_worker, daemon=True).start()

    def on_auto_connect_success(self, port, baudrate):
        """自动连接成功回调"""
        # 更新串口通信对象
        self.serial_comm.serial_port = self.enhanced_serial.serial_port
        self.serial_comm.is_connected = True

        # 更新UI
        self.port_var.set(f"{port} - 自动检测")
        self.baudrate_var.set(str(baudrate))
        self.connect_btn.config(text="断开")
        self.status_label.config(text=f"已连接 {port} ({baudrate})", foreground="green")

        self.log_message(f"自动连接成功: {port} (波特率: {baudrate})")

    def on_auto_connect_failed(self, error_msg=""):
        """自动连接失败回调"""
        msg = f"自动连接失败"
        if error_msg:
            msg += f": {error_msg}"
        self.log_message(msg)

    def toggle_connection(self):
        """切换连接状态"""
        if self.enhanced_serial.is_connected:
            self.disconnect()
        else:
            self.connect()

    def connect(self):
        """连接串口"""
        current_selection = self.port_var.get()
        if not current_selection:
            messagebox.showwarning("警告", "请选择串口")
            return

        if " - " in current_selection:
            port = current_selection.split(" - ")[0]
        else:
            port = current_selection

        try:
            baudrate = int(self.baudrate_var.get())
        except ValueError:
            messagebox.showerror("错误", "无效的波特率")
            return

        if self.enhanced_serial.connect(port, baudrate):
            # 更新串口通信对象
            self.serial_comm.serial_port = self.enhanced_serial.serial_port
            self.serial_comm.is_connected = True

            # 更新UI
            self.connect_btn.config(text="断开")
            self.status_label.config(text=f"已连接 {port} ({baudrate})", foreground="green")
            self.log_message(f"成功连接到 {port} (波特率: {baudrate})")
        else:
            messagebox.showerror("错误", "连接失败")

    def disconnect(self):
        """断开连接"""
        # 停止连续测试
        if self.continuous_test_manager.is_running:
            self.stop_continuous_test()

        # 断开连接
        self.enhanced_serial.disconnect()
        self.serial_comm.is_connected = False
        self.serial_comm.serial_port = None

        # 更新UI
        self.connect_btn.config(text="连接")
        self.status_label.config(text="未连接", foreground="red")
        self.log_message("已断开连接")

    def start_continuous_test(self):
        """开始连续测试"""
        if not self.enhanced_serial.is_connected:
            messagebox.showwarning("警告", "请先连接串口")
            return

        interval = self.test_interval_var.get()
        self.continuous_test_manager.start_continuous_test(
            self.execute_single_test, interval
        )

        # 更新UI
        self.start_test_btn.config(state=tk.DISABLED)
        self.stop_test_btn.config(state=tk.NORMAL)

        self.log_message(f"开始连续测试，间隔: {interval}秒")

    def stop_continuous_test(self):
        """停止连续测试"""
        self.continuous_test_manager.stop_continuous_test()

        # 更新UI
        self.start_test_btn.config(state=tk.NORMAL)
        self.stop_test_btn.config(state=tk.DISABLED)

        self.log_message("连续测试已停止")

    def execute_single_test(self):
        """执行单次测试（用于连续测试）"""
        try:
            # 发送测试命令
            responses = self.serial_comm.send_test_command()
            if responses and len(responses) >= 4:
                # 解析数据
                data = self.protocol.parse_test_response(responses)
                if data:
                    # 添加到数据管理器
                    test_data = self.data_manager.add_test_data(
                        data['voltage'], data['r_ohm'],
                        data['r_sei'], data['r_ct']
                    )

                    # 更新UI（在主线程中）
                    self.root.after(0, lambda: self.update_data_display(test_data))
                    if hasattr(self, 'chart_widget') and self.chart_widget:
                        self.root.after(0, lambda: self.chart_widget.update_chart())

        except Exception as e:
            self.root.after(0, lambda: self.log_message(f"测试执行错误: {e}"))

    def send_test_command(self):
        """发送测试命令"""
        if not self.enhanced_serial.is_connected:
            messagebox.showwarning("警告", "请先连接串口")
            return

        def test_worker():
            try:
                self.root.after(0, lambda: self.log_message("发送测试命令 (0xAA)..."))

                responses = self.serial_comm.send_test_command()
                if responses:
                    data = self.protocol.parse_test_response(responses)
                    if data:
                        # 添加到数据管理器
                        test_data = self.data_manager.add_test_data(
                            data['voltage'], data['r_ohm'],
                            data['r_sei'], data['r_ct']
                        )

                        # 更新UI
                        self.root.after(0, lambda: self.update_data_display(test_data))
                        if hasattr(self, 'chart_widget') and self.chart_widget:
                            self.root.after(0, lambda: self.chart_widget.update_chart())
                        self.root.after(0, lambda: self.log_message("测试命令响应成功"))
                    else:
                        self.root.after(0, lambda: self.log_message("测试命令响应解析失败"))
                else:
                    self.root.after(0, lambda: self.log_message("测试命令超时或失败"))

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"测试命令错误: {e}"))

        threading.Thread(target=test_worker, daemon=True).start()

    def send_voltage_command(self):
        """发送电压测量命令"""
        if not self.enhanced_serial.is_connected:
            messagebox.showwarning("警告", "请先连接串口")
            return

        def voltage_worker():
            try:
                self.root.after(0, lambda: self.log_message("发送电压测量命令 (0x55)..."))

                response = self.serial_comm.send_voltage_command()
                if response:
                    data = self.protocol.parse_voltage_response(response)
                    if data:
                        self.root.after(0, lambda: self.log_message(f"B2电压: {data['voltage']/1000:.3f} V ({data['voltage']:.0f} mV)"))
                    else:
                        self.root.after(0, lambda: self.log_message("电压测量响应解析失败"))
                else:
                    self.root.after(0, lambda: self.log_message("电压测量超时或失败"))

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"电压测量错误: {e}"))

        threading.Thread(target=voltage_worker, daemon=True).start()

    def send_reset_command(self):
        """发送重置命令"""
        if not self.enhanced_serial.is_connected:
            messagebox.showwarning("警告", "请先连接串口")
            return

        def reset_worker():
            try:
                self.root.after(0, lambda: self.log_message("发送重置命令 (0xA0)..."))

                if self.serial_comm.send_reset_command():
                    self.root.after(0, lambda: self.log_message("重置命令发送成功"))
                else:
                    self.root.after(0, lambda: self.log_message("重置命令发送失败"))

            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"重置命令错误: {e}"))

        threading.Thread(target=reset_worker, daemon=True).start()

    def update_data_display(self, test_data):
        """更新数据显示"""
        # 使用新的实时显示组件更新数据
        self.realtime_display.update_data(
            voltage_mv=test_data.voltage,
            r_ohm=test_data.r_ohm,
            r_sei=test_data.r_sei,
            r_ct=test_data.r_ct,
            test_count=test_data.test_number
        )

    def export_data(self):
        """导出数据"""
        if self.data_manager.get_data_count() == 0:
            messagebox.showwarning("警告", "没有数据可导出")
            return

        # 选择保存位置
        filename = filedialog.asksaveasfilename(
            title="导出测试数据",
            defaultextension=".csv",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
            initialname=f"LVER_测试数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        )

        if filename:
            try:
                exported_file = self.data_manager.export_to_csv(filename)
                messagebox.showinfo("成功", f"数据已导出到:\n{exported_file}")
                self.log_message(f"数据已导出: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {e}")
                self.log_message(f"导出失败: {e}")

    def clear_data(self):
        """清空数据"""
        if self.data_manager.get_data_count() == 0:
            messagebox.showinfo("提示", "没有数据需要清空")
            return

        if messagebox.askyesno("确认", "确定要清空所有测试数据吗？"):
            self.data_manager.clear_data()

            # 重置实时显示
            self.realtime_display.clear_data()

            # 更新图表
            if hasattr(self, 'chart_widget') and self.chart_widget:
                self.chart_widget.update_chart()

            self.log_message("测试数据已清空")

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("[%H:%M:%S]")
        full_message = f"{timestamp} {message}\n"

        self.log_text.insert(tk.END, full_message)
        self.log_text.see(tk.END)

        # 限制日志行数
        lines = self.log_text.get("1.0", tk.END).split('\n')
        if len(lines) > 100:
            # 删除前面的行
            self.log_text.delete("1.0", f"{len(lines)-100}.0")

    def on_closing(self):
        """窗口关闭事件"""
        # 停止连续测试
        if self.continuous_test_manager.is_running:
            self.continuous_test_manager.stop_continuous_test()

        # 停止串口监控
        self.enhanced_serial.stop_port_monitoring()

        # 断开连接
        self.enhanced_serial.disconnect()

        # 关闭窗口
        self.root.destroy()
    
    def update_status(self):
        """更新状态显示"""
        # 这个方法现在由新的UI处理，保留以兼容旧代码
        pass

    def periodic_update(self):
        """定期更新"""
        # 更新测试状态显示
        if hasattr(self, 'test_count_display'):
            count = self.data_manager.get_data_count()
            self.test_count_display.config(text=str(count))

        # 更新连续测试状态
        if self.continuous_test_manager.is_running:
            status = self.continuous_test_manager.get_status()
            # 可以在这里添加更多状态更新逻辑

        # 继续定期更新
        self.root.after(1000, self.periodic_update)




def main():
    """主函数"""
    root = tk.Tk()

    # 设置窗口图标（如果有的话）
    try:
        # root.iconbitmap("icon.ico")  # 可以添加图标文件
        pass
    except:
        pass

    # 创建应用程序实例
    app = LVERApp(root)

    # 启动主循环
    root.mainloop()


if __name__ == "__main__":
    main()
