#!/usr/bin/env python3
"""
LVER 串口通信应用程序主入口 - 纯净版本
不包含任何模拟数据，仅支持真实串口连接
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    try:
        import serial
        import serial.tools.list_ports
        print("✓ pyserial 依赖检查通过")
    except ImportError:
        missing_deps.append("pyserial")
        print("✗ pyserial 未安装")
    
    try:
        import matplotlib
        print("✓ matplotlib 依赖检查通过")
    except ImportError:
        missing_deps.append("matplotlib")
        print("✗ matplotlib 未安装")
    
    if missing_deps:
        print(f"\n缺少依赖: {', '.join(missing_deps)}")
        print("请运行以下命令安装:")
        for dep in missing_deps:
            print(f"  pip install {dep}")
        return False
    
    return True

def main():
    """主函数"""
    print("正在启动LVER应用程序 (纯净版本)...")
    print("特性：无模拟数据，仅真实串口连接")
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    try:
        # 导入GUI模块
        from gui import LVERApp
        print("✓ GUI模块导入成功")
        
        # 创建主窗口
        root = tk.Tk()
        root.title("LVER 串口通信工具 - 纯净版")
        root.geometry("1400x1000")
        
        # 设置窗口图标（如果存在）
        try:
            root.iconbitmap("icon.ico")
        except:
            pass
        
        print("✓ 主窗口创建成功")
        
        # 创建应用程序实例
        app = LVERApp(root)
        print("✓ 应用程序实例创建成功")
        
        # 确保数据管理器是空的
        app.data_manager.test_data.clear()
        print("✓ 数据管理器已清空")
        
        # 延迟启动串口监控
        def delayed_setup():
            try:
                app.setup_port_monitoring()
                print("✓ 串口监控启动成功")
                
                # 刷新串口列表
                app.refresh_ports()
                print("✓ 串口列表已刷新")
                
            except Exception as e:
                print(f"串口监控启动失败: {e}")
        
        # 在主循环开始后启动串口监控
        root.after(1000, delayed_setup)
        
        print("\n" + "="*60)
        print("LVER应用程序启动成功！(纯净版)")
        print("功能特性：")
        print("1. ✓ 真实串口连接支持")
        print("2. ✓ 完整图表显示功能")
        print("3. ✓ 无任何模拟数据")
        print("4. ✓ 高清图表导出")
        print("5. ✓ CSV数据导出")
        print("6. ✓ 实时数据监控")
        print("\n使用说明：")
        print("- 连接您的串口设备")
        print("- 选择正确的串口和波特率")
        print("- 点击'连接'建立连接")
        print("- 进行测试查看实时数据")
        print("="*60)
        
        # 启动主循环
        print("启动GUI主循环...")
        root.mainloop()
        
    except ImportError as e:
        error_msg = f"模块导入失败: {e}\n\n请确保所有依赖都已正确安装。"
        print(error_msg)
        messagebox.showerror("导入错误", error_msg)
        
    except Exception as e:
        error_msg = f"应用程序启动失败: {e}"
        print(error_msg)
        import traceback
        traceback.print_exc()
        messagebox.showerror("启动错误", error_msg)

if __name__ == "__main__":
    main()
