#!/usr/bin/env python3
"""
测试纯净启动 - 验证没有模拟数据
"""

import sys
import os
import tkinter as tk

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟serial模块以避免依赖问题
class MockSerial:
    def __init__(self, *args, **kwargs):
        self.port = kwargs.get('port', 'COM1')
        self.baudrate = kwargs.get('baudrate', 9600)
        self.is_open = False
    
    def open(self):
        self.is_open = True
    
    def close(self):
        self.is_open = False
    
    def write(self, data):
        return len(data)
    
    def read(self, size=1):
        return b''
    
    def readline(self):
        return b''
    
    def in_waiting(self):
        return 0

class MockListPorts:
    @staticmethod
    def comports():
        class MockPort:
            def __init__(self, device, description):
                self.device = device
                self.description = description
        
        return [
            MockPort('COM3', 'USB Serial Port (COM3)'),
        ]

# 创建模拟模块
import types
serial_module = types.ModuleType('serial')
serial_module.Serial = MockSerial
serial_module.SerialException = Exception
serial_module.tools = types.ModuleType('tools')
serial_module.tools.list_ports = MockListPorts

# 将模拟模块添加到sys.modules
sys.modules['serial'] = serial_module
sys.modules['serial.tools'] = serial_module.tools
sys.modules['serial.tools.list_ports'] = MockListPorts

def main():
    """测试纯净启动"""
    print("测试纯净启动 - 验证无模拟数据")
    
    try:
        from gui import LVERApp
        print("✓ GUI模块导入成功")
        
        # 创建主窗口
        root = tk.Tk()
        root.title("LVER 测试 - 纯净启动验证")
        root.geometry("1400x1000")
        
        # 创建应用程序实例
        app = LVERApp(root)
        print("✓ 应用程序实例创建成功")
        
        # 验证数据管理器是空的
        data_count = len(app.data_manager.get_all_data())
        print(f"✓ 数据管理器中的数据数量: {data_count}")
        
        if data_count == 0:
            print("✅ 验证通过：没有模拟数据")
        else:
            print("❌ 验证失败：存在模拟数据")
            # 清空数据
            app.data_manager.test_data.clear()
            print("✓ 已清空模拟数据")
        
        # 更新图表显示
        if hasattr(app, 'chart_widget') and app.chart_widget:
            app.chart_widget.update_chart()
            print("✓ 图表更新完成")
        
        print("\n" + "="*50)
        print("纯净启动测试完成！")
        print("应该显示：'暂无测试数据'")
        print("="*50)
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
