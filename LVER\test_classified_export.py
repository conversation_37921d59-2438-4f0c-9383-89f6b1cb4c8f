#!/usr/bin/env python3
"""
测试分类导出功能
验证新的导出功能是否正常工作
"""

import tkinter as tk
from tkinter import messagebox
import random
import time
from datetime import datetime

# 导入主应用程序
import sys
import os
sys.path.append(os.path.dirname(__file__))

try:
    from main_fixed_complete import LVERFixedApp
    print("✓ 成功导入增强版应用程序")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    exit(1)

def test_export_functions():
    """测试导出功能"""
    print("=" * 60)
    print("LVER 分类导出功能测试")
    print("=" * 60)
    
    # 创建测试应用
    root = tk.Tk()
    app = LVERFixedApp(root)
    
    # 生成测试数据
    print("生成测试数据...")
    test_measurements = [
        ("V", random.randint(3200, 3400)),
        ("R_sei", random.randint(270, 290)),
        ("R_ct", random.randint(150, 170)),
        ("R_ohm", random.randint(500, 530)),
        ("V", random.randint(3200, 3400)),
        ("R_sei", random.randint(270, 290)),
        ("R_ct", random.randint(150, 170)),
        ("R_ohm", random.randint(500, 530)),
        ("V", random.randint(3200, 3400)),
        ("R_sei", random.randint(270, 290)),
        ("R_ct", random.randint(150, 170)),
        ("R_ohm", random.randint(500, 530)),
    ]
    
    # 添加测试数据到应用
    for measure_type, value in test_measurements:
        app.add_measurement_data(measure_type, value)
        time.sleep(0.1)  # 模拟测量间隔
    
    print(f"✓ 生成了 {len(test_measurements)} 条测试数据")
    
    # 显示数据统计
    stats = {
        'V': len([d for d in app.test_data if d['type'] == 'V']),
        'R_sei': len([d for d in app.test_data if d['type'] == 'R_sei']),
        'R_ct': len([d for d in app.test_data if d['type'] == 'R_ct']),
        'R_ohm': len([d for d in app.test_data if d['type'] == 'R_ohm'])
    }
    
    print("\n数据统计:")
    print(f"• 电压测量 (V): {stats['V']} 条")
    print(f"• SEI电阻 (R_sei): {stats['R_sei']} 条")
    print(f"• CT电阻 (R_ct): {stats['R_ct']} 条")
    print(f"• 欧姆电阻 (R_ohm): {stats['R_ohm']} 条")
    print(f"• 总计: {len(app.test_data)} 条")
    
    print("\n" + "=" * 60)
    print("导出功能测试")
    print("=" * 60)
    
    # 测试导出功能
    test_options = [
        ("统一导出 (单文件)", "app.export_data_unified()"),
        ("分类导出 (多文件)", "app.export_data_classified()"),
        ("Excel格式导出", "app.export_data_excel()")
    ]
    
    print("可用的导出选项:")
    for i, (name, _) in enumerate(test_options, 1):
        print(f"{i}. {name}")
    
    print("\n请在应用程序中测试导出功能:")
    print("1. 点击 '导出数据 ▼' 按钮")
    print("2. 选择不同的导出选项")
    print("3. 验证导出文件的内容和格式")
    
    # 显示应用程序
    root.deiconify()
    root.lift()
    root.focus_force()
    
    # 显示测试说明
    messagebox.showinfo("导出功能测试", 
                       f"""测试数据已生成完成！

数据统计:
• 电压测量: {stats['V']} 条
• SEI电阻: {stats['R_sei']} 条  
• CT电阻: {stats['R_ct']} 条
• 欧姆电阻: {stats['R_ohm']} 条
• 总计: {len(app.test_data)} 条

请测试以下导出功能:
1. 📄 统一导出 (单文件)
2. 📁 分类导出 (多文件) 
3. 📊 Excel格式导出

点击 '导出数据 ▼' 按钮开始测试！""")
    
    print("✓ 测试应用程序已启动")
    print("✓ 测试数据已加载")
    print("✓ 请在GUI中测试导出功能")
    
    root.mainloop()

def show_export_features():
    """显示导出功能特性"""
    print("\n" + "=" * 60)
    print("分类导出功能特性")
    print("=" * 60)
    
    features = [
        "📄 统一导出 (单文件)",
        "  • 所有数据导出到一个CSV文件",
        "  • 保持原有的导出格式",
        "  • 适合简单的数据查看",
        "",
        "📁 分类导出 (多文件)",
        "  • 每种参数类型独立的CSV文件",
        "  • 包含详细的统计信息",
        "  • 生成数据汇总文件",
        "  • 便于分类分析和处理",
        "",
        "📊 Excel格式导出",
        "  • 多工作表Excel文件",
        "  • 每种参数类型独立工作表",
        "  • 专业的格式和样式",
        "  • 包含汇总工作表",
        "  • 支持Excel图表和分析",
        "",
        "🔧 增强功能",
        "  • 自动生成时间戳文件名",
        "  • 完整的错误处理",
        "  • 导出成功确认",
        "  • 自动打开文件/目录选项",
        "  • UTF-8编码支持中文",
        "  • 详细的导出统计信息"
    ]
    
    for feature in features:
        print(feature)
    
    print("=" * 60)

def main():
    print("LVER 分类导出功能测试工具")
    show_export_features()
    
    # 询问是否开始测试
    root = tk.Tk()
    root.withdraw()
    
    if messagebox.askyesno("开始测试", 
                          "是否要启动LVER应用程序并生成测试数据？\n\n这将创建一个包含多种参数类型的测试数据集，\n然后您可以测试不同的导出功能。"):
        root.destroy()
        test_export_functions()
    else:
        print("用户取消了测试")
        root.destroy()

if __name__ == "__main__":
    main()
