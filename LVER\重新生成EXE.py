#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新生成EXE文件脚本
使用最新的main_fixed_complete.py生成EXE文件
"""

import subprocess
import sys
import os

def build_exe():
    """重新生成EXE文件"""
    print("=" * 70)
    print("🔨 重新生成EXE文件")
    print("=" * 70)
    
    # 检查文件是否存在
    if not os.path.exists('main_fixed_complete.py'):
        print("❌ 错误: main_fixed_complete.py 文件不存在")
        return False
    
    print("✅ 找到源文件: main_fixed_complete.py")
    
    # 清理旧的构建文件
    print("\n🧹 清理旧的构建文件...")
    try:
        if os.path.exists('build'):
            import shutil
            shutil.rmtree('build')
            print("✅ 清理build目录")
        
        if os.path.exists('dist'):
            import shutil
            shutil.rmtree('dist')
            print("✅ 清理dist目录")
            
        # 清理spec文件
        for spec_file in ['LVER_串口通信工具.spec', 'LVER_串口通信工具_v2.1.spec']:
            if os.path.exists(spec_file):
                os.remove(spec_file)
                print(f"✅ 清理{spec_file}")
                
    except Exception as e:
        print(f"⚠️  清理警告: {e}")
    
    # 构建EXE
    print("\n🔨 开始构建EXE文件...")
    
    cmd = [
        'pyinstaller',
        '--onefile',
        '--windowed',
        '--name=鲸测云LCER电池测试仪_v3.0',
        '--hidden-import=tkinter',
        '--hidden-import=matplotlib',
        '--hidden-import=serial',
        '--hidden-import=openpyxl',
        '--hidden-import=matplotlib.backends.backend_tkagg',
        '--hidden-import=PIL',
        'main_fixed_complete.py'
    ]
    
    try:
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ EXE文件构建成功！")
            
            # 检查生成的文件
            exe_path = 'dist/鲸测云LCER电池测试仪_v3.0.exe'
            if os.path.exists(exe_path):
                size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📁 生成文件: {exe_path}")
                print(f"📏 文件大小: {size:.1f} MB")
                
                print("\n🎉 EXE文件重新生成完成！")
                print("✅ 包含最新的品牌更新和图标移除")
                print("✅ 所有LVER文本已更新为鲸测云LCER")
                print("✅ 所有羽毛图标已移除")
                
                return True
            else:
                print("❌ 错误: EXE文件未生成")
                return False
        else:
            print("❌ 构建失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建错误: {e}")
        return False

if __name__ == "__main__":
    success = build_exe()
    if success:
        print("\n🚀 请运行新生成的EXE文件:")
        print("   dist/鲸测云LCER电池测试仪_v3.0.exe")
    else:
        print("\n❌ 构建失败，请检查错误信息")
