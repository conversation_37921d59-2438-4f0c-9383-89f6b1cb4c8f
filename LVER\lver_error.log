2025-06-28 11:05:07,498 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 11:05:48,761 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 11:05:48,958 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 11:05:50,756 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:05:51,285 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,514', '1,R_sei,308', '1,R_ct,104']
2025-06-28 11:06:35,194 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:06:35,788 - LVER - DEBUG - 测试命令响应: ['1,V,1789', '1,R_ohm,1065500', '1,R_sei,0', '1,R_ct,0']
2025-06-28 11:06:47,562 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:06:48,091 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,514', '1,R_sei,283', '1,R_ct,45']
2025-06-28 11:07:03,004 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:07:05,007 - LVER - ERROR - [通信超时] 操作超时: 电压测量命令 (超时时间: 2.0秒)
2025-06-28 11:07:08,998 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:07:11,001 - LVER - ERROR - [通信超时] 操作超时: 电压测量命令 (超时时间: 2.0秒)
2025-06-28 11:07:21,698 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:07:22,226 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,506', '1,R_sei,308', '1,R_ct,87']
2025-06-28 11:07:25,641 - LVER - DEBUG - 发送重置命令 0xA0
2025-06-28 11:07:25,642 - LVER - INFO - 重置命令发送成功
2025-06-28 11:07:29,065 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:07:31,068 - LVER - ERROR - [通信超时] 操作超时: 电压测量命令 (超时时间: 2.0秒)
2025-06-28 11:07:33,426 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:07:33,946 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,514', '1,R_sei,291', '1,R_ct,112']
2025-06-28 11:07:59,386 - LVER - INFO - 串口连接已断开
2025-06-28 11:08:02,616 - LVER - INFO - 尝试连接串口 COM5, 波特率: 38400
2025-06-28 11:08:02,677 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 11:08:05,230 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:08:07,234 - LVER - ERROR - [通信超时] 操作超时: 测试命令响应 (超时时间: 2.0秒)
2025-06-28 11:08:10,052 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:08:12,053 - LVER - ERROR - [通信超时] 操作超时: 电压测量命令 (超时时间: 2.0秒)
2025-06-28 11:08:13,235 - LVER - DEBUG - 发送重置命令 0xA0
2025-06-28 11:08:13,236 - LVER - INFO - 重置命令发送成功
2025-06-28 11:08:25,875 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:08:27,887 - LVER - ERROR - [通信超时] 操作超时: 测试命令响应 (超时时间: 2.0秒)
2025-06-28 11:08:31,035 - LVER - INFO - 串口连接已断开
2025-06-28 11:08:31,992 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 11:08:32,053 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 11:08:33,412 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:08:33,931 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,527', '1,R_sei,270', '1,R_ct,87']
2025-06-28 11:12:25,794 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:12:27,798 - LVER - ERROR - [通信超时] 操作超时: 电压测量命令 (超时时间: 2.0秒)
2025-06-28 11:12:41,970 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:12:42,488 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,514', '1,R_sei,304', '1,R_ct,104']
2025-06-28 11:12:45,746 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:12:46,269 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,522', '1,R_sei,291', '1,R_ct,108']
2025-06-28 11:12:51,416 - LVER - INFO - 串口连接已断开
2025-06-28 11:12:52,951 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 11:12:53,012 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 11:12:54,284 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:12:54,809 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,295', '1,R_ct,112']
2025-06-28 11:12:59,588 - LVER - DEBUG - 发送重置命令 0xA0
2025-06-28 11:12:59,589 - LVER - INFO - 重置命令发送成功
2025-06-28 11:13:00,514 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:13:01,035 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,291', '1,R_ct,79']
2025-06-28 11:13:10,997 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:13:13,005 - LVER - ERROR - [通信超时] 操作超时: 电压测量命令 (超时时间: 2.0秒)
2025-06-28 11:13:15,688 - LVER - INFO - 串口连接已断开
2025-06-28 11:13:38,526 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 11:13:43,238 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 11:13:43,432 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 11:13:44,626 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:13:45,155 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,527', '1,R_sei,295', '1,R_ct,129']
2025-06-28 11:13:53,776 - LVER - DEBUG - 发送重置命令 0xA0
2025-06-28 11:13:53,776 - LVER - INFO - 重置命令发送成功
2025-06-28 11:13:55,047 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:13:59,152 - LVER - ERROR - [通信超时] 操作超时: 电压测量命令 (超时时间: 2.0秒)
2025-06-28 11:16:13,980 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 11:16:22,165 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 11:16:22,362 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 11:16:25,168 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:16:25,169 - LVER - DEBUG - 发送命令: 55
2025-06-28 11:16:25,169 - LVER - DEBUG - 已发送 1 字节
2025-06-28 11:16:25,369 - LVER - DEBUG - 开始接收响应，超时时间: 6.0秒
2025-06-28 11:16:31,408 - LVER - DEBUG - 未接收到任何响应数据
2025-06-28 11:16:31,409 - LVER - ERROR - [通信超时] 操作超时: 电压测量命令 (超时时间: 2.0秒)
2025-06-28 11:16:35,022 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:16:35,543 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,506', '1,R_sei,295', '1,R_ct,104']
2025-06-28 11:18:52,524 - LVER - INFO - 串口连接已断开
2025-06-28 11:40:08,268 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 11:40:08,329 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 11:40:13,733 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:40:14,154 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 第1行: 1,V,数值, 收到: Start Voltage : 5075...
2025-06-28 11:40:16,309 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:40:16,723 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 第1行: 1,V,数值, 收到: Start Voltage : 5075...
2025-06-28 11:40:20,062 - LVER - INFO - 串口连接已断开
2025-06-28 11:40:20,876 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 11:40:20,937 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 11:40:22,048 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:40:22,470 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 4行CSV数据, 收到: 8行数据...
2025-06-28 11:40:28,304 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:40:28,722 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 4行CSV数据, 收到: 7行数据...
2025-06-28 11:40:31,269 - LVER - DEBUG - 发送重置命令 0xA0
2025-06-28 11:40:31,270 - LVER - INFO - 重置命令发送成功
2025-06-28 11:40:32,533 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:40:32,677 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 第1行: 1,V,数值, 收到: Start Voltage : 5075...
2025-06-28 11:40:34,087 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:40:34,088 - LVER - DEBUG - 发送命令: 55
2025-06-28 11:40:34,089 - LVER - DEBUG - 已发送 1 字节
2025-06-28 11:40:34,289 - LVER - DEBUG - 开始接收响应，超时时间: 6.0秒
2025-06-28 11:40:34,290 - LVER - DEBUG - 接收到数据: 525f6f686d2041646a757374312048465f63757272656e743a313030206d4120525f6f686d5f74656d703a323420525f7365695f74656d703a3820525f6f686d5f4164645f525f7365693a33320d0a -> 'R_ohm Adjust1 HF_current:100 mA R_ohm_temp:24 R_sei_temp:8 R_ohm_Add_R_sei:32

'
2025-06-28 11:40:34,391 - LVER - DEBUG - 接收到数据: 525f6f686d2041646a757374312048465f63757272656e743a313030206d4120525f6f686d5f74656d703a313420525f7365695f74656d703a3820525f6f686d5f4164645f525f7365693a32320d0a -> 'R_ohm Adjust1 HF_current:100 mA R_ohm_temp:14 R_sei_temp:8 R_ohm_Add_R_sei:22

'
2025-06-28 11:40:34,493 - LVER - DEBUG - 接收到数据: 525f6f686d2041646a757374312048465f63757272656e743a313030206d4120525f6f686d5f74656d703a313820525f7365695f74656d703a313220525f6f686d5f4164645f525f7365693a33300d0a -> 'R_ohm Adjust1 HF_current:100 mA R_ohm_temp:18 R_sei_temp:12 R_ohm_Add_R_sei:30

'
2025-06-28 11:40:34,694 - LVER - DEBUG - 接收到数据: 525f6f686d2041646a757374312048465f63757272656e743a313030206d4120525f6f686d5f74656d703a313620525f7365695f74656d703a3220525f6f686d5f4164645f525f7365693a31380d0a -> 'R_ohm Adjust1 HF_current:100 mA R_ohm_temp:16 R_sei_temp:2 R_ohm_Add_R_sei:18

'
2025-06-28 11:40:34,795 - LVER - DEBUG - 接收到数据: 525f6f686d2041646a757374312048465f63757272656e743a31323530206d4120525f6f686d5f74656d703a363420525f7365695f74656d703a333020525f6f686d5f4164645f525f7365693a39340d0a -> 'R_ohm Adjust1 HF_current:1250 mA R_ohm_temp:64 R_sei_temp:30 R_ohm_Add_R_sei:94

'
2025-06-28 11:40:34,997 - LVER - DEBUG - 接收到数据: 525f6f686d2041646a757374312048465f63757272656e743a31323530206d4120525f6f686d5f74656d703a363220525f7365695f74656d703a333420525f6f686d5f4164645f525f7365693a39360d0a -> 'R_ohm Adjust1 HF_current:1250 mA R_ohm_temp:62 R_sei_temp:34 R_ohm_Add_R_sei:96

'
2025-06-28 11:40:35,098 - LVER - DEBUG - 接收到数据: 525f6f686d2041646a757374312048465f63757272656e743a31323530206d4120525f6f686d5f74656d703a363620525f7365695f74656d703a333220525f6f686d5f4164645f525f7365693a39380d0a -> 'R_ohm Adjust1 HF_current:1250 mA R_ohm_temp:66 R_sei_temp:32 R_ohm_Add_R_sei:98

'
2025-06-28 11:40:35,199 - LVER - DEBUG - 接收到数据: 525f6f686d2041646a757374312048465f63757272656e743a31323530206d4120525f6f686d5f74656d703a373020525f7365695f74656d703a343020525f6f686d5f4164645f525f7365693a3131300d0a -> 'R_ohm Adjust1 HF_current:1250 mA R_ohm_temp:70 R_sei_temp:40 R_ohm_Add_R_sei:110

'
2025-06-28 11:40:35,401 - LVER - DEBUG - 接收到数据: 525f6f686d2041646a757374312048465f63757272656e743a31323530206d4120525f6f686d5f74656d703a363620525f7365695f74656d703a333620525f6f686d5f4164645f525f7365693a3130320d0a -> 'R_ohm Adjust1 HF_current:1250 mA R_ohm_temp:66 R_sei_temp:36 R_ohm_Add_R_sei:102

'
2025-06-28 11:40:35,503 - LVER - DEBUG - 接收到数据: 525f6f686d2041646a757374312048465f63757272656e743a35343936206d4120525f6f686d5f74656d703a32383220525f7365695f74656d703a32303020525f6f686d5f4164645f525f7365693a3438320d0a48462041646a7573746d656e742043757272656e743a20353439366d41200d0a -> 'R_ohm Adjust1 HF_current:5496 mA R_ohm_temp:282 R_sei_temp:200 R_ohm_Add_R_sei:482

HF Adjustment Current: 5496mA 

'
2025-06-28 11:40:35,568 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:40:35,568 - LVER - DEBUG - 发送命令: 55
2025-06-28 11:40:35,568 - LVER - DEBUG - 已发送 1 字节
2025-06-28 11:40:35,769 - LVER - DEBUG - 开始接收响应，超时时间: 6.0秒
2025-06-28 11:40:35,770 - LVER - DEBUG - 接收到数据: 525f63742041646a75737420525f63745f63757272656e743a31333734206d4120525f63745f74656d703a2031360d0a525f63745f63757272656e743a20313337346d410d0a0d0a312c562c353037350d0a312c525f6f686d2c3533340d0a312c525f7365692c3337390d0a312c525f63 -> 'R_ct Adjust R_ct_current:1374 mA R_ct_temp: 16

R_ct_current: 1374mA



1,V,5075

1,R_ohm,534

1,R_sei,379

1,R_c'
2025-06-28 11:40:35,770 - LVER - DEBUG - 找到有效CSV行: '1,V,5075'
2025-06-28 11:40:35,770 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 1,B2_Voltage,数值, 收到: 1,V,5075...
2025-06-28 11:40:35,770 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 1,B2_Voltage,数值, 收到: 1,V,5075...
2025-06-28 11:40:35,771 - LVER - DEBUG - 原始响应: '1,V,5075'
2025-06-28 11:40:35,806 - LVER - DEBUG - 接收到数据: 742c3132310d0a -> 't,121

'
2025-06-28 11:40:35,806 - LVER - DEBUG - 找到有效CSV行: 't,121'
2025-06-28 11:40:35,806 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 1,B2_Voltage,数值, 收到: t,121...
2025-06-28 11:40:35,806 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 1,B2_Voltage,数值, 收到: t,121...
2025-06-28 11:40:35,807 - LVER - DEBUG - 原始响应: 't,121'
2025-06-28 11:40:37,776 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:40:38,197 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 第1行: 1,V,数值, 收到: Start Voltage : 5075...
2025-06-28 11:40:49,789 - LVER - INFO - 串口连接已断开
2025-06-28 11:43:36,995 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 11:43:37,091 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 11:43:43,636 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:43:44,057 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 4行CSV数据, 收到: 7行数据...
2025-06-28 11:44:30,597 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 11:45:13,298 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 11:45:13,396 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 11:45:15,232 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:45:15,638 - LVER - DEBUG - 收集CSV行: '1,V,5075'
2025-06-28 11:45:15,689 - LVER - DEBUG - 收集CSV行: '1,R_ohm,542'
2025-06-28 11:45:15,689 - LVER - DEBUG - 收集CSV行: '1,R_sei,367'
2025-06-28 11:45:15,689 - LVER - DEBUG - 收集CSV行: '1,R_ct,140'
2025-06-28 11:45:15,740 - LVER - DEBUG - 总共接收到 8 行响应
2025-06-28 11:45:15,741 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,5075', '1,R_ohm,542', '1,R_sei,367', '1,R_ct,140']
2025-06-28 11:45:15,741 - LVER - DEBUG - 测试命令响应: ['1,V,5075', '1,R_ohm,542', '1,R_sei,367', '1,R_ct,140']
2025-06-28 11:45:27,374 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:45:27,375 - LVER - DEBUG - 发送命令: 55
2025-06-28 11:45:27,375 - LVER - DEBUG - 已发送 1 字节
2025-06-28 11:45:27,576 - LVER - DEBUG - 开始接收响应，超时时间: 6.0秒
2025-06-28 11:45:27,577 - LVER - DEBUG - 接收到数据: 423220766f6c7461676520746573740d0a312c42325f566f6c746167652c333836300d0a -> 'B2 voltage test

1,B2_Voltage,3860

'
2025-06-28 11:45:27,577 - LVER - DEBUG - 找到CSV行: '1,B2_Voltage,3860'
2025-06-28 11:45:27,577 - LVER - DEBUG - 返回第一个CSV行: '1,B2_Voltage,3860'
2025-06-28 11:45:27,577 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 1,V,数值, 收到: 1,B2_Voltage,3860...
2025-06-28 11:45:27,578 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 1,B2_Voltage,数值, 收到: 1,B2_Voltage,3860...
2025-06-28 11:45:27,578 - LVER - DEBUG - 原始响应: '1,B2_Voltage,3860'
2025-06-28 11:45:28,947 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:45:28,948 - LVER - DEBUG - 发送命令: 55
2025-06-28 11:45:28,948 - LVER - DEBUG - 已发送 1 字节
2025-06-28 11:45:29,149 - LVER - DEBUG - 开始接收响应，超时时间: 6.0秒
2025-06-28 11:45:29,149 - LVER - DEBUG - 接收到数据: 423220766f6c7461676520746573740d0a312c42325f566f6c746167652c333835300d0a -> 'B2 voltage test

1,B2_Voltage,3850

'
2025-06-28 11:45:29,149 - LVER - DEBUG - 找到CSV行: '1,B2_Voltage,3850'
2025-06-28 11:45:29,149 - LVER - DEBUG - 返回第一个CSV行: '1,B2_Voltage,3850'
2025-06-28 11:45:29,149 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 1,V,数值, 收到: 1,B2_Voltage,3850...
2025-06-28 11:45:29,150 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 1,B2_Voltage,数值, 收到: 1,B2_Voltage,3850...
2025-06-28 11:45:29,150 - LVER - DEBUG - 原始响应: '1,B2_Voltage,3850'
2025-06-28 11:45:30,131 - LVER - DEBUG - 发送重置命令 0xA0
2025-06-28 11:45:30,132 - LVER - INFO - 重置命令发送成功
2025-06-28 11:45:31,804 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:45:35,053 - LVER - DEBUG - 收集CSV行: '1,V,5075'
2025-06-28 11:45:35,053 - LVER - DEBUG - 收集CSV行: '1,R_ohm,561'
2025-06-28 11:45:35,053 - LVER - DEBUG - 收集CSV行: '1,R_sei,344'
2025-06-28 11:45:35,053 - LVER - DEBUG - 收集CSV行: '1,R_ct,90'
2025-06-28 11:45:35,103 - LVER - DEBUG - 总共接收到 31 行响应
2025-06-28 11:45:35,103 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,5075', '1,R_ohm,561', '1,R_sei,344', '1,R_ct,90']
2025-06-28 11:45:35,104 - LVER - DEBUG - 测试命令响应: ['1,V,5075', '1,R_ohm,561', '1,R_sei,344', '1,R_ct,90']
2025-06-28 11:45:48,390 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:45:48,391 - LVER - DEBUG - 发送命令: 55
2025-06-28 11:45:48,391 - LVER - DEBUG - 已发送 1 字节
2025-06-28 11:45:48,593 - LVER - DEBUG - 开始接收响应，超时时间: 6.0秒
2025-06-28 11:45:48,593 - LVER - DEBUG - 接收到数据: 423220766f6c7461676520746573740d0a312c42325f566f6c746167652c333737370d0a -> 'B2 voltage test

1,B2_Voltage,3777

'
2025-06-28 11:45:48,593 - LVER - DEBUG - 找到CSV行: '1,B2_Voltage,3777'
2025-06-28 11:45:48,594 - LVER - DEBUG - 返回第一个CSV行: '1,B2_Voltage,3777'
2025-06-28 11:45:48,594 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 1,V,数值, 收到: 1,B2_Voltage,3777...
2025-06-28 11:45:48,594 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 1,B2_Voltage,数值, 收到: 1,B2_Voltage,3777...
2025-06-28 11:45:48,595 - LVER - DEBUG - 原始响应: '1,B2_Voltage,3777'
2025-06-28 11:45:51,039 - LVER - DEBUG - 发送重置命令 0xA0
2025-06-28 11:45:51,040 - LVER - INFO - 重置命令发送成功
2025-06-28 11:45:55,262 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:45:55,262 - LVER - DEBUG - 发送命令: 55
2025-06-28 11:45:55,263 - LVER - DEBUG - 已发送 1 字节
2025-06-28 11:45:55,463 - LVER - DEBUG - 开始接收响应，超时时间: 6.0秒
2025-06-28 11:45:55,464 - LVER - DEBUG - 接收到数据: 423220766f6c7461676520746573740d0a312c42325f566f6c746167652c333733380d0a -> 'B2 voltage test

1,B2_Voltage,3738

'
2025-06-28 11:45:55,464 - LVER - DEBUG - 找到CSV行: '1,B2_Voltage,3738'
2025-06-28 11:45:55,464 - LVER - DEBUG - 返回第一个CSV行: '1,B2_Voltage,3738'
2025-06-28 11:45:55,464 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 1,V,数值, 收到: 1,B2_Voltage,3738...
2025-06-28 11:45:55,464 - LVER - ERROR - [无效响应] 响应格式无效 - 期望: 1,B2_Voltage,数值, 收到: 1,B2_Voltage,3738...
2025-06-28 11:45:55,465 - LVER - DEBUG - 原始响应: '1,B2_Voltage,3738'
2025-06-28 11:45:59,630 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:46:02,878 - LVER - DEBUG - 收集CSV行: '1,V,5075'
2025-06-28 11:46:02,878 - LVER - DEBUG - 收集CSV行: '1,R_ohm,561'
2025-06-28 11:46:02,878 - LVER - DEBUG - 收集CSV行: '1,R_sei,344'
2025-06-28 11:46:02,878 - LVER - DEBUG - 收集CSV行: '1,R_ct,60'
2025-06-28 11:46:02,929 - LVER - DEBUG - 总共接收到 31 行响应
2025-06-28 11:46:02,929 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,5075', '1,R_ohm,561', '1,R_sei,344', '1,R_ct,60']
2025-06-28 11:46:02,929 - LVER - DEBUG - 测试命令响应: ['1,V,5075', '1,R_ohm,561', '1,R_sei,344', '1,R_ct,60']
2025-06-28 11:46:37,013 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:46:37,725 - LVER - DEBUG - 收集CSV行: '1,V,5075'
2025-06-28 11:46:37,726 - LVER - DEBUG - 收集CSV行: '1,R_ohm,572'
2025-06-28 11:46:37,726 - LVER - DEBUG - 收集CSV行: '1,R_sei,352'
2025-06-28 11:46:37,726 - LVER - DEBUG - 收集CSV行: '1,R_ct,132'
2025-06-28 11:46:37,776 - LVER - DEBUG - 总共接收到 10 行响应
2025-06-28 11:46:37,776 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,5075', '1,R_ohm,572', '1,R_sei,352', '1,R_ct,132']
2025-06-28 11:46:37,776 - LVER - DEBUG - 测试命令响应: ['1,V,5075', '1,R_ohm,572', '1,R_sei,352', '1,R_ct,132']
2025-06-28 11:46:53,008 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:46:53,718 - LVER - DEBUG - 收集CSV行: '1,V,5075'
2025-06-28 11:46:53,719 - LVER - DEBUG - 收集CSV行: '1,R_ohm,523'
2025-06-28 11:46:53,719 - LVER - DEBUG - 收集CSV行: '1,R_sei,360'
2025-06-28 11:46:53,719 - LVER - DEBUG - 收集CSV行: '1,R_ct,136'
2025-06-28 11:46:53,769 - LVER - DEBUG - 总共接收到 10 行响应
2025-06-28 11:46:53,770 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,5075', '1,R_ohm,523', '1,R_sei,360', '1,R_ct,136']
2025-06-28 11:46:53,770 - LVER - DEBUG - 测试命令响应: ['1,V,5075', '1,R_ohm,523', '1,R_sei,360', '1,R_ct,136']
2025-06-28 11:47:23,442 - LVER - INFO - 串口连接已断开
2025-06-28 11:47:24,769 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 11:47:24,825 - LVER - ERROR - [串口连接错误] 串口访问权限不足: 连接串口 COM5 - could not open port 'COM5': PermissionError(13, '连到系统上的设备没有发挥作用。', None, 31)
Traceback (most recent call last):
  File "D:\code\haha\LVER\serial_communication.py", line 54, in connect
    self.serial_port = serial.Serial(
                       ~~~~~~~~~~~~~^
        port=port,
        ^^^^^^^^^^
    ...<4 lines>...
        timeout=self.timeout
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\serial\serialwin32.py", line 33, in __init__
    super(Serial, self).__init__(*args, **kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\serial\serialutil.py", line 244, in __init__
    self.open()
    ~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\serial\serialwin32.py", line 64, in open
    raise SerialException("could not open port {!r}: {!r}".format(self.portstr, ctypes.WinError()))
serial.serialutil.SerialException: could not open port 'COM5': PermissionError(13, '连到系统上的设备没有发挥作用。', None, 31)

2025-06-28 11:47:27,265 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 11:47:27,268 - LVER - ERROR - [串口连接错误] 串口访问权限不足: 连接串口 COM5 - could not open port 'COM5': PermissionError(13, '连到系统上的设备没有发挥作用。', None, 31)
Traceback (most recent call last):
  File "D:\code\haha\LVER\serial_communication.py", line 54, in connect
    self.serial_port = serial.Serial(
                       ~~~~~~~~~~~~~^
        port=port,
        ^^^^^^^^^^
    ...<4 lines>...
        timeout=self.timeout
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\serial\serialwin32.py", line 33, in __init__
    super(Serial, self).__init__(*args, **kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\serial\serialutil.py", line 244, in __init__
    self.open()
    ~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\serial\serialwin32.py", line 64, in open
    raise SerialException("could not open port {!r}: {!r}".format(self.portstr, ctypes.WinError()))
serial.serialutil.SerialException: could not open port 'COM5': PermissionError(13, '连到系统上的设备没有发挥作用。', None, 31)

2025-06-28 11:47:30,729 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 11:47:30,732 - LVER - ERROR - [串口连接错误] 串口访问权限不足: 连接串口 COM5 - could not open port 'COM5': PermissionError(13, '连到系统上的设备没有发挥作用。', None, 31)
Traceback (most recent call last):
  File "D:\code\haha\LVER\serial_communication.py", line 54, in connect
    self.serial_port = serial.Serial(
                       ~~~~~~~~~~~~~^
        port=port,
        ^^^^^^^^^^
    ...<4 lines>...
        timeout=self.timeout
        ^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\serial\serialwin32.py", line 33, in __init__
    super(Serial, self).__init__(*args, **kwargs)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\serial\serialutil.py", line 244, in __init__
    self.open()
    ~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\serial\serialwin32.py", line 64, in open
    raise SerialException("could not open port {!r}: {!r}".format(self.portstr, ctypes.WinError()))
serial.serialutil.SerialException: could not open port 'COM5': PermissionError(13, '连到系统上的设备没有发挥作用。', None, 31)

2025-06-28 11:47:47,121 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 11:47:47,182 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 11:47:51,082 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:47:51,590 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 11:47:51,590 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 11:47:51,590 - LVER - DEBUG - 收集CSV行: '1,R_sei,300'
2025-06-28 11:47:51,591 - LVER - DEBUG - 收集CSV行: '1,R_ct,104'
2025-06-28 11:47:51,642 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 11:47:51,642 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,535', '1,R_sei,300', '1,R_ct,104']
2025-06-28 11:47:51,642 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,535', '1,R_sei,300', '1,R_ct,104']
2025-06-28 11:47:59,202 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 11:47:59,203 - LVER - DEBUG - 发送命令: 55
2025-06-28 11:47:59,203 - LVER - DEBUG - 已发送 1 字节
2025-06-28 11:47:59,403 - LVER - DEBUG - 开始接收响应，超时时间: 6.0秒
2025-06-28 11:48:03,722 - LVER - DEBUG - 发送重置命令 0xA0
2025-06-28 11:48:03,723 - LVER - INFO - 重置命令发送成功
2025-06-28 11:48:05,442 - LVER - DEBUG - 未接收到任何响应数据
2025-06-28 11:48:05,442 - LVER - ERROR - [通信超时] 操作超时: 电压测量命令 (超时时间: 2.0秒)
2025-06-28 11:48:06,558 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 11:48:07,065 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 11:48:07,066 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 11:48:07,066 - LVER - DEBUG - 收集CSV行: '1,R_sei,283'
2025-06-28 11:48:07,116 - LVER - DEBUG - 收集CSV行: '1,R_ct,108'
2025-06-28 11:48:07,167 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 11:48:07,167 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,535', '1,R_sei,283', '1,R_ct,108']
2025-06-28 11:48:07,167 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,535', '1,R_sei,283', '1,R_ct,108']
2025-06-28 12:41:50,131 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:42:12,808 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 12:42:13,318 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 12:42:13,318 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 12:42:13,318 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 12:42:13,318 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 12:42:13,368 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 12:42:13,369 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,527', '1,R_sei,333', '1,R_ct,133']
2025-06-28 12:42:13,369 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,527', '1,R_sei,333', '1,R_ct,133']
2025-06-28 12:58:22,898 - LVER - INFO - 串口连接已断开
2025-06-28 13:40:22,071 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:40:22,071 - LVER - DEBUG - 串口监控已启动
2025-06-28 13:40:22,071 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:40:49,547 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:40:49,547 - LVER - DEBUG - 串口监控已启动
2025-06-28 13:40:49,548 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:40:49,548 - LVER - DEBUG - 添加测试数据: 测试#1, 电压=3295mV
2025-06-28 13:40:49,548 - LVER - DEBUG - 添加测试数据: 测试#2, 电压=3290mV
2025-06-28 13:40:49,548 - LVER - DEBUG - 添加测试数据: 测试#3, 电压=3285mV
2025-06-28 13:40:51,551 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:40:53,562 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:40:55,577 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:40:57,591 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:40:59,605 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:41:01,606 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:41:03,607 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:41:05,614 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:41:07,630 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:41:09,636 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:41:11,647 - LVER - DEBUG - 发现 0 个串口: []
2025-06-28 13:41:13,655 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:18,621 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:18,622 - LVER - DEBUG - 串口监控已启动
2025-06-30 08:40:18,631 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:18,632 - LVER - DEBUG - 添加测试数据: 测试#1, 电压=3295mV
2025-06-30 08:40:18,632 - LVER - DEBUG - 添加测试数据: 测试#2, 电压=3290mV
2025-06-30 08:40:18,632 - LVER - DEBUG - 添加测试数据: 测试#3, 电压=3285mV
2025-06-30 08:40:20,634 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:22,649 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:22,661 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:23,605 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:23,893 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:24,397 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:24,665 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:26,676 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:26,828 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:27,372 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:27,877 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:28,469 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:28,689 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:29,060 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:29,717 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:30,044 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:30,356 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:30,699 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:32,709 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:34,710 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:35,330 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:35,330 - LVER - ERROR - [串口连接错误] 未发现可用串口
2025-06-30 08:40:36,712 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:38,719 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:40,724 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:42,739 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:43,788 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:44,752 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:46,764 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:48,778 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:50,785 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:52,795 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:54,804 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:56,812 - LVER - DEBUG - 发现 0 个串口: []
2025-06-30 08:40:58,825 - LVER - DEBUG - 串口监控已停止
2025-06-30 08:40:58,927 - LVER - DEBUG - 串口监控已停止
2025-06-30 08:42:20,450 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:42:20,450 - LVER - DEBUG - 串口监控已启动
2025-06-30 08:42:20,450 - LVER - DEBUG - 串口列表变化: {'COM3', 'COM1'}
2025-06-30 08:42:20,451 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:42:20,451 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:42:20,452 - LVER - DEBUG - 添加测试数据: 测试#1, 电压=5075mV
2025-06-30 08:42:20,452 - LVER - DEBUG - 添加测试数据: 测试#2, 电压=5070mV
2025-06-30 08:42:20,452 - LVER - DEBUG - 添加测试数据: 测试#3, 电压=5065mV
2025-06-30 08:42:20,452 - LVER - DEBUG - 添加测试数据: 测试#4, 电压=5080mV
2025-06-30 08:42:20,452 - LVER - DEBUG - 添加测试数据: 测试#5, 电压=5072mV
2025-06-30 08:42:21,581 - LVER - ERROR - [串口通信错误] 串口通信错误: 串口监控 - main thread is not in main loop
Traceback (most recent call last):
  File "D:\code\haha\LVER\enhanced_serial.py", line 83, in _port_monitor_worker
    self.port_change_callback(port_list)
  File "D:\code\haha\LVER\gui.py", line 309, in on_port_list_changed
    self.root.after(0, update_ui)
  File "C:\Program Files\Python38\lib\tkinter\__init__.py", line 821, in after
    name = self._register(callit)
  File "C:\Program Files\Python38\lib\tkinter\__init__.py", line 1528, in _register
    self.tk.createcommand(name, f)
RuntimeError: main thread is not in main loop

2025-06-30 08:42:47,847 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:42:47,847 - LVER - DEBUG - 串口监控已启动
2025-06-30 08:42:47,847 - LVER - DEBUG - 串口列表变化: {'COM1', 'COM3'}
2025-06-30 08:42:47,847 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:42:47,848 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:42:47,848 - LVER - DEBUG - 添加测试数据: 测试#1, 电压=5075mV
2025-06-30 08:42:47,848 - LVER - DEBUG - 添加测试数据: 测试#2, 电压=5070mV
2025-06-30 08:42:47,848 - LVER - DEBUG - 添加测试数据: 测试#3, 电压=5065mV
2025-06-30 08:42:47,849 - LVER - DEBUG - 添加测试数据: 测试#4, 电压=5080mV
2025-06-30 08:42:47,849 - LVER - DEBUG - 添加测试数据: 测试#5, 电压=5072mV
2025-06-30 08:42:49,987 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:42:51,989 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:42:54,002 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:42:56,013 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:42:58,020 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:00,034 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:02,037 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:04,046 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:06,052 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:08,067 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:10,076 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:12,090 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:12,884 - LVER - INFO - 尝试连接串口 COM3, 波特率: 115200
2025-06-30 08:43:12,886 - LVER - ERROR - [串口通信错误] 串口通信错误: 连接串口 - module 'serial' has no attribute 'EIGHTBITS'
Traceback (most recent call last):
  File "D:\code\haha\LVER\enhanced_serial.py", line 196, in connect
    bytesize=serial.EIGHTBITS,
AttributeError: module 'serial' has no attribute 'EIGHTBITS'

2025-06-30 08:43:14,094 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:15,268 - LVER - INFO - 尝试连接串口 COM3, 波特率: 115200
2025-06-30 08:43:15,269 - LVER - ERROR - [串口通信错误] 串口通信错误: 连接串口 - module 'serial' has no attribute 'EIGHTBITS'
Traceback (most recent call last):
  File "D:\code\haha\LVER\enhanced_serial.py", line 196, in connect
    bytesize=serial.EIGHTBITS,
AttributeError: module 'serial' has no attribute 'EIGHTBITS'

2025-06-30 08:43:16,102 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:18,113 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:20,129 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:20,212 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:20,780 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:22,139 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:24,152 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:26,163 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:28,179 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:30,191 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:32,205 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:34,216 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:36,229 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:38,238 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:40,250 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:42,263 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:44,265 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:46,281 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:48,284 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:50,294 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:52,304 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:54,315 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:56,321 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:43:58,324 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:00,337 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:02,351 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:04,360 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:06,364 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:08,378 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:10,391 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:12,406 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:14,418 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:16,430 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:18,433 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:20,448 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:22,450 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:24,463 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:26,475 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:28,484 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:30,499 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:32,514 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:34,522 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:36,533 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:38,545 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:40,550 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:42,556 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:44,570 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:46,581 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:48,591 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:50,602 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:52,618 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:54,629 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:56,635 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:44:58,648 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:00,649 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:02,663 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:04,667 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:06,671 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:08,676 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:10,690 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:12,700 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:14,716 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:16,729 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:18,742 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:20,755 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:22,762 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:24,778 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:26,780 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:28,790 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:30,795 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:32,806 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:34,814 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:36,827 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:38,839 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:40,854 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:42,866 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:44,868 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:46,870 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:48,884 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:50,887 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:52,888 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:54,902 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:56,912 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:45:58,926 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:00,933 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:02,948 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:04,958 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:06,961 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:08,967 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:10,971 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:12,981 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:14,996 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:17,012 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:19,024 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:21,037 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:23,049 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:25,066 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:27,071 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:29,073 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:31,086 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:33,100 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:35,101 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:37,113 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:39,128 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:41,131 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:43,139 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:45,148 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:47,154 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:49,156 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:51,166 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:53,181 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:55,191 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:57,205 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:46:59,220 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:01,226 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:03,237 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:05,248 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:07,253 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:09,261 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:11,273 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:13,286 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:15,300 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:17,302 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:19,302 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:21,314 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:23,317 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:25,320 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:27,329 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:29,341 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:31,355 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:33,360 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:35,368 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:37,375 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:39,381 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:41,385 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:43,397 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:45,399 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:47,411 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:49,426 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:51,435 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:53,444 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:55,460 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:57,470 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:47:59,471 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:01,479 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:03,485 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:05,486 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:07,498 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:09,513 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:11,524 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:13,537 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:15,548 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:17,558 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:19,570 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:21,585 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:23,592 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:25,594 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:27,606 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:29,609 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:37,398 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:37,398 - LVER - DEBUG - 串口监控已启动
2025-06-30 08:48:37,398 - LVER - DEBUG - 串口列表变化: {'COM3', 'COM1'}
2025-06-30 08:48:37,399 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:37,399 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:37,399 - LVER - DEBUG - 添加测试数据: 测试#1, 电压=5075mV
2025-06-30 08:48:37,399 - LVER - DEBUG - 添加测试数据: 测试#2, 电压=5070mV
2025-06-30 08:48:37,400 - LVER - DEBUG - 添加测试数据: 测试#3, 电压=5065mV
2025-06-30 08:48:37,400 - LVER - DEBUG - 添加测试数据: 测试#4, 电压=5080mV
2025-06-30 08:48:37,400 - LVER - DEBUG - 添加测试数据: 测试#5, 电压=5072mV
2025-06-30 08:48:39,528 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:41,540 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:43,412 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:43,549 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:44,266 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:45,553 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:46,418 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:47,554 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:49,564 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:49,818 - LVER - INFO - 尝试连接串口 COM3, 波特率: 115200
2025-06-30 08:48:49,820 - LVER - ERROR - [串口通信错误] 串口通信错误: 连接串口 - module 'serial' has no attribute 'EIGHTBITS'
Traceback (most recent call last):
  File "D:\code\haha\LVER\enhanced_serial.py", line 196, in connect
    bytesize=serial.EIGHTBITS,
AttributeError: module 'serial' has no attribute 'EIGHTBITS'

2025-06-30 08:48:51,575 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:52,305 - LVER - INFO - 尝试连接串口 COM3, 波特率: 115200
2025-06-30 08:48:52,306 - LVER - ERROR - [串口通信错误] 串口通信错误: 连接串口 - module 'serial' has no attribute 'EIGHTBITS'
Traceback (most recent call last):
  File "D:\code\haha\LVER\enhanced_serial.py", line 196, in connect
    bytesize=serial.EIGHTBITS,
AttributeError: module 'serial' has no attribute 'EIGHTBITS'

2025-06-30 08:48:53,589 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:55,598 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:57,610 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:48:59,623 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:01,636 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:03,649 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:05,664 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:07,677 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:09,688 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:11,699 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:12,417 - LVER - INFO - 尝试连接串口 COM3, 波特率: 115200
2025-06-30 08:49:12,418 - LVER - ERROR - [串口通信错误] 串口通信错误: 连接串口 - module 'serial' has no attribute 'EIGHTBITS'
Traceback (most recent call last):
  File "D:\code\haha\LVER\enhanced_serial.py", line 196, in connect
    bytesize=serial.EIGHTBITS,
AttributeError: module 'serial' has no attribute 'EIGHTBITS'

2025-06-30 08:49:13,708 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:15,712 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:17,725 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:19,740 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:21,749 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:23,760 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:25,769 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:27,778 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:29,781 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:31,794 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:33,797 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:35,811 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:37,825 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:39,829 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:41,841 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:43,850 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:45,865 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:47,880 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:49,884 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:51,893 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:53,898 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:55,912 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:57,924 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:49:59,940 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:01,944 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:03,949 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:05,960 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:07,962 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:09,973 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:11,983 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:13,996 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:16,010 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:18,020 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:20,034 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:22,035 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:24,040 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:26,043 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:28,044 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:30,055 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:32,070 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:34,082 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:36,098 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:38,102 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:40,103 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:50:42,111 - LVER - DEBUG - 发现 2 个串口: ['COM1', 'COM3']
2025-06-30 08:55:27,788 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:27,789 - LVER - DEBUG - 串口监控已启动
2025-06-30 08:55:27,789 - LVER - DEBUG - 串口列表变化: {'COM3'}
2025-06-30 08:55:27,789 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:27,789 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:29,948 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:31,949 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:33,949 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:35,964 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:37,972 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:38,143 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:39,973 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:41,987 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:44,000 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:46,010 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:48,023 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:49,550 - LVER - INFO - 尝试连接串口 COM3, 波特率: 115200
2025-06-30 08:55:49,552 - LVER - ERROR - [串口通信错误] 串口通信错误: 连接串口 - module 'serial' has no attribute 'EIGHTBITS'
Traceback (most recent call last):
  File "D:\code\haha\LVER\enhanced_serial.py", line 196, in connect
    bytesize=serial.EIGHTBITS,
AttributeError: module 'serial' has no attribute 'EIGHTBITS'

2025-06-30 08:55:50,038 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:52,048 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:54,056 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:56,067 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:55:58,080 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:56:00,085 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:56:02,093 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:56:04,108 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:56:06,113 - LVER - DEBUG - 发现 1 个串口: ['COM3']
2025-06-30 08:56:08,125 - LVER - DEBUG - 发现 1 个串口: ['COM3']
