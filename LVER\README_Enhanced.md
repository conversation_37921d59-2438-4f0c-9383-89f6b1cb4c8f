# LVER 串口通信应用程序 - 增强版

## 概述

LVER增强版是一个功能强大的PC桌面应用程序，专为LVER设备的串口通信而设计。相比基础版本，增强版提供了更丰富的功能和更好的用户体验。

## 新增功能

### 🔌 串口连接优化
- **自动检测串口**: 自动发现可用的串口设备
- **自动刷新**: 实时监控串口设备的插拔状态
- **智能连接**: 自动尝试多种波特率进行连接
- **一键自动连接**: 智能选择最佳串口和波特率

### 🎛️ 测试控制功能
- **连续测试**: 支持自动重复执行测试命令
- **可配置间隔**: 测试间隔可设置为0.5-60秒
- **开始/停止控制**: 随时启动或停止连续测试
- **实时状态显示**: 显示当前测试状态和次数

### 📊 实时数据显示
- **电压显示**: 同时显示V和mV单位
- **电阻值显示**: 显示R_ohm、R_sei、R_ct三种电阻值（μΩ单位）
- **时间戳**: 显示最后更新时间
- **测试计数**: 显示累计测试次数
- **操作日志**: 详细记录所有操作和响应

### 📈 数据可视化
- **实时图表**: 使用matplotlib绘制实时数据曲线
- **多曲线支持**: 同时显示电压和电阻数据
- **交互功能**: 支持缩放、平移、图例控制
- **数据选择**: 可选择显示/隐藏特定数据类型

### 💾 数据导出功能
- **CSV导出**: 导出所有测试数据到CSV文件
- **时间戳记录**: 每条数据包含精确时间戳
- **自动命名**: 文件名格式为 LVER_测试数据_YYYYMMDD_HHMMSS.csv
- **数据完整性**: 包含所有测量参数和单位转换

### 🎨 界面布局重构
- **分区设计**: 连接控制、实时数据、图表分区显示
- **响应式布局**: 支持窗口大小调整
- **现代化界面**: 使用ttk组件提供更好的视觉效果

## 系统要求

- **操作系统**: Windows 7/8/10/11
- **Python版本**: 3.8或更高版本
- **必要依赖**:
  - pyserial (串口通信)
  - matplotlib (图表显示)
  - tkinter (GUI界面，通常随Python安装)

## 安装和启动

### 方法1: 使用启动脚本（推荐）
```bash
# 启动增强版应用程序
python start_enhanced_app.py
```

### 方法2: 安装依赖后直接启动
```bash
# 安装matplotlib依赖
python install_matplotlib.py

# 启动应用程序
python gui.py
```

### 方法3: 手动安装依赖
```bash
pip install matplotlib pyserial
python gui.py
```

## 使用说明

### 连接设备
1. 插入USB转串口设备
2. 点击"刷新"按钮或等待自动检测
3. 选择正确的串口
4. 点击"连接"或"自动连接"

### 单次测试
- **测试命令 (0xAA)**: 获取完整的测试数据
- **电压测量 (0x55)**: 仅测量电压值
- **重置 (0xA0)**: 重置设备状态

### 连续测试
1. 设置测试间隔（0.5-60秒）
2. 点击"开始连续测试"
3. 观察实时数据更新和图表变化
4. 点击"停止测试"结束连续测试

### 数据管理
- **查看数据**: 实时数据显示区域显示最新测量值
- **图表分析**: 图表区域显示历史数据趋势
- **导出数据**: 点击"导出数据"保存为CSV文件
- **清空数据**: 点击"清空数据"重置所有记录

## 数据格式

### 测试数据包含
- 时间戳
- 电压值 (mV和V)
- 电阻值 (R_ohm, R_sei, R_ct，单位μΩ)
- 测试序号

### CSV导出格式
```csv
测试序号,时间戳,电压(mV),电压(V),电阻(μΩ),SEI电阻(μΩ),CT电阻(μΩ)
1,2024-01-01 12:00:00,5075,5.075,123.456,234.567,345.678
```

## 故障排除

### 常见问题

1. **无法找到串口**
   - 检查USB转串口驱动是否安装
   - 确认设备已正确连接
   - 点击"刷新"按钮

2. **连接失败**
   - 尝试"自动连接"功能
   - 检查其他程序是否占用串口
   - 确认波特率设置正确

3. **图表不显示**
   - 运行 `install_matplotlib.py` 安装matplotlib
   - 检查Python版本兼容性

4. **数据解析错误**
   - 检查设备响应格式
   - 查看操作日志中的错误信息

### 技术支持

如遇到问题，请检查操作日志中的详细错误信息，或联系技术支持。

## 版本历史

- **v2.0** (增强版): 添加连续测试、实时图表、数据导出等功能
- **v1.0** (基础版): 基本串口通信和命令发送功能

## 开发信息

- **开发语言**: Python
- **GUI框架**: tkinter
- **图表库**: matplotlib
- **串口库**: pyserial
