@echo off
chcp 65001 >nul
echo.
echo 🔋 鲸测云LCER电池测试仪 - COM5权限问题快速解决
echo ================================================================
echo.
echo 🔍 正在诊断COM5端口状态...
echo.

REM 检查COM5端口是否存在
mode COM5: >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ COM5端口已检测到
) else (
    echo ❌ COM5端口未检测到
    echo 💡 请检查USB设备是否正确连接
    goto :end
)

echo.
echo 🔧 尝试解决方案...
echo.

echo 方案1: 结束可能占用COM5的进程...
taskkill /f /im "sscom32.exe" >nul 2>&1
taskkill /f /im "putty.exe" >nul 2>&1
taskkill /f /im "teraterm.exe" >nul 2>&1
taskkill /f /im "hyperterminal.exe" >nul 2>&1
echo ✓ 已尝试结束常见串口工具进程

echo.
echo 方案2: 重置COM5端口...
echo 正在重置COM5端口状态...

REM 使用PowerShell重置COM5端口
powershell -Command "Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like '*COM5*'} | ForEach-Object {$_.Disable(); Start-Sleep -Seconds 2; $_.Enable()}" >nul 2>&1

if %errorlevel% equ 0 (
    echo ✅ COM5端口重置完成
) else (
    echo ⚠️  端口重置可能需要管理员权限
)

echo.
echo 方案3: 以管理员权限启动应用程序...
echo.

REM 切换到应用程序目录
cd /d "D:\code\haha\LVER"

echo 🚀 正在以管理员权限启动应用程序...
echo 如果出现UAC提示，请点击"是"
echo.

REM 以管理员权限启动应用程序
powershell -Command "Start-Process 'D:\code\haha\.venv\Scripts\python.exe' -ArgumentList 'main_fixed_complete.py' -Verb RunAs"

if %errorlevel% equ 0 (
    echo ✅ 应用程序启动命令已执行
    echo 💡 请在新窗口中测试COM5连接
) else (
    echo ❌ 启动失败，请手动以管理员身份运行
    echo.
    echo 📋 手动操作步骤:
    echo 1. 右键点击"命令提示符"
    echo 2. 选择"以管理员身份运行"
    echo 3. 执行命令: cd /d "D:\code\haha\LVER"
    echo 4. 执行命令: D:\code\haha\.venv\Scripts\python.exe main_fixed_complete.py
)

:end
echo.
echo ================================================================
echo 🎯 如果问题仍未解决，请尝试:
echo 1. 重新插拔USB设备
echo 2. 重启计算机
echo 3. 更新USB转串口驱动程序
echo ================================================================
echo.
pause
