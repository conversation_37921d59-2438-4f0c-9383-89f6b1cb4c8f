#!/usr/bin/env python3
"""
重启应用程序脚本 - 快速重启真实串口通信应用程序
"""

import subprocess
import sys
import os

def restart_app():
    """重启应用程序"""
    print("重启LVER串口通信应用程序...")
    print("=" * 50)
    
    # 使用已知的Python 3.13环境
    python_cmd = r"C:\Python313\python.exe"
    
    try:
        # 直接启动主应用程序
        subprocess.run([python_cmd, "main.py"], cwd=os.path.dirname(os.path.abspath(__file__)))
    except KeyboardInterrupt:
        print("\n应用程序被用户中断")
    except Exception as e:
        print(f"启动应用程序时出错: {e}")

if __name__ == "__main__":
    restart_app()
