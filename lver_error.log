2025-06-28 12:59:23,436 - LVER - DEBUG - 串口监控已启动
2025-06-28 12:59:23,454 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:23,454 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:23,454 - LVER - DEBUG - 串口列表变化: {'COM5', 'COM1'}
2025-06-28 12:59:23,467 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:25,517 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:27,530 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:29,543 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:31,556 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:33,569 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:35,582 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:37,595 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:39,609 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:41,622 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:43,634 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:45,647 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:47,661 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:49,675 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:51,163 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:51,688 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:53,701 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:55,367 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 12:59:55,464 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 12:59:55,714 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:57,728 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:59,741 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:59,879 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:00,388 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:00,388 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:00:00,388 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:00:00,389 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:00:00,439 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:00,439 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,125']
2025-06-28 13:00:00,439 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,125']
2025-06-28 13:00:00,440 - LVER - DEBUG - 添加测试数据: 测试#1, 电压=3295.0mV
2025-06-28 13:00:01,754 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:03,767 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:05,495 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 13:00:05,496 - LVER - DEBUG - 发送命令: 55
2025-06-28 13:00:05,497 - LVER - DEBUG - 已发送 1 字节
2025-06-28 13:00:05,697 - LVER - DEBUG - 开始接收响应，超时时间: 6.0秒
2025-06-28 13:00:05,781 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:07,793 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:09,687 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:09,806 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:10,196 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:10,196 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:10,196 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:00:10,196 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:00:10,247 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:10,248 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,141']
2025-06-28 13:00:10,248 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,141']
2025-06-28 13:00:10,248 - LVER - DEBUG - 添加测试数据: 测试#2, 电压=3295.0mV
2025-06-28 13:00:11,739 - LVER - DEBUG - 未接收到任何响应数据
2025-06-28 13:00:11,739 - LVER - ERROR - [通信超时] 操作超时: 电压测量命令 (超时时间: 2.0秒)
2025-06-28 13:00:11,818 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:13,831 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:15,255 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:15,256 - LVER - INFO - 开始连续测试，间隔: 2.0秒
2025-06-28 13:00:15,763 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:15,764 - LVER - DEBUG - 收集CSV行: '1,R_ohm,539'
2025-06-28 13:00:15,764 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:00:15,764 - LVER - DEBUG - 收集CSV行: '1,R_ct,166'
2025-06-28 13:00:15,814 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:15,815 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,539', '1,R_sei,337', '1,R_ct,166']
2025-06-28 13:00:15,815 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,539', '1,R_sei,337', '1,R_ct,166']
2025-06-28 13:00:15,815 - LVER - DEBUG - 添加测试数据: 测试#3, 电压=3295.0mV
2025-06-28 13:00:15,843 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:17,827 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:17,858 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:18,334 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:18,335 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:00:18,335 - LVER - DEBUG - 收集CSV行: '1,R_sei,345'
2025-06-28 13:00:18,386 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:00:18,437 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:18,437 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,522', '1,R_sei,345', '1,R_ct,125']
2025-06-28 13:00:18,437 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,522', '1,R_sei,345', '1,R_ct,125']
2025-06-28 13:00:18,437 - LVER - DEBUG - 添加测试数据: 测试#4, 电压=3295.0mV
2025-06-28 13:00:19,871 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:20,452 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:20,961 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:20,962 - LVER - DEBUG - 收集CSV行: '1,R_ohm,543'
2025-06-28 13:00:20,962 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:00:20,962 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:00:21,013 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:21,013 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,543', '1,R_sei,320', '1,R_ct,129']
2025-06-28 13:00:21,013 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,543', '1,R_sei,320', '1,R_ct,129']
2025-06-28 13:00:21,014 - LVER - DEBUG - 添加测试数据: 测试#5, 电压=3295.0mV
2025-06-28 13:00:21,884 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:23,029 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:23,540 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:23,540 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:23,540 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:00:23,540 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:00:23,591 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:23,591 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,133']
2025-06-28 13:00:23,592 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,133']
2025-06-28 13:00:23,592 - LVER - DEBUG - 添加测试数据: 测试#6, 电压=3295.0mV
2025-06-28 13:00:23,898 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:25,602 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:25,910 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:26,108 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:26,109 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:00:26,109 - LVER - DEBUG - 收集CSV行: '1,R_sei,316'
2025-06-28 13:00:26,160 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:00:26,211 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:26,211 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,535', '1,R_sei,316', '1,R_ct,137']
2025-06-28 13:00:26,211 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,535', '1,R_sei,316', '1,R_ct,137']
2025-06-28 13:00:26,211 - LVER - DEBUG - 添加测试数据: 测试#7, 电压=3295.0mV
2025-06-28 13:00:27,924 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:28,225 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:28,732 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:28,732 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:28,732 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:00:28,783 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:00:28,834 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:28,834 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,129']
2025-06-28 13:00:28,835 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,129']
2025-06-28 13:00:28,835 - LVER - DEBUG - 添加测试数据: 测试#8, 电压=3290.0mV
2025-06-28 13:00:29,937 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:30,849 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:31,358 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:31,358 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:00:31,358 - LVER - DEBUG - 收集CSV行: '1,R_sei,316'
2025-06-28 13:00:31,359 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:00:31,409 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:31,409 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,316', '1,R_ct,137']
2025-06-28 13:00:31,409 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,316', '1,R_ct,137']
2025-06-28 13:00:31,409 - LVER - DEBUG - 添加测试数据: 测试#9, 电压=3290.0mV
2025-06-28 13:00:31,949 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:33,422 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:33,931 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:33,931 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:00:33,931 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:00:33,931 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:00:33,963 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:33,982 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:33,982 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,133']
2025-06-28 13:00:33,982 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,133']
2025-06-28 13:00:33,982 - LVER - DEBUG - 添加测试数据: 测试#10, 电压=3290.0mV
2025-06-28 13:00:35,975 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:35,994 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:36,502 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:36,502 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:36,502 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:00:36,502 - LVER - DEBUG - 收集CSV行: '1,R_ct,166'
2025-06-28 13:00:36,553 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:36,553 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,166']
2025-06-28 13:00:36,554 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,166']
2025-06-28 13:00:36,554 - LVER - DEBUG - 添加测试数据: 测试#11, 电压=3290.0mV
2025-06-28 13:00:37,988 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:38,567 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:39,076 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:39,076 - LVER - DEBUG - 收集CSV行: '1,R_ohm,518'
2025-06-28 13:00:39,076 - LVER - DEBUG - 收集CSV行: '1,R_sei,341'
2025-06-28 13:00:39,076 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:00:39,127 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:39,127 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,518', '1,R_sei,341', '1,R_ct,158']
2025-06-28 13:00:39,127 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,518', '1,R_sei,341', '1,R_ct,158']
2025-06-28 13:00:39,127 - LVER - DEBUG - 添加测试数据: 测试#12, 电压=3290.0mV
2025-06-28 13:00:40,001 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:41,138 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:41,647 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:41,647 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:41,647 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:00:41,647 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:00:41,698 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:41,698 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,154']
2025-06-28 13:00:41,698 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,154']
2025-06-28 13:00:41,698 - LVER - DEBUG - 添加测试数据: 测试#13, 电压=3290.0mV
2025-06-28 13:00:42,014 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:43,711 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:44,027 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:44,219 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:44,219 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:00:44,219 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:00:44,219 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:00:44,270 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:44,270 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:00:44,270 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:00:44,270 - LVER - DEBUG - 添加测试数据: 测试#14, 电压=3290.0mV
2025-06-28 13:00:46,040 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:46,284 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:46,792 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:46,793 - LVER - DEBUG - 收集CSV行: '1,R_ohm,539'
2025-06-28 13:00:46,793 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:00:46,793 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:00:46,843 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:46,843 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,539', '1,R_sei,325', '1,R_ct,141']
2025-06-28 13:00:46,843 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,539', '1,R_sei,325', '1,R_ct,141']
2025-06-28 13:00:46,844 - LVER - DEBUG - 添加测试数据: 测试#15, 电压=3290.0mV
2025-06-28 13:00:48,053 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:48,857 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:49,365 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:49,365 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:49,365 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:00:49,365 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:00:49,416 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:49,416 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,154']
2025-06-28 13:00:49,416 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,154']
2025-06-28 13:00:49,416 - LVER - DEBUG - 添加测试数据: 测试#16, 电压=3290.0mV
2025-06-28 13:00:50,066 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:51,431 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:51,938 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:51,939 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:00:51,939 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:00:51,990 - LVER - DEBUG - 收集CSV行: '1,R_ct,170'
2025-06-28 13:00:52,040 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:52,041 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,337', '1,R_ct,170']
2025-06-28 13:00:52,041 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,337', '1,R_ct,170']
2025-06-28 13:00:52,041 - LVER - DEBUG - 添加测试数据: 测试#17, 电压=3290.0mV
2025-06-28 13:00:52,079 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:54,050 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:54,092 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:54,558 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:54,558 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:54,558 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:00:54,609 - LVER - DEBUG - 收集CSV行: '1,R_ct,170'
2025-06-28 13:00:54,659 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:54,660 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,170']
2025-06-28 13:00:54,660 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,170']
2025-06-28 13:00:54,660 - LVER - DEBUG - 添加测试数据: 测试#18, 电压=3290.0mV
2025-06-28 13:00:56,106 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
