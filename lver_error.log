2025-06-28 12:59:23,436 - LVER - DEBUG - 串口监控已启动
2025-06-28 12:59:23,454 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:23,454 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:23,454 - LVER - DEBUG - 串口列表变化: {'COM5', 'COM1'}
2025-06-28 12:59:23,467 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:25,517 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:27,530 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:29,543 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:31,556 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:33,569 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:35,582 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:37,595 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:39,609 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:41,622 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:43,634 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:45,647 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:47,661 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:49,675 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:51,163 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:51,688 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:53,701 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:55,367 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 12:59:55,464 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 12:59:55,714 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:57,728 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:59,741 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 12:59:59,879 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:00,388 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:00,388 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:00:00,388 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:00:00,389 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:00:00,439 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:00,439 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,125']
2025-06-28 13:00:00,439 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,125']
2025-06-28 13:00:00,440 - LVER - DEBUG - 添加测试数据: 测试#1, 电压=3295.0mV
2025-06-28 13:00:01,754 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:03,767 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:05,495 - LVER - DEBUG - 发送电压测量命令 0x55
2025-06-28 13:00:05,496 - LVER - DEBUG - 发送命令: 55
2025-06-28 13:00:05,497 - LVER - DEBUG - 已发送 1 字节
2025-06-28 13:00:05,697 - LVER - DEBUG - 开始接收响应，超时时间: 6.0秒
2025-06-28 13:00:05,781 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:07,793 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:09,687 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:09,806 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:10,196 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:10,196 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:10,196 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:00:10,196 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:00:10,247 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:10,248 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,141']
2025-06-28 13:00:10,248 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,141']
2025-06-28 13:00:10,248 - LVER - DEBUG - 添加测试数据: 测试#2, 电压=3295.0mV
2025-06-28 13:00:11,739 - LVER - DEBUG - 未接收到任何响应数据
2025-06-28 13:00:11,739 - LVER - ERROR - [通信超时] 操作超时: 电压测量命令 (超时时间: 2.0秒)
2025-06-28 13:00:11,818 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:13,831 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:15,255 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:15,256 - LVER - INFO - 开始连续测试，间隔: 2.0秒
2025-06-28 13:00:15,763 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:15,764 - LVER - DEBUG - 收集CSV行: '1,R_ohm,539'
2025-06-28 13:00:15,764 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:00:15,764 - LVER - DEBUG - 收集CSV行: '1,R_ct,166'
2025-06-28 13:00:15,814 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:15,815 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,539', '1,R_sei,337', '1,R_ct,166']
2025-06-28 13:00:15,815 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,539', '1,R_sei,337', '1,R_ct,166']
2025-06-28 13:00:15,815 - LVER - DEBUG - 添加测试数据: 测试#3, 电压=3295.0mV
2025-06-28 13:00:15,843 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:17,827 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:17,858 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:18,334 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:18,335 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:00:18,335 - LVER - DEBUG - 收集CSV行: '1,R_sei,345'
2025-06-28 13:00:18,386 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:00:18,437 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:18,437 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,522', '1,R_sei,345', '1,R_ct,125']
2025-06-28 13:00:18,437 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,522', '1,R_sei,345', '1,R_ct,125']
2025-06-28 13:00:18,437 - LVER - DEBUG - 添加测试数据: 测试#4, 电压=3295.0mV
2025-06-28 13:00:19,871 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:20,452 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:20,961 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:20,962 - LVER - DEBUG - 收集CSV行: '1,R_ohm,543'
2025-06-28 13:00:20,962 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:00:20,962 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:00:21,013 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:21,013 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,543', '1,R_sei,320', '1,R_ct,129']
2025-06-28 13:00:21,013 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,543', '1,R_sei,320', '1,R_ct,129']
2025-06-28 13:00:21,014 - LVER - DEBUG - 添加测试数据: 测试#5, 电压=3295.0mV
2025-06-28 13:00:21,884 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:23,029 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:23,540 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:23,540 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:23,540 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:00:23,540 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:00:23,591 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:23,591 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,133']
2025-06-28 13:00:23,592 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,133']
2025-06-28 13:00:23,592 - LVER - DEBUG - 添加测试数据: 测试#6, 电压=3295.0mV
2025-06-28 13:00:23,898 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:25,602 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:25,910 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:26,108 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:00:26,109 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:00:26,109 - LVER - DEBUG - 收集CSV行: '1,R_sei,316'
2025-06-28 13:00:26,160 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:00:26,211 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:26,211 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,535', '1,R_sei,316', '1,R_ct,137']
2025-06-28 13:00:26,211 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,535', '1,R_sei,316', '1,R_ct,137']
2025-06-28 13:00:26,211 - LVER - DEBUG - 添加测试数据: 测试#7, 电压=3295.0mV
2025-06-28 13:00:27,924 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:28,225 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:28,732 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:28,732 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:28,732 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:00:28,783 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:00:28,834 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:28,834 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,129']
2025-06-28 13:00:28,835 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,129']
2025-06-28 13:00:28,835 - LVER - DEBUG - 添加测试数据: 测试#8, 电压=3290.0mV
2025-06-28 13:00:29,937 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:30,849 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:31,358 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:31,358 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:00:31,358 - LVER - DEBUG - 收集CSV行: '1,R_sei,316'
2025-06-28 13:00:31,359 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:00:31,409 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:31,409 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,316', '1,R_ct,137']
2025-06-28 13:00:31,409 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,316', '1,R_ct,137']
2025-06-28 13:00:31,409 - LVER - DEBUG - 添加测试数据: 测试#9, 电压=3290.0mV
2025-06-28 13:00:31,949 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:33,422 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:33,931 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:33,931 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:00:33,931 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:00:33,931 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:00:33,963 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:33,982 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:33,982 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,133']
2025-06-28 13:00:33,982 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,133']
2025-06-28 13:00:33,982 - LVER - DEBUG - 添加测试数据: 测试#10, 电压=3290.0mV
2025-06-28 13:00:35,975 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:35,994 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:36,502 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:36,502 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:36,502 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:00:36,502 - LVER - DEBUG - 收集CSV行: '1,R_ct,166'
2025-06-28 13:00:36,553 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:36,553 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,166']
2025-06-28 13:00:36,554 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,166']
2025-06-28 13:00:36,554 - LVER - DEBUG - 添加测试数据: 测试#11, 电压=3290.0mV
2025-06-28 13:00:37,988 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:38,567 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:39,076 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:39,076 - LVER - DEBUG - 收集CSV行: '1,R_ohm,518'
2025-06-28 13:00:39,076 - LVER - DEBUG - 收集CSV行: '1,R_sei,341'
2025-06-28 13:00:39,076 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:00:39,127 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:39,127 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,518', '1,R_sei,341', '1,R_ct,158']
2025-06-28 13:00:39,127 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,518', '1,R_sei,341', '1,R_ct,158']
2025-06-28 13:00:39,127 - LVER - DEBUG - 添加测试数据: 测试#12, 电压=3290.0mV
2025-06-28 13:00:40,001 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:41,138 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:41,647 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:41,647 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:41,647 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:00:41,647 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:00:41,698 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:41,698 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,154']
2025-06-28 13:00:41,698 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,154']
2025-06-28 13:00:41,698 - LVER - DEBUG - 添加测试数据: 测试#13, 电压=3290.0mV
2025-06-28 13:00:42,014 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:43,711 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:44,027 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:44,219 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:44,219 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:00:44,219 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:00:44,219 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:00:44,270 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:44,270 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:00:44,270 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:00:44,270 - LVER - DEBUG - 添加测试数据: 测试#14, 电压=3290.0mV
2025-06-28 13:00:46,040 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:46,284 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:46,792 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:46,793 - LVER - DEBUG - 收集CSV行: '1,R_ohm,539'
2025-06-28 13:00:46,793 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:00:46,793 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:00:46,843 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:46,843 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,539', '1,R_sei,325', '1,R_ct,141']
2025-06-28 13:00:46,843 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,539', '1,R_sei,325', '1,R_ct,141']
2025-06-28 13:00:46,844 - LVER - DEBUG - 添加测试数据: 测试#15, 电压=3290.0mV
2025-06-28 13:00:48,053 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:48,857 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:49,365 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:49,365 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:49,365 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:00:49,365 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:00:49,416 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:49,416 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,154']
2025-06-28 13:00:49,416 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,154']
2025-06-28 13:00:49,416 - LVER - DEBUG - 添加测试数据: 测试#16, 电压=3290.0mV
2025-06-28 13:00:50,066 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:51,431 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:51,938 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:51,939 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:00:51,939 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:00:51,990 - LVER - DEBUG - 收集CSV行: '1,R_ct,170'
2025-06-28 13:00:52,040 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:52,041 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,337', '1,R_ct,170']
2025-06-28 13:00:52,041 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,337', '1,R_ct,170']
2025-06-28 13:00:52,041 - LVER - DEBUG - 添加测试数据: 测试#17, 电压=3290.0mV
2025-06-28 13:00:52,079 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:54,050 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:00:54,092 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:00:54,558 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:00:54,558 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:00:54,558 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:00:54,609 - LVER - DEBUG - 收集CSV行: '1,R_ct,170'
2025-06-28 13:00:54,659 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:00:54,660 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,170']
2025-06-28 13:00:54,660 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,170']
2025-06-28 13:00:54,660 - LVER - DEBUG - 添加测试数据: 测试#18, 电压=3290.0mV
2025-06-28 13:00:56,106 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:04:58,398 - LVER - DEBUG - 串口监控已启动
2025-06-28 13:04:58,416 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:04:58,417 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:04:58,417 - LVER - DEBUG - 串口列表变化: {'COM1', 'COM5'}
2025-06-28 13:04:58,430 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:00,502 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:02,517 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:04,531 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:06,545 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:08,559 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:10,572 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:12,586 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:14,601 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:16,615 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:18,628 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:20,643 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:22,656 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:24,669 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:26,684 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:28,697 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:30,711 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:32,724 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:34,738 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:36,751 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:38,765 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:40,779 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:42,793 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:44,805 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:46,819 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:48,836 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:50,849 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:52,863 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:54,876 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:56,889 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:05:58,902 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:00,917 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:02,931 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:04,944 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:06,957 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:08,970 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:10,983 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:12,996 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:15,010 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:17,023 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:19,037 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:21,050 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:23,063 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:25,076 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:27,090 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:29,104 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:31,118 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:33,131 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:35,144 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:37,157 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:39,170 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:41,183 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:43,197 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:45,210 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:47,223 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:49,236 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:51,251 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:53,265 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:55,278 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:57,292 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:06:59,305 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:01,318 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:03,332 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:05,345 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:07,359 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:09,373 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:11,386 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:13,398 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:15,411 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:17,424 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:19,439 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:21,452 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:23,465 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:25,478 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:27,492 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:29,505 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:31,519 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:33,532 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:35,548 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:37,561 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:39,574 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:41,587 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:43,601 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:45,615 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:47,628 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:49,641 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:51,657 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:53,671 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:55,685 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:57,699 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:07:59,712 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:01,731 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:03,744 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:05,757 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:07,770 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:09,783 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:11,797 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:13,810 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:15,824 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:17,838 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:19,852 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:21,865 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:23,879 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:25,893 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:27,907 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:29,920 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:31,934 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:33,947 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:35,960 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:37,974 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:39,988 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:42,001 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:44,015 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:46,028 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:48,042 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:50,056 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:52,070 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:54,084 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:56,097 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:08:58,110 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:00,123 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:02,137 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:04,150 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:06,169 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:08,183 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:10,196 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:12,215 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:14,228 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:16,242 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:18,256 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:20,275 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:22,289 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:24,302 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:26,315 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:28,331 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:30,345 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:32,359 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:34,373 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:36,386 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:38,400 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:40,413 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:42,429 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:44,443 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:46,457 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:48,469 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:50,483 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:52,497 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:54,511 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:56,525 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:09:58,539 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:00,552 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:02,565 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:04,579 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:06,593 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:08,607 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:10,621 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:12,634 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:14,647 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:16,660 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:18,675 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:20,688 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:22,701 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:24,715 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:26,729 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:28,743 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:30,757 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:32,771 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:34,784 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:36,797 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:38,811 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:40,825 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:42,839 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:44,853 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:44,856 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:46,867 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:47,851 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 13:10:47,948 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 13:10:48,881 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:50,895 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:52,763 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:10:52,763 - LVER - INFO - 开始连续测试，间隔: 2.0秒
2025-06-28 13:10:52,909 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:53,271 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:10:53,271 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:10:53,272 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:10:53,272 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:10:53,322 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:10:53,322 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,137']
2025-06-28 13:10:53,322 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,137']
2025-06-28 13:10:53,322 - LVER - DEBUG - 添加测试数据: 测试#1, 电压=3295.0mV
2025-06-28 13:10:54,923 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:55,337 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:10:55,846 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:10:55,846 - LVER - DEBUG - 收集CSV行: '1,R_ohm,518'
2025-06-28 13:10:55,846 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:10:55,846 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:10:55,897 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:10:55,898 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,518', '1,R_sei,337', '1,R_ct,133']
2025-06-28 13:10:55,898 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,518', '1,R_sei,337', '1,R_ct,133']
2025-06-28 13:10:55,898 - LVER - DEBUG - 添加测试数据: 测试#2, 电压=3295.0mV
2025-06-28 13:10:56,936 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:10:57,915 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:10:58,422 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:10:58,422 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:10:58,423 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:10:58,473 - LVER - DEBUG - 收集CSV行: '1,R_ct,116'
2025-06-28 13:10:58,524 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:10:58,524 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,522', '1,R_sei,325', '1,R_ct,116']
2025-06-28 13:10:58,525 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,522', '1,R_sei,325', '1,R_ct,116']
2025-06-28 13:10:58,525 - LVER - DEBUG - 添加测试数据: 测试#3, 电压=3295.0mV
2025-06-28 13:10:58,950 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:00,540 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:11:00,963 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:01,046 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:11:01,046 - LVER - DEBUG - 收集CSV行: '1,R_ohm,514'
2025-06-28 13:11:01,097 - LVER - DEBUG - 收集CSV行: '1,R_sei,354'
2025-06-28 13:11:01,097 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:11:01,148 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:11:01,148 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,514', '1,R_sei,354', '1,R_ct,141']
2025-06-28 13:11:01,148 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,514', '1,R_sei,354', '1,R_ct,141']
2025-06-28 13:11:01,148 - LVER - DEBUG - 添加测试数据: 测试#4, 电压=3295.0mV
2025-06-28 13:11:02,976 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:03,162 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:11:03,670 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:11:03,670 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:11:03,670 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:11:03,721 - LVER - DEBUG - 收集CSV行: '1,R_ct,112'
2025-06-28 13:11:03,771 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:11:03,772 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,112']
2025-06-28 13:11:03,772 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,112']
2025-06-28 13:11:03,772 - LVER - DEBUG - 添加测试数据: 测试#5, 电压=3295.0mV
2025-06-28 13:11:04,990 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:05,785 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:11:06,294 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:11:06,294 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:11:06,294 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:11:06,294 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:11:06,345 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:11:06,345 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,158']
2025-06-28 13:11:06,345 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,158']
2025-06-28 13:11:06,346 - LVER - DEBUG - 添加测试数据: 测试#6, 电压=3290.0mV
2025-06-28 13:11:07,003 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:08,357 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:11:08,863 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:11:08,863 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:11:08,863 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:11:08,914 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:11:08,964 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:11:08,965 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,129']
2025-06-28 13:11:08,965 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,129']
2025-06-28 13:11:08,965 - LVER - DEBUG - 添加测试数据: 测试#7, 电压=3290.0mV
2025-06-28 13:11:09,017 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:10,978 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:11:11,031 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:11,487 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:11:11,487 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:11:11,487 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:11:11,487 - LVER - DEBUG - 收集CSV行: '1,R_ct,120'
2025-06-28 13:11:11,538 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:11:11,538 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,337', '1,R_ct,120']
2025-06-28 13:11:11,538 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,337', '1,R_ct,120']
2025-06-28 13:11:11,538 - LVER - DEBUG - 添加测试数据: 测试#8, 电压=3290.0mV
2025-06-28 13:11:13,049 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:13,549 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:11:14,058 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:11:14,058 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:11:14,059 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:11:14,059 - LVER - DEBUG - 收集CSV行: '1,R_ct,145'
2025-06-28 13:11:14,109 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:11:14,110 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,145']
2025-06-28 13:11:14,110 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,145']
2025-06-28 13:11:14,110 - LVER - DEBUG - 添加测试数据: 测试#9, 电压=3290.0mV
2025-06-28 13:11:15,063 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:16,122 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:11:16,631 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:11:16,631 - LVER - DEBUG - 收集CSV行: '1,R_ohm,547'
2025-06-28 13:11:16,631 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:11:16,631 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:11:16,682 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:11:16,682 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,547', '1,R_sei,325', '1,R_ct,141']
2025-06-28 13:11:16,682 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,547', '1,R_sei,325', '1,R_ct,141']
2025-06-28 13:11:16,683 - LVER - DEBUG - 添加测试数据: 测试#10, 电压=3290.0mV
2025-06-28 13:11:17,076 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:18,700 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:11:19,089 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:19,209 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:11:19,209 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:11:19,209 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:11:19,209 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:11:19,260 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:11:19,260 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,154']
2025-06-28 13:11:19,260 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,154']
2025-06-28 13:11:19,260 - LVER - DEBUG - 添加测试数据: 测试#11, 电压=3290.0mV
2025-06-28 13:11:21,104 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:21,272 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:11:21,778 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:11:21,778 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:11:21,778 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:11:21,830 - LVER - DEBUG - 收集CSV行: '1,R_ct,166'
2025-06-28 13:11:21,881 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:11:21,881 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,166']
2025-06-28 13:11:21,881 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,166']
2025-06-28 13:11:21,881 - LVER - DEBUG - 添加测试数据: 测试#12, 电压=3290.0mV
2025-06-28 13:11:23,117 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:23,895 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:11:24,402 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:11:24,402 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:11:24,402 - LVER - DEBUG - 收集CSV行: '1,R_sei,354'
2025-06-28 13:11:24,453 - LVER - DEBUG - 收集CSV行: '1,R_ct,145'
2025-06-28 13:11:54,505 - LVER - DEBUG - 串口监控已启动
2025-06-28 13:11:54,523 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:54,523 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:54,523 - LVER - DEBUG - 串口列表变化: {'COM1', 'COM5'}
2025-06-28 13:11:54,536 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:56,579 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:11:58,592 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:00,605 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:02,618 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:03,574 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:04,632 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:05,930 - LVER - INFO - 尝试连接串口 COM5, 波特率: 115200
2025-06-28 13:12:06,027 - LVER - INFO - 成功连接到串口 COM5
2025-06-28 13:12:06,646 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:08,659 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:10,672 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:10,779 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:12:10,779 - LVER - INFO - 开始连续测试，间隔: 2.0秒
2025-06-28 13:12:11,287 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:12:11,287 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:12:11,287 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:12:11,287 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:12:11,338 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:12:11,338 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,129']
2025-06-28 13:12:11,339 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,129']
2025-06-28 13:12:11,339 - LVER - DEBUG - 添加测试数据: 测试#1, 电压=3295.0mV
2025-06-28 13:12:12,688 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:13,360 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:12:13,871 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:12:13,871 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:12:13,872 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:12:13,872 - LVER - DEBUG - 收集CSV行: '1,R_ct,104'
2025-06-28 13:12:13,923 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:12:13,924 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,522', '1,R_sei,337', '1,R_ct,104']
2025-06-28 13:12:13,924 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,522', '1,R_sei,337', '1,R_ct,104']
2025-06-28 13:12:13,924 - LVER - DEBUG - 添加测试数据: 测试#2, 电压=3295.0mV
2025-06-28 13:12:14,700 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:15,956 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:12:16,464 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:12:16,464 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:12:16,464 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:12:16,515 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:12:16,566 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:12:16,566 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,125']
2025-06-28 13:12:16,566 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,125']
2025-06-28 13:12:16,566 - LVER - DEBUG - 添加测试数据: 测试#3, 电压=3295.0mV
2025-06-28 13:12:16,713 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:18,584 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:12:18,726 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:19,092 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:12:19,092 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:12:19,092 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:12:19,143 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:12:19,193 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:12:19,193 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,522', '1,R_sei,337', '1,R_ct,129']
2025-06-28 13:12:19,193 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,522', '1,R_sei,337', '1,R_ct,129']
2025-06-28 13:12:19,194 - LVER - DEBUG - 添加测试数据: 测试#4, 电压=3295.0mV
2025-06-28 13:12:20,740 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:21,210 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:12:21,719 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:12:21,720 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:12:21,720 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:12:21,720 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:12:21,770 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:12:21,770 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,133']
2025-06-28 13:12:21,771 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,133']
2025-06-28 13:12:21,771 - LVER - DEBUG - 添加测试数据: 测试#5, 电压=3295.0mV
2025-06-28 13:12:22,753 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:23,790 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:12:24,298 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:12:24,298 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:12:24,298 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:12:24,298 - LVER - DEBUG - 收集CSV行: '1,R_ct,112'
2025-06-28 13:12:24,349 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:12:24,349 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,112']
2025-06-28 13:12:24,349 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,112']
2025-06-28 13:12:24,349 - LVER - DEBUG - 添加测试数据: 测试#6, 电压=3295.0mV
2025-06-28 13:12:24,766 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:26,369 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:12:26,780 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:26,876 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:12:26,877 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:12:26,877 - LVER - DEBUG - 收集CSV行: '1,R_sei,345'
2025-06-28 13:12:26,927 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:12:26,978 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:12:26,978 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,345', '1,R_ct,129']
2025-06-28 13:12:26,978 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,345', '1,R_ct,129']
2025-06-28 13:12:26,978 - LVER - DEBUG - 添加测试数据: 测试#7, 电压=3290.0mV
2025-06-28 13:12:28,793 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:29,000 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:12:29,509 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:12:29,509 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:12:29,509 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:12:29,509 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:12:29,560 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:12:29,560 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,133']
2025-06-28 13:12:29,560 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,133']
2025-06-28 13:12:29,560 - LVER - DEBUG - 添加测试数据: 测试#8, 电压=3290.0mV
2025-06-28 13:12:30,807 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:31,579 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:12:32,089 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:12:32,089 - LVER - DEBUG - 收集CSV行: '1,R_ohm,543'
2025-06-28 13:12:32,089 - LVER - DEBUG - 收集CSV行: '1,R_sei,316'
2025-06-28 13:12:32,090 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:12:32,140 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:12:32,140 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,543', '1,R_sei,316', '1,R_ct,133']
2025-06-28 13:12:32,140 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,543', '1,R_sei,316', '1,R_ct,133']
2025-06-28 13:12:32,141 - LVER - DEBUG - 添加测试数据: 测试#9, 电压=3290.0mV
2025-06-28 13:12:32,820 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:34,156 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:12:34,665 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:12:34,665 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:12:34,665 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:12:34,665 - LVER - DEBUG - 收集CSV行: '1,R_ct,145'
2025-06-28 13:12:34,716 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:12:34,716 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,145']
2025-06-28 13:12:34,716 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,145']
2025-06-28 13:12:34,716 - LVER - DEBUG - 添加测试数据: 测试#10, 电压=3290.0mV
2025-06-28 13:12:34,834 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:36,736 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:12:36,847 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:37,244 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:12:37,244 - LVER - DEBUG - 收集CSV行: '1,R_ohm,518'
2025-06-28 13:12:37,244 - LVER - DEBUG - 收集CSV行: '1,R_sei,341'
2025-06-28 13:12:37,296 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:12:37,346 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:12:37,347 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,518', '1,R_sei,341', '1,R_ct,137']
2025-06-28 13:12:37,347 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,518', '1,R_sei,341', '1,R_ct,137']
2025-06-28 13:12:37,347 - LVER - DEBUG - 添加测试数据: 测试#11, 电压=3290.0mV
2025-06-28 13:12:38,860 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:12:39,378 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:12:39,885 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:12:39,886 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:12:39,886 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:12:39,937 - LVER - DEBUG - 收集CSV行: '1,R_ct,120'
2025-06-28 13:13:26,579 - LVER - DEBUG - 串口监控已启动
2025-06-28 13:13:26,598 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:26,599 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:26,599 - LVER - DEBUG - 串口列表变化: {'COM5', 'COM1'}
2025-06-28 13:13:26,615 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:28,680 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:30,693 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:32,707 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:34,721 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:36,734 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:38,748 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:40,762 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:42,776 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:44,788 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:46,805 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:48,818 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:50,832 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:52,845 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:54,858 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:56,871 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:13:58,883 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:00,896 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:02,910 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:04,928 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:06,943 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:08,977 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:10,989 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:11,030 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:11,031 - LVER - DEBUG - 尝试连接端口: COM5 (Silicon Labs CP210x USB to UART Bridge (COM5))
2025-06-28 13:14:11,031 - LVER - DEBUG - 尝试 COM5 波特率 115200
2025-06-28 13:14:11,177 - LVER - INFO - 成功连接到 COM5 (波特率: 115200)
2025-06-28 13:14:13,002 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:15,016 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:16,001 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:16,001 - LVER - DEBUG - 尝试连接端口: COM5 (Silicon Labs CP210x USB to UART Bridge (COM5))
2025-06-28 13:14:16,001 - LVER - DEBUG - 尝试 COM5 波特率 115200
2025-06-28 13:14:16,002 - LVER - DEBUG - 端口 COM5 波特率 115200 连接失败: could not open port 'COM5': PermissionError(13, '拒绝访问。', None, 5)
2025-06-28 13:14:16,002 - LVER - DEBUG - 尝试 COM5 波特率 9600
2025-06-28 13:14:16,002 - LVER - DEBUG - 端口 COM5 波特率 9600 连接失败: could not open port 'COM5': PermissionError(13, '拒绝访问。', None, 5)
2025-06-28 13:14:16,002 - LVER - DEBUG - 尝试 COM5 波特率 38400
2025-06-28 13:14:16,002 - LVER - DEBUG - 端口 COM5 波特率 38400 连接失败: could not open port 'COM5': PermissionError(13, '拒绝访问。', None, 5)
2025-06-28 13:14:16,002 - LVER - DEBUG - 尝试 COM5 波特率 57600
2025-06-28 13:14:16,003 - LVER - DEBUG - 端口 COM5 波特率 57600 连接失败: could not open port 'COM5': PermissionError(13, '拒绝访问。', None, 5)
2025-06-28 13:14:16,003 - LVER - DEBUG - 尝试 COM5 波特率 19200
2025-06-28 13:14:16,003 - LVER - DEBUG - 端口 COM5 波特率 19200 连接失败: could not open port 'COM5': PermissionError(13, '拒绝访问。', None, 5)
2025-06-28 13:14:16,003 - LVER - DEBUG - 尝试 COM5 波特率 4800
2025-06-28 13:14:16,003 - LVER - DEBUG - 端口 COM5 波特率 4800 连接失败: could not open port 'COM5': PermissionError(13, '拒绝访问。', None, 5)
2025-06-28 13:14:16,003 - LVER - DEBUG - 尝试连接端口: COM1 (通信端口 (COM1))
2025-06-28 13:14:16,003 - LVER - DEBUG - 尝试 COM1 波特率 115200
2025-06-28 13:14:16,004 - LVER - INFO - 成功连接到 COM1 (波特率: 115200)
2025-06-28 13:14:17,029 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:19,043 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:21,055 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:23,069 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:25,083 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:26,313 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:26,314 - LVER - DEBUG - 尝试连接端口: COM5 (Silicon Labs CP210x USB to UART Bridge (COM5))
2025-06-28 13:14:26,314 - LVER - DEBUG - 尝试 COM5 波特率 115200
2025-06-28 13:14:26,462 - LVER - INFO - 成功连接到 COM5 (波特率: 115200)
2025-06-28 13:14:27,096 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:29,111 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:29,346 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:14:29,853 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:14:29,853 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:14:29,853 - LVER - DEBUG - 收集CSV行: '1,R_sei,300'
2025-06-28 13:14:29,904 - LVER - DEBUG - 收集CSV行: '1,R_ct,108'
2025-06-28 13:14:29,955 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:14:29,955 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,535', '1,R_sei,300', '1,R_ct,108']
2025-06-28 13:14:29,955 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,535', '1,R_sei,300', '1,R_ct,108']
2025-06-28 13:14:29,956 - LVER - DEBUG - 添加测试数据: 测试#1, 电压=3295.0mV
2025-06-28 13:14:31,125 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:33,138 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:33,266 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:14:33,266 - LVER - INFO - 开始连续测试，间隔: 2.0秒
2025-06-28 13:14:33,773 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:14:33,773 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:14:33,774 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:14:33,825 - LVER - DEBUG - 收集CSV行: '1,R_ct,112'
2025-06-28 13:14:33,876 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:14:33,876 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,112']
2025-06-28 13:14:33,876 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,112']
2025-06-28 13:14:33,876 - LVER - DEBUG - 添加测试数据: 测试#2, 电压=3295.0mV
2025-06-28 13:14:35,152 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:35,894 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:14:35,906 - LVER - DEBUG - 发送重置命令 0xA0
2025-06-28 13:14:35,906 - LVER - INFO - 重置命令发送成功
2025-06-28 13:14:36,402 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:14:36,402 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:14:36,402 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:14:36,403 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:14:36,453 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:14:36,453 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,129']
2025-06-28 13:14:36,454 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,129']
2025-06-28 13:14:36,454 - LVER - DEBUG - 添加测试数据: 测试#3, 电压=3295.0mV
2025-06-28 13:14:37,165 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:38,474 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:14:38,982 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:14:38,983 - LVER - DEBUG - 收集CSV行: '1,R_ohm,514'
2025-06-28 13:14:38,983 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:14:39,033 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:14:39,084 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:14:39,084 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,514', '1,R_sei,337', '1,R_ct,133']
2025-06-28 13:14:39,084 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,514', '1,R_sei,337', '1,R_ct,133']
2025-06-28 13:14:39,084 - LVER - DEBUG - 添加测试数据: 测试#4, 电压=3295.0mV
2025-06-28 13:14:39,178 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:41,113 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:14:41,192 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:41,621 - LVER - DEBUG - 收集CSV行: '1,V,3295'
2025-06-28 13:14:41,621 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:14:41,621 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:14:41,621 - LVER - DEBUG - 收集CSV行: '1,R_ct,112'
2025-06-28 13:14:41,672 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:14:41,672 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3295', '1,R_ohm,527', '1,R_sei,337', '1,R_ct,112']
2025-06-28 13:14:41,672 - LVER - DEBUG - 测试命令响应: ['1,V,3295', '1,R_ohm,527', '1,R_sei,337', '1,R_ct,112']
2025-06-28 13:14:41,672 - LVER - DEBUG - 添加测试数据: 测试#5, 电压=3295.0mV
2025-06-28 13:14:43,208 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:43,692 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:14:44,199 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:14:44,200 - LVER - DEBUG - 收集CSV行: '1,R_ohm,514'
2025-06-28 13:14:44,200 - LVER - DEBUG - 收集CSV行: '1,R_sei,345'
2025-06-28 13:14:44,250 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:14:44,301 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:14:44,301 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,514', '1,R_sei,345', '1,R_ct,137']
2025-06-28 13:14:44,302 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,514', '1,R_sei,345', '1,R_ct,137']
2025-06-28 13:14:44,302 - LVER - DEBUG - 添加测试数据: 测试#6, 电压=3290.0mV
2025-06-28 13:14:45,221 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:46,320 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:14:46,827 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:14:46,827 - LVER - DEBUG - 收集CSV行: '1,R_ohm,497'
2025-06-28 13:14:46,827 - LVER - DEBUG - 收集CSV行: '1,R_sei,354'
2025-06-28 13:14:46,878 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:14:46,929 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:14:46,929 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,497', '1,R_sei,354', '1,R_ct,129']
2025-06-28 13:14:46,929 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,497', '1,R_sei,354', '1,R_ct,129']
2025-06-28 13:14:46,930 - LVER - DEBUG - 添加测试数据: 测试#7, 电压=3290.0mV
2025-06-28 13:14:47,235 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:48,947 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:14:49,248 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:49,454 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:14:49,454 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:14:49,454 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:14:49,505 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:14:49,555 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:14:49,555 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,125']
2025-06-28 13:14:49,555 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,125']
2025-06-28 13:14:49,555 - LVER - DEBUG - 添加测试数据: 测试#8, 电压=3290.0mV
2025-06-28 13:14:51,262 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:51,574 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:14:52,083 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:14:52,083 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:14:52,083 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:14:52,083 - LVER - DEBUG - 收集CSV行: '1,R_ct,145'
2025-06-28 13:14:52,134 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:14:52,134 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,145']
2025-06-28 13:14:52,135 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,145']
2025-06-28 13:14:52,135 - LVER - DEBUG - 添加测试数据: 测试#9, 电压=3290.0mV
2025-06-28 13:14:53,275 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:54,154 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:14:54,660 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:14:54,660 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:14:54,660 - LVER - DEBUG - 收集CSV行: '1,R_sei,316'
2025-06-28 13:14:54,711 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:14:54,762 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:14:54,762 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,316', '1,R_ct,154']
2025-06-28 13:14:54,762 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,316', '1,R_ct,154']
2025-06-28 13:14:54,763 - LVER - DEBUG - 添加测试数据: 测试#10, 电压=3290.0mV
2025-06-28 13:14:55,288 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:56,781 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:14:57,291 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:14:57,291 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:14:57,292 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:14:57,292 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:14:57,302 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:57,343 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:14:57,343 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,320', '1,R_ct,133']
2025-06-28 13:14:57,343 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,320', '1,R_ct,133']
2025-06-28 13:14:57,343 - LVER - DEBUG - 添加测试数据: 测试#11, 电压=3290.0mV
2025-06-28 13:14:59,317 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:14:59,370 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:14:59,878 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:14:59,879 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:14:59,879 - LVER - DEBUG - 收集CSV行: '1,R_sei,312'
2025-06-28 13:14:59,879 - LVER - DEBUG - 收集CSV行: '1,R_ct,175'
2025-06-28 13:14:59,930 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:14:59,930 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,312', '1,R_ct,175']
2025-06-28 13:14:59,930 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,312', '1,R_ct,175']
2025-06-28 13:14:59,930 - LVER - DEBUG - 添加测试数据: 测试#12, 电压=3290.0mV
2025-06-28 13:15:01,330 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:01,948 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:02,458 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:02,458 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:15:02,458 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:15:02,459 - LVER - DEBUG - 收集CSV行: '1,R_ct,145'
2025-06-28 13:15:02,509 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:02,509 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,145']
2025-06-28 13:15:02,509 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,145']
2025-06-28 13:15:02,509 - LVER - DEBUG - 添加测试数据: 测试#13, 电压=3290.0mV
2025-06-28 13:15:03,343 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:04,527 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:05,037 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:05,038 - LVER - DEBUG - 收集CSV行: '1,R_ohm,552'
2025-06-28 13:15:05,038 - LVER - DEBUG - 收集CSV行: '1,R_sei,312'
2025-06-28 13:15:05,038 - LVER - DEBUG - 收集CSV行: '1,R_ct,150'
2025-06-28 13:15:05,088 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:05,088 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,552', '1,R_sei,312', '1,R_ct,150']
2025-06-28 13:15:05,089 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,552', '1,R_sei,312', '1,R_ct,150']
2025-06-28 13:15:05,089 - LVER - DEBUG - 添加测试数据: 测试#14, 电压=3290.0mV
2025-06-28 13:15:05,357 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:07,107 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:07,370 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:07,616 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:07,616 - LVER - DEBUG - 收集CSV行: '1,R_ohm,514'
2025-06-28 13:15:07,616 - LVER - DEBUG - 收集CSV行: '1,R_sei,345'
2025-06-28 13:15:07,616 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:15:07,666 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:07,666 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,514', '1,R_sei,345', '1,R_ct,141']
2025-06-28 13:15:07,667 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,514', '1,R_sei,345', '1,R_ct,141']
2025-06-28 13:15:07,667 - LVER - DEBUG - 添加测试数据: 测试#15, 电压=3290.0mV
2025-06-28 13:15:09,383 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:09,683 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:10,192 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:10,192 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:15:10,193 - LVER - DEBUG - 收集CSV行: '1,R_sei,316'
2025-06-28 13:15:10,193 - LVER - DEBUG - 收集CSV行: '1,R_ct,170'
2025-06-28 13:15:10,244 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:10,244 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,316', '1,R_ct,170']
2025-06-28 13:15:10,244 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,316', '1,R_ct,170']
2025-06-28 13:15:10,245 - LVER - DEBUG - 添加测试数据: 测试#16, 电压=3290.0mV
2025-06-28 13:15:11,396 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:12,261 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:12,769 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:12,769 - LVER - DEBUG - 收集CSV行: '1,R_ohm,518'
2025-06-28 13:15:12,769 - LVER - DEBUG - 收集CSV行: '1,R_sei,350'
2025-06-28 13:15:12,820 - LVER - DEBUG - 收集CSV行: '1,R_ct,145'
2025-06-28 13:15:12,871 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:12,871 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,518', '1,R_sei,350', '1,R_ct,145']
2025-06-28 13:15:12,871 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,518', '1,R_sei,350', '1,R_ct,145']
2025-06-28 13:15:12,871 - LVER - DEBUG - 添加测试数据: 测试#17, 电压=3290.0mV
2025-06-28 13:15:13,410 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:14,891 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:15,399 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:15,399 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:15:15,399 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:15:15,400 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:15:15,424 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:15,451 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:15,451 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,337', '1,R_ct,154']
2025-06-28 13:15:15,451 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,337', '1,R_ct,154']
2025-06-28 13:15:15,451 - LVER - DEBUG - 添加测试数据: 测试#18, 电压=3290.0mV
2025-06-28 13:15:17,437 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:17,468 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:17,978 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:17,978 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:15:17,978 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:15:17,978 - LVER - DEBUG - 收集CSV行: '1,R_ct,145'
2025-06-28 13:15:18,028 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:18,029 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,145']
2025-06-28 13:15:18,029 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,145']
2025-06-28 13:15:18,029 - LVER - DEBUG - 添加测试数据: 测试#19, 电压=3290.0mV
2025-06-28 13:15:19,451 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:20,045 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:20,553 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:20,553 - LVER - DEBUG - 收集CSV行: '1,R_ohm,547'
2025-06-28 13:15:20,554 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:15:20,554 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:15:20,604 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:20,605 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,547', '1,R_sei,333', '1,R_ct,137']
2025-06-28 13:15:20,605 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,547', '1,R_sei,333', '1,R_ct,137']
2025-06-28 13:15:20,605 - LVER - DEBUG - 添加测试数据: 测试#20, 电压=3290.0mV
2025-06-28 13:15:21,463 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:22,624 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:23,134 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:23,134 - LVER - DEBUG - 收集CSV行: '1,R_ohm,539'
2025-06-28 13:15:23,134 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:15:23,134 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:15:23,184 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:23,185 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,539', '1,R_sei,325', '1,R_ct,154']
2025-06-28 13:15:23,185 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,539', '1,R_sei,325', '1,R_ct,154']
2025-06-28 13:15:23,185 - LVER - DEBUG - 添加测试数据: 测试#21, 电压=3290.0mV
2025-06-28 13:15:23,477 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:25,201 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:25,490 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:25,708 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:25,709 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:15:25,709 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:15:25,760 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:15:25,810 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:25,811 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:15:25,811 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:15:25,811 - LVER - DEBUG - 添加测试数据: 测试#22, 电压=3290.0mV
2025-06-28 13:15:27,504 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:27,829 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:28,336 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:28,336 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:15:28,336 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:15:28,387 - LVER - DEBUG - 收集CSV行: '1,R_ct,166'
2025-06-28 13:15:28,438 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:28,438 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,166']
2025-06-28 13:15:28,438 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,166']
2025-06-28 13:15:28,438 - LVER - DEBUG - 添加测试数据: 测试#23, 电压=3290.0mV
2025-06-28 13:15:29,517 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:30,453 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:30,962 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:30,962 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:15:30,963 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:15:30,963 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:15:31,014 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:31,014 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,320', '1,R_ct,141']
2025-06-28 13:15:31,014 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,320', '1,R_ct,141']
2025-06-28 13:15:31,014 - LVER - DEBUG - 添加测试数据: 测试#24, 电压=3290.0mV
2025-06-28 13:15:31,530 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:33,031 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:33,540 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:33,540 - LVER - DEBUG - 收集CSV行: '1,R_ohm,518'
2025-06-28 13:15:33,541 - LVER - DEBUG - 收集CSV行: '1,R_sei,341'
2025-06-28 13:15:33,541 - LVER - DEBUG - 收集CSV行: '1,R_ct,183'
2025-06-28 13:15:33,544 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:33,591 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:33,591 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,518', '1,R_sei,341', '1,R_ct,183']
2025-06-28 13:15:33,591 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,518', '1,R_sei,341', '1,R_ct,183']
2025-06-28 13:15:33,592 - LVER - DEBUG - 添加测试数据: 测试#25, 电压=3290.0mV
2025-06-28 13:15:35,557 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:35,618 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:36,127 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:36,127 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:15:36,127 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:15:36,127 - LVER - DEBUG - 收集CSV行: '1,R_ct,162'
2025-06-28 13:15:36,178 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:36,178 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,162']
2025-06-28 13:15:36,178 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,162']
2025-06-28 13:15:36,178 - LVER - DEBUG - 添加测试数据: 测试#26, 电压=3290.0mV
2025-06-28 13:15:37,570 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:38,196 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:38,704 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:38,705 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:15:38,705 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:15:38,705 - LVER - DEBUG - 收集CSV行: '1,R_ct,166'
2025-06-28 13:15:38,755 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:38,756 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,166']
2025-06-28 13:15:38,756 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,166']
2025-06-28 13:15:38,756 - LVER - DEBUG - 添加测试数据: 测试#27, 电压=3290.0mV
2025-06-28 13:15:39,583 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:40,772 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:41,280 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:41,280 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:15:41,280 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:15:41,331 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:15:41,383 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:41,383 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:15:41,383 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:15:41,383 - LVER - DEBUG - 添加测试数据: 测试#28, 电压=3290.0mV
2025-06-28 13:15:41,596 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:43,402 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:43,609 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:43,910 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:43,910 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:15:43,910 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:15:43,910 - LVER - DEBUG - 收集CSV行: '1,R_ct,170'
2025-06-28 13:15:43,961 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:43,961 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,170']
2025-06-28 13:15:43,961 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,170']
2025-06-28 13:15:43,961 - LVER - DEBUG - 添加测试数据: 测试#29, 电压=3290.0mV
2025-06-28 13:15:45,622 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:45,979 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:46,487 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:46,487 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:15:46,487 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:15:46,487 - LVER - DEBUG - 收集CSV行: '1,R_ct,175'
2025-06-28 13:15:46,537 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:46,538 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,175']
2025-06-28 13:15:46,538 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,175']
2025-06-28 13:15:46,538 - LVER - DEBUG - 添加测试数据: 测试#30, 电压=3290.0mV
2025-06-28 13:15:47,636 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:48,554 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:49,062 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:49,062 - LVER - DEBUG - 收集CSV行: '1,R_ohm,518'
2025-06-28 13:15:49,062 - LVER - DEBUG - 收集CSV行: '1,R_sei,345'
2025-06-28 13:15:49,113 - LVER - DEBUG - 收集CSV行: '1,R_ct,166'
2025-06-28 13:15:49,164 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:49,164 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,518', '1,R_sei,345', '1,R_ct,166']
2025-06-28 13:15:49,164 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,518', '1,R_sei,345', '1,R_ct,166']
2025-06-28 13:15:49,164 - LVER - DEBUG - 添加测试数据: 测试#31, 电压=3290.0mV
2025-06-28 13:15:49,649 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:51,179 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:51,664 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:51,689 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:51,689 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:15:51,689 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:15:51,689 - LVER - DEBUG - 收集CSV行: '1,R_ct,162'
2025-06-28 13:15:51,739 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:51,739 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,162']
2025-06-28 13:15:51,740 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,162']
2025-06-28 13:15:51,740 - LVER - DEBUG - 添加测试数据: 测试#32, 电压=3290.0mV
2025-06-28 13:15:53,678 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:53,759 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:54,267 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:54,267 - LVER - DEBUG - 收集CSV行: '1,R_ohm,547'
2025-06-28 13:15:54,267 - LVER - DEBUG - 收集CSV行: '1,R_sei,316'
2025-06-28 13:15:54,268 - LVER - DEBUG - 收集CSV行: '1,R_ct,162'
2025-06-28 13:15:54,319 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:54,319 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,547', '1,R_sei,316', '1,R_ct,162']
2025-06-28 13:15:54,319 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,547', '1,R_sei,316', '1,R_ct,162']
2025-06-28 13:15:54,319 - LVER - DEBUG - 添加测试数据: 测试#33, 电压=3290.0mV
2025-06-28 13:15:55,691 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:56,335 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:56,844 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:56,844 - LVER - DEBUG - 收集CSV行: '1,R_ohm,547'
2025-06-28 13:15:56,844 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:15:56,844 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:15:56,895 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:56,895 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,547', '1,R_sei,320', '1,R_ct,158']
2025-06-28 13:15:56,895 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,547', '1,R_sei,320', '1,R_ct,158']
2025-06-28 13:15:56,895 - LVER - DEBUG - 添加测试数据: 测试#34, 电压=3290.0mV
2025-06-28 13:15:57,705 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:15:58,911 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:15:59,420 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:15:59,420 - LVER - DEBUG - 收集CSV行: '1,R_ohm,543'
2025-06-28 13:15:59,421 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:15:59,421 - LVER - DEBUG - 收集CSV行: '1,R_ct,175'
2025-06-28 13:15:59,472 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:15:59,472 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,543', '1,R_sei,320', '1,R_ct,175']
2025-06-28 13:15:59,472 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,543', '1,R_sei,320', '1,R_ct,175']
2025-06-28 13:15:59,472 - LVER - DEBUG - 添加测试数据: 测试#35, 电压=3290.0mV
2025-06-28 13:15:59,719 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:01,490 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:01,731 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:01,998 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:01,998 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:16:01,998 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:16:02,049 - LVER - DEBUG - 收集CSV行: '1,R_ct,175'
2025-06-28 13:16:02,100 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:02,100 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,175']
2025-06-28 13:16:02,100 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,175']
2025-06-28 13:16:02,100 - LVER - DEBUG - 添加测试数据: 测试#36, 电压=3290.0mV
2025-06-28 13:16:03,744 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:04,118 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:04,626 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:04,627 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:16:04,627 - LVER - DEBUG - 收集CSV行: '1,R_sei,350'
2025-06-28 13:16:04,627 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:16:04,678 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:04,678 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,350', '1,R_ct,125']
2025-06-28 13:16:04,678 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,350', '1,R_ct,125']
2025-06-28 13:16:04,678 - LVER - DEBUG - 添加测试数据: 测试#37, 电压=3290.0mV
2025-06-28 13:16:05,758 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:06,694 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:07,203 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:07,203 - LVER - DEBUG - 收集CSV行: '1,R_ohm,518'
2025-06-28 13:16:07,203 - LVER - DEBUG - 收集CSV行: '1,R_sei,316'
2025-06-28 13:16:07,203 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:16:07,254 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:07,254 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,518', '1,R_sei,316', '1,R_ct,154']
2025-06-28 13:16:07,254 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,518', '1,R_sei,316', '1,R_ct,154']
2025-06-28 13:16:07,254 - LVER - DEBUG - 添加测试数据: 测试#38, 电压=3290.0mV
2025-06-28 13:16:07,772 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:09,280 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:09,786 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:09,788 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:09,788 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:16:09,789 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:16:09,789 - LVER - DEBUG - 收集CSV行: '1,R_ct,183'
2025-06-28 13:16:09,839 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:09,839 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,183']
2025-06-28 13:16:09,840 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,183']
2025-06-28 13:16:09,840 - LVER - DEBUG - 添加测试数据: 测试#39, 电压=3290.0mV
2025-06-28 13:16:11,799 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:11,858 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:12,365 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:12,365 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:16:12,365 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:16:12,365 - LVER - DEBUG - 收集CSV行: '1,R_ct,166'
2025-06-28 13:16:12,416 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:12,416 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,337', '1,R_ct,166']
2025-06-28 13:16:12,416 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,337', '1,R_ct,166']
2025-06-28 13:16:12,416 - LVER - DEBUG - 添加测试数据: 测试#40, 电压=3290.0mV
2025-06-28 13:16:13,814 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:14,434 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:14,943 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:14,943 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:16:14,943 - LVER - DEBUG - 收集CSV行: '1,R_sei,350'
2025-06-28 13:16:14,943 - LVER - DEBUG - 收集CSV行: '1,R_ct,150'
2025-06-28 13:16:14,994 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:14,994 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,350', '1,R_ct,150']
2025-06-28 13:16:14,994 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,350', '1,R_ct,150']
2025-06-28 13:16:14,994 - LVER - DEBUG - 添加测试数据: 测试#41, 电压=3290.0mV
2025-06-28 13:16:15,826 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:17,012 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:17,522 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:17,523 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:16:17,523 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:16:17,523 - LVER - DEBUG - 收集CSV行: '1,R_ct,116'
2025-06-28 13:16:17,574 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:17,574 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,116']
2025-06-28 13:16:17,574 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,116']
2025-06-28 13:16:17,574 - LVER - DEBUG - 添加测试数据: 测试#42, 电压=3290.0mV
2025-06-28 13:16:17,840 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:19,593 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:19,854 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:20,101 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:20,101 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:16:20,101 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:16:20,101 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:16:20,152 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:20,153 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,133']
2025-06-28 13:16:20,153 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,133']
2025-06-28 13:16:20,153 - LVER - DEBUG - 添加测试数据: 测试#43, 电压=3290.0mV
2025-06-28 13:16:21,867 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:22,170 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:22,678 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:22,678 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:16:22,678 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:16:22,729 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:16:22,779 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:22,780 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,158']
2025-06-28 13:16:22,780 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,158']
2025-06-28 13:16:22,780 - LVER - DEBUG - 添加测试数据: 测试#44, 电压=3290.0mV
2025-06-28 13:16:23,880 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:24,796 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:25,304 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:25,304 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:16:25,304 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:16:25,305 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:16:25,355 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:25,355 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,141']
2025-06-28 13:16:25,355 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,141']
2025-06-28 13:16:25,355 - LVER - DEBUG - 添加测试数据: 测试#45, 电压=3290.0mV
2025-06-28 13:16:25,893 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:27,370 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:27,878 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:27,878 - LVER - DEBUG - 收集CSV行: '1,R_ohm,518'
2025-06-28 13:16:27,878 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:16:27,879 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:16:27,906 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:27,929 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:27,929 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,518', '1,R_sei,337', '1,R_ct,133']
2025-06-28 13:16:27,929 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,518', '1,R_sei,337', '1,R_ct,133']
2025-06-28 13:16:27,929 - LVER - DEBUG - 添加测试数据: 测试#46, 电压=3290.0mV
2025-06-28 13:16:29,919 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:29,959 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:30,467 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:30,467 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:16:30,467 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:16:30,518 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:16:30,569 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:30,569 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:16:30,570 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:16:30,570 - LVER - DEBUG - 添加测试数据: 测试#47, 电压=3290.0mV
2025-06-28 13:16:31,932 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:32,589 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:33,098 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:33,098 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:16:33,098 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:16:33,098 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:16:33,149 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:33,150 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,154']
2025-06-28 13:16:33,150 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,154']
2025-06-28 13:16:33,150 - LVER - DEBUG - 添加测试数据: 测试#48, 电压=3290.0mV
2025-06-28 13:16:33,946 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:35,182 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:35,691 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:35,691 - LVER - DEBUG - 收集CSV行: '1,R_ohm,552'
2025-06-28 13:16:35,691 - LVER - DEBUG - 收集CSV行: '1,R_sei,304'
2025-06-28 13:16:35,691 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:16:35,742 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:35,742 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,552', '1,R_sei,304', '1,R_ct,137']
2025-06-28 13:16:35,742 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,552', '1,R_sei,304', '1,R_ct,137']
2025-06-28 13:16:35,742 - LVER - DEBUG - 添加测试数据: 测试#49, 电压=3290.0mV
2025-06-28 13:16:35,960 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:37,769 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:37,974 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:38,277 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:38,278 - LVER - DEBUG - 收集CSV行: '1,R_ohm,506'
2025-06-28 13:16:38,278 - LVER - DEBUG - 收集CSV行: '1,R_sei,354'
2025-06-28 13:16:38,328 - LVER - DEBUG - 收集CSV行: '1,R_ct,150'
2025-06-28 13:16:38,379 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:38,380 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,506', '1,R_sei,354', '1,R_ct,150']
2025-06-28 13:16:38,380 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,506', '1,R_sei,354', '1,R_ct,150']
2025-06-28 13:16:38,380 - LVER - DEBUG - 添加测试数据: 测试#50, 电压=3290.0mV
2025-06-28 13:16:39,987 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:40,397 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:40,904 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:40,904 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:16:40,904 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:16:40,955 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:16:41,006 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:41,006 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:16:41,007 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:16:41,007 - LVER - DEBUG - 添加测试数据: 测试#51, 电压=3290.0mV
2025-06-28 13:16:42,000 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:43,037 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:43,544 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:43,544 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:16:43,545 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:16:43,596 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:16:43,647 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:43,647 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,325', '1,R_ct,154']
2025-06-28 13:16:43,647 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,325', '1,R_ct,154']
2025-06-28 13:16:43,647 - LVER - DEBUG - 添加测试数据: 测试#52, 电压=3290.0mV
2025-06-28 13:16:44,013 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:45,666 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:46,026 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:46,174 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:46,174 - LVER - DEBUG - 收集CSV行: '1,R_ohm,547'
2025-06-28 13:16:46,174 - LVER - DEBUG - 收集CSV行: '1,R_sei,316'
2025-06-28 13:16:46,174 - LVER - DEBUG - 收集CSV行: '1,R_ct,150'
2025-06-28 13:16:46,225 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:46,225 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,547', '1,R_sei,316', '1,R_ct,150']
2025-06-28 13:16:46,225 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,547', '1,R_sei,316', '1,R_ct,150']
2025-06-28 13:16:46,226 - LVER - DEBUG - 添加测试数据: 测试#53, 电压=3290.0mV
2025-06-28 13:16:48,040 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:48,244 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:48,754 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:48,754 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:16:48,754 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:16:48,754 - LVER - DEBUG - 收集CSV行: '1,R_ct,150'
2025-06-28 13:16:48,805 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:48,805 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,337', '1,R_ct,150']
2025-06-28 13:16:48,805 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,337', '1,R_ct,150']
2025-06-28 13:16:48,805 - LVER - DEBUG - 添加测试数据: 测试#54, 电压=3290.0mV
2025-06-28 13:16:50,053 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:50,823 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:51,332 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:51,332 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:16:51,332 - LVER - DEBUG - 收集CSV行: '1,R_sei,312'
2025-06-28 13:16:51,333 - LVER - DEBUG - 收集CSV行: '1,R_ct,162'
2025-06-28 13:16:51,383 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:51,383 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,312', '1,R_ct,162']
2025-06-28 13:16:51,383 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,312', '1,R_ct,162']
2025-06-28 13:16:51,383 - LVER - DEBUG - 添加测试数据: 测试#55, 电压=3290.0mV
2025-06-28 13:16:52,066 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:53,404 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:53,911 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:53,912 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:16:53,912 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:16:53,963 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:16:54,013 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:54,013 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,329', '1,R_ct,154']
2025-06-28 13:16:54,014 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,329', '1,R_ct,154']
2025-06-28 13:16:54,014 - LVER - DEBUG - 添加测试数据: 测试#56, 电压=3290.0mV
2025-06-28 13:16:54,079 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:56,031 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:56,093 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:56,536 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:56,536 - LVER - DEBUG - 收集CSV行: '1,R_ohm,539'
2025-06-28 13:16:56,587 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:16:56,587 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:16:56,638 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:56,638 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,539', '1,R_sei,325', '1,R_ct,125']
2025-06-28 13:16:56,638 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,539', '1,R_sei,325', '1,R_ct,125']
2025-06-28 13:16:56,638 - LVER - DEBUG - 添加测试数据: 测试#57, 电压=3290.0mV
2025-06-28 13:16:58,106 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:16:58,655 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:16:59,163 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:16:59,163 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:16:59,164 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:16:59,164 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:16:59,214 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:16:59,214 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,137']
2025-06-28 13:16:59,214 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,137']
2025-06-28 13:16:59,215 - LVER - DEBUG - 添加测试数据: 测试#58, 电压=3290.0mV
2025-06-28 13:17:00,119 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:01,232 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:01,739 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:01,740 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:17:01,740 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:17:01,790 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:17:01,841 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:01,841 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,333', '1,R_ct,141']
2025-06-28 13:17:01,841 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,333', '1,R_ct,141']
2025-06-28 13:17:01,841 - LVER - DEBUG - 添加测试数据: 测试#59, 电压=3290.0mV
2025-06-28 13:17:02,132 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:03,868 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:04,145 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:04,377 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:04,377 - LVER - DEBUG - 收集CSV行: '1,R_ohm,539'
2025-06-28 13:17:04,377 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:17:04,377 - LVER - DEBUG - 收集CSV行: '1,R_ct,170'
2025-06-28 13:17:04,428 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:04,428 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,539', '1,R_sei,329', '1,R_ct,170']
2025-06-28 13:17:04,429 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,539', '1,R_sei,329', '1,R_ct,170']
2025-06-28 13:17:04,429 - LVER - DEBUG - 添加测试数据: 测试#60, 电压=3290.0mV
2025-06-28 13:17:06,159 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:06,445 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:06,953 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:06,953 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:17:06,953 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:17:06,953 - LVER - DEBUG - 收集CSV行: '1,R_ct,150'
2025-06-28 13:17:07,004 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:07,004 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,333', '1,R_ct,150']
2025-06-28 13:17:07,004 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,333', '1,R_ct,150']
2025-06-28 13:17:07,004 - LVER - DEBUG - 添加测试数据: 测试#61, 电压=3290.0mV
2025-06-28 13:17:08,172 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:09,020 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:09,527 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:09,527 - LVER - DEBUG - 收集CSV行: '1,R_ohm,514'
2025-06-28 13:17:09,527 - LVER - DEBUG - 收集CSV行: '1,R_sei,354'
2025-06-28 13:17:09,578 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:17:09,629 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:09,629 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,514', '1,R_sei,354', '1,R_ct,133']
2025-06-28 13:17:09,629 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,514', '1,R_sei,354', '1,R_ct,133']
2025-06-28 13:17:09,629 - LVER - DEBUG - 添加测试数据: 测试#62, 电压=3290.0mV
2025-06-28 13:17:10,185 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:11,648 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:12,158 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:12,158 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:17:12,159 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:17:12,159 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:17:12,198 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:12,209 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:12,209 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,154']
2025-06-28 13:17:12,210 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,154']
2025-06-28 13:17:12,210 - LVER - DEBUG - 添加测试数据: 测试#63, 电压=3290.0mV
2025-06-28 13:17:14,211 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:14,228 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:14,735 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:14,735 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:17:14,735 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:17:14,786 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:17:14,836 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:14,837 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,141']
2025-06-28 13:17:14,837 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,141']
2025-06-28 13:17:14,837 - LVER - DEBUG - 添加测试数据: 测试#64, 电压=3290.0mV
2025-06-28 13:17:16,225 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:16,856 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:17,363 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:17,363 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:17:17,363 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:17:17,414 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:17:17,465 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:17,465 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,333', '1,R_ct,154']
2025-06-28 13:17:17,466 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,333', '1,R_ct,154']
2025-06-28 13:17:17,466 - LVER - DEBUG - 添加测试数据: 测试#65, 电压=3290.0mV
2025-06-28 13:17:18,238 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:19,484 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:19,992 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:19,993 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:17:19,993 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:17:19,993 - LVER - DEBUG - 收集CSV行: '1,R_ct,145'
2025-06-28 13:17:20,043 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:20,043 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,145']
2025-06-28 13:17:20,044 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,145']
2025-06-28 13:17:20,044 - LVER - DEBUG - 添加测试数据: 测试#66, 电压=3290.0mV
2025-06-28 13:17:20,251 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:22,060 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:22,265 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:22,570 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:22,570 - LVER - DEBUG - 收集CSV行: '1,R_ohm,543'
2025-06-28 13:17:22,570 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:17:22,570 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:17:22,621 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:22,621 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,543', '1,R_sei,325', '1,R_ct,154']
2025-06-28 13:17:22,621 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,543', '1,R_sei,325', '1,R_ct,154']
2025-06-28 13:17:22,621 - LVER - DEBUG - 添加测试数据: 测试#67, 电压=3290.0mV
2025-06-28 13:17:24,279 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:24,637 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:25,144 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:25,145 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:17:25,145 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:17:25,145 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:17:25,196 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:25,196 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,325', '1,R_ct,154']
2025-06-28 13:17:25,196 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,325', '1,R_ct,154']
2025-06-28 13:17:25,196 - LVER - DEBUG - 添加测试数据: 测试#68, 电压=3290.0mV
2025-06-28 13:17:26,292 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:27,211 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:27,720 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:27,720 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:17:27,720 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:17:27,720 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:17:27,771 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:27,771 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,141']
2025-06-28 13:17:27,771 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,141']
2025-06-28 13:17:27,772 - LVER - DEBUG - 添加测试数据: 测试#69, 电压=3290.0mV
2025-06-28 13:17:28,305 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:29,789 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:30,296 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:30,296 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:17:30,297 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:17:30,319 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:30,348 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:17:30,398 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:30,398 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,137']
2025-06-28 13:17:30,399 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,137']
2025-06-28 13:17:30,399 - LVER - DEBUG - 添加测试数据: 测试#70, 电压=3290.0mV
2025-06-28 13:17:32,333 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:32,416 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:32,925 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:32,925 - LVER - DEBUG - 收集CSV行: '1,R_ohm,539'
2025-06-28 13:17:32,925 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:17:32,925 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:17:32,976 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:32,976 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,539', '1,R_sei,325', '1,R_ct,129']
2025-06-28 13:17:32,976 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,539', '1,R_sei,325', '1,R_ct,129']
2025-06-28 13:17:32,976 - LVER - DEBUG - 添加测试数据: 测试#71, 电压=3290.0mV
2025-06-28 13:17:34,347 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:35,006 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:35,514 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:35,514 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:17:35,514 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:17:35,565 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:17:35,616 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:35,616 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,137']
2025-06-28 13:17:35,616 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,137']
2025-06-28 13:17:35,616 - LVER - DEBUG - 添加测试数据: 测试#72, 电压=3290.0mV
2025-06-28 13:17:36,360 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:37,645 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:38,153 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:38,153 - LVER - DEBUG - 收集CSV行: '1,R_ohm,506'
2025-06-28 13:17:38,153 - LVER - DEBUG - 收集CSV行: '1,R_sei,366'
2025-06-28 13:17:38,205 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:17:38,256 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:38,256 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,506', '1,R_sei,366', '1,R_ct,141']
2025-06-28 13:17:38,257 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,506', '1,R_sei,366', '1,R_ct,141']
2025-06-28 13:17:38,257 - LVER - DEBUG - 添加测试数据: 测试#73, 电压=3290.0mV
2025-06-28 13:17:38,374 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:40,281 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:40,388 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:40,791 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:40,791 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:17:40,791 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:17:40,791 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:17:40,842 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:40,842 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,125']
2025-06-28 13:17:40,842 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,125']
2025-06-28 13:17:40,842 - LVER - DEBUG - 添加测试数据: 测试#74, 电压=3290.0mV
2025-06-28 13:17:42,402 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:42,860 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:43,369 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:43,369 - LVER - DEBUG - 收集CSV行: '1,R_ohm,539'
2025-06-28 13:17:43,369 - LVER - DEBUG - 收集CSV行: '1,R_sei,312'
2025-06-28 13:17:43,369 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:17:43,420 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:43,420 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,539', '1,R_sei,312', '1,R_ct,141']
2025-06-28 13:17:43,420 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,539', '1,R_sei,312', '1,R_ct,141']
2025-06-28 13:17:43,420 - LVER - DEBUG - 添加测试数据: 测试#75, 电压=3290.0mV
2025-06-28 13:17:44,416 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:45,436 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:45,943 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:45,943 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:17:45,944 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:17:45,995 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:17:46,047 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:46,047 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:17:46,047 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,158']
2025-06-28 13:17:46,047 - LVER - DEBUG - 添加测试数据: 测试#76, 电压=3290.0mV
2025-06-28 13:17:46,429 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:48,066 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:48,442 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:48,575 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:48,575 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:17:48,575 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:17:48,575 - LVER - DEBUG - 收集CSV行: '1,R_ct,162'
2025-06-28 13:17:48,626 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:48,626 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,162']
2025-06-28 13:17:48,626 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,162']
2025-06-28 13:17:48,626 - LVER - DEBUG - 添加测试数据: 测试#77, 电压=3290.0mV
2025-06-28 13:17:50,455 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:50,642 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:51,154 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:51,155 - LVER - DEBUG - 收集CSV行: '1,R_ohm,539'
2025-06-28 13:17:51,155 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:17:51,155 - LVER - DEBUG - 收集CSV行: '1,R_ct,145'
2025-06-28 13:17:51,205 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:51,205 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,539', '1,R_sei,320', '1,R_ct,145']
2025-06-28 13:17:51,205 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,539', '1,R_sei,320', '1,R_ct,145']
2025-06-28 13:17:51,206 - LVER - DEBUG - 添加测试数据: 测试#78, 电压=3290.0mV
2025-06-28 13:17:52,469 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:53,224 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:53,731 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:53,731 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:17:53,731 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:17:53,782 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:17:53,833 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:53,833 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,333', '1,R_ct,125']
2025-06-28 13:17:53,833 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,333', '1,R_ct,125']
2025-06-28 13:17:53,833 - LVER - DEBUG - 添加测试数据: 测试#79, 电压=3290.0mV
2025-06-28 13:17:54,482 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:55,851 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:56,358 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:56,358 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:17:56,358 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:17:56,409 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:17:56,459 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:56,459 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,329', '1,R_ct,158']
2025-06-28 13:17:56,459 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,329', '1,R_ct,158']
2025-06-28 13:17:56,460 - LVER - DEBUG - 添加测试数据: 测试#80, 电压=3290.0mV
2025-06-28 13:17:56,496 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:58,474 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:17:58,509 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:17:58,981 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:17:58,981 - LVER - DEBUG - 收集CSV行: '1,R_ohm,510'
2025-06-28 13:17:58,981 - LVER - DEBUG - 收集CSV行: '1,R_sei,354'
2025-06-28 13:17:59,032 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:17:59,082 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:17:59,082 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,510', '1,R_sei,354', '1,R_ct,154']
2025-06-28 13:17:59,083 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,510', '1,R_sei,354', '1,R_ct,154']
2025-06-28 13:17:59,083 - LVER - DEBUG - 添加测试数据: 测试#81, 电压=3290.0mV
2025-06-28 13:18:00,522 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:01,110 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:01,619 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:01,619 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:18:01,619 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:18:01,620 - LVER - DEBUG - 收集CSV行: '1,R_ct,150'
2025-06-28 13:18:01,670 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:01,671 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,333', '1,R_ct,150']
2025-06-28 13:18:01,671 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,333', '1,R_ct,150']
2025-06-28 13:18:01,671 - LVER - DEBUG - 添加测试数据: 测试#82, 电压=3290.0mV
2025-06-28 13:18:02,536 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:03,688 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:04,195 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:04,195 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:18:04,195 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:18:04,246 - LVER - DEBUG - 收集CSV行: '1,R_ct,150'
2025-06-28 13:18:04,297 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:04,297 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,150']
2025-06-28 13:18:04,297 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,150']
2025-06-28 13:18:04,297 - LVER - DEBUG - 添加测试数据: 测试#83, 电压=3290.0mV
2025-06-28 13:18:04,549 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:06,316 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:06,562 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:06,825 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:06,825 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:18:06,825 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:18:06,826 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:18:06,876 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:06,876 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,137']
2025-06-28 13:18:06,876 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,137']
2025-06-28 13:18:06,876 - LVER - DEBUG - 添加测试数据: 测试#84, 电压=3290.0mV
2025-06-28 13:18:08,576 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:08,892 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:09,399 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:09,400 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:18:09,400 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:18:09,450 - LVER - DEBUG - 收集CSV行: '1,R_ct,145'
2025-06-28 13:18:09,501 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:09,501 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,145']
2025-06-28 13:18:09,501 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,145']
2025-06-28 13:18:09,501 - LVER - DEBUG - 添加测试数据: 测试#85, 电压=3290.0mV
2025-06-28 13:18:10,589 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:11,519 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:12,027 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:12,028 - LVER - DEBUG - 收集CSV行: '1,R_ohm,539'
2025-06-28 13:18:12,028 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:18:12,028 - LVER - DEBUG - 收集CSV行: '1,R_ct,158'
2025-06-28 13:18:12,079 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:12,079 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,539', '1,R_sei,325', '1,R_ct,158']
2025-06-28 13:18:12,079 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,539', '1,R_sei,325', '1,R_ct,158']
2025-06-28 13:18:12,079 - LVER - DEBUG - 添加测试数据: 测试#86, 电压=3290.0mV
2025-06-28 13:18:12,604 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:14,098 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:14,605 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:14,605 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:18:14,606 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:18:14,617 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:14,656 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:18:14,706 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:14,707 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,137']
2025-06-28 13:18:14,707 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,137']
2025-06-28 13:18:14,707 - LVER - DEBUG - 添加测试数据: 测试#87, 电压=3290.0mV
2025-06-28 13:18:16,630 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:16,722 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:17,231 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:17,231 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:18:17,231 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:18:17,232 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:18:17,282 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:17,282 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,154']
2025-06-28 13:18:17,282 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,154']
2025-06-28 13:18:17,283 - LVER - DEBUG - 添加测试数据: 测试#88, 电压=3290.0mV
2025-06-28 13:18:18,643 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:19,309 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:19,817 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:19,817 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:18:19,817 - LVER - DEBUG - 收集CSV行: '1,R_sei,341'
2025-06-28 13:18:19,868 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:18:19,919 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:19,919 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,341', '1,R_ct,129']
2025-06-28 13:18:19,920 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,341', '1,R_ct,129']
2025-06-28 13:18:19,920 - LVER - DEBUG - 添加测试数据: 测试#89, 电压=3290.0mV
2025-06-28 13:18:20,657 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:21,936 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:22,445 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:22,446 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:18:22,446 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:18:22,446 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:18:22,496 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:22,497 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,125']
2025-06-28 13:18:22,497 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,125']
2025-06-28 13:18:22,497 - LVER - DEBUG - 添加测试数据: 测试#90, 电压=3290.0mV
2025-06-28 13:18:22,670 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:24,524 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:24,683 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:25,033 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:25,034 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:18:25,034 - LVER - DEBUG - 收集CSV行: '1,R_sei,341'
2025-06-28 13:18:25,034 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:18:25,085 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:25,085 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,341', '1,R_ct,133']
2025-06-28 13:18:25,085 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,341', '1,R_ct,133']
2025-06-28 13:18:25,085 - LVER - DEBUG - 添加测试数据: 测试#91, 电压=3290.0mV
2025-06-28 13:18:26,696 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:27,114 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:27,621 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:27,621 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:18:27,621 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:18:27,672 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:18:27,722 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:27,722 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,337', '1,R_ct,137']
2025-06-28 13:18:27,723 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,337', '1,R_ct,137']
2025-06-28 13:18:27,723 - LVER - DEBUG - 添加测试数据: 测试#92, 电压=3290.0mV
2025-06-28 13:18:28,710 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:29,740 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:30,247 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:30,247 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:18:30,247 - LVER - DEBUG - 收集CSV行: '1,R_sei,341'
2025-06-28 13:18:30,299 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:18:30,349 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:30,350 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,341', '1,R_ct,125']
2025-06-28 13:18:30,350 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,341', '1,R_ct,125']
2025-06-28 13:18:30,350 - LVER - DEBUG - 添加测试数据: 测试#93, 电压=3290.0mV
2025-06-28 13:18:30,723 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:32,371 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:32,737 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:32,878 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:32,878 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:18:32,878 - LVER - DEBUG - 收集CSV行: '1,R_sei,312'
2025-06-28 13:18:32,929 - LVER - DEBUG - 收集CSV行: '1,R_ct,116'
2025-06-28 13:18:32,980 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:32,980 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,312', '1,R_ct,116']
2025-06-28 13:18:32,980 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,312', '1,R_ct,116']
2025-06-28 13:18:32,980 - LVER - DEBUG - 添加测试数据: 测试#94, 电压=3290.0mV
2025-06-28 13:18:34,750 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:34,997 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:35,505 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:35,505 - LVER - DEBUG - 收集CSV行: '1,R_ohm,527'
2025-06-28 13:18:35,505 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:18:35,556 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:18:35,606 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:35,607 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,137']
2025-06-28 13:18:35,607 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,527', '1,R_sei,325', '1,R_ct,137']
2025-06-28 13:18:35,607 - LVER - DEBUG - 添加测试数据: 测试#95, 电压=3290.0mV
2025-06-28 13:18:36,763 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:37,625 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:38,134 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:38,134 - LVER - DEBUG - 收集CSV行: '1,R_ohm,514'
2025-06-28 13:18:38,134 - LVER - DEBUG - 收集CSV行: '1,R_sei,350'
2025-06-28 13:18:38,134 - LVER - DEBUG - 收集CSV行: '1,R_ct,133'
2025-06-28 13:18:38,185 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:38,185 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,514', '1,R_sei,350', '1,R_ct,133']
2025-06-28 13:18:38,185 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,514', '1,R_sei,350', '1,R_ct,133']
2025-06-28 13:18:38,185 - LVER - DEBUG - 添加测试数据: 测试#96, 电压=3290.0mV
2025-06-28 13:18:38,777 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:40,203 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:40,708 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:40,709 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:18:40,760 - LVER - DEBUG - 收集CSV行: '1,R_sei,333'
2025-06-28 13:18:40,760 - LVER - DEBUG - 收集CSV行: '1,R_ct,145'
2025-06-28 13:18:40,790 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:40,811 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:40,811 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,145']
2025-06-28 13:18:40,811 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,333', '1,R_ct,145']
2025-06-28 13:18:40,811 - LVER - DEBUG - 添加测试数据: 测试#97, 电压=3290.0mV
2025-06-28 13:18:42,804 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:42,830 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:43,337 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:43,338 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:18:43,338 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:18:43,389 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:18:43,439 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:43,439 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,137']
2025-06-28 13:18:43,439 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,325', '1,R_ct,137']
2025-06-28 13:18:43,439 - LVER - DEBUG - 添加测试数据: 测试#98, 电压=3290.0mV
2025-06-28 13:18:44,817 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:45,453 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:45,961 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:45,962 - LVER - DEBUG - 收集CSV行: '1,R_ohm,539'
2025-06-28 13:18:45,962 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:18:45,962 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:18:46,013 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:46,013 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,539', '1,R_sei,320', '1,R_ct,129']
2025-06-28 13:18:46,013 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,539', '1,R_sei,320', '1,R_ct,129']
2025-06-28 13:18:46,013 - LVER - DEBUG - 添加测试数据: 测试#99, 电压=3290.0mV
2025-06-28 13:18:46,830 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:48,031 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:48,539 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:48,539 - LVER - DEBUG - 收集CSV行: '1,R_ohm,514'
2025-06-28 13:18:48,539 - LVER - DEBUG - 收集CSV行: '1,R_sei,350'
2025-06-28 13:18:48,590 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:18:48,641 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:48,641 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,514', '1,R_sei,350', '1,R_ct,154']
2025-06-28 13:18:48,641 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,514', '1,R_sei,350', '1,R_ct,154']
2025-06-28 13:18:48,641 - LVER - DEBUG - 添加测试数据: 测试#100, 电压=3290.0mV
2025-06-28 13:18:48,843 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:50,657 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:50,857 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:51,165 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:51,165 - LVER - DEBUG - 收集CSV行: '1,R_ohm,518'
2025-06-28 13:18:51,165 - LVER - DEBUG - 收集CSV行: '1,R_sei,345'
2025-06-28 13:18:51,216 - LVER - DEBUG - 收集CSV行: '1,R_ct,120'
2025-06-28 13:18:51,267 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:51,267 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,518', '1,R_sei,345', '1,R_ct,120']
2025-06-28 13:18:51,267 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,518', '1,R_sei,345', '1,R_ct,120']
2025-06-28 13:18:51,268 - LVER - DEBUG - 添加测试数据: 测试#101, 电压=3290.0mV
2025-06-28 13:18:52,875 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:53,284 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:53,792 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:53,792 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:18:53,793 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:18:53,793 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:18:53,843 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:53,844 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,329', '1,R_ct,137']
2025-06-28 13:18:53,844 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,329', '1,R_ct,137']
2025-06-28 13:18:53,844 - LVER - DEBUG - 添加测试数据: 测试#102, 电压=3290.0mV
2025-06-28 13:18:54,889 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:55,860 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:56,368 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:56,369 - LVER - DEBUG - 收集CSV行: '1,R_ohm,552'
2025-06-28 13:18:56,369 - LVER - DEBUG - 收集CSV行: '1,R_sei,308'
2025-06-28 13:18:56,369 - LVER - DEBUG - 收集CSV行: '1,R_ct,116'
2025-06-28 13:18:56,419 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:56,419 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,552', '1,R_sei,308', '1,R_ct,116']
2025-06-28 13:18:56,420 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,552', '1,R_sei,308', '1,R_ct,116']
2025-06-28 13:18:56,420 - LVER - DEBUG - 添加测试数据: 测试#103, 电压=3290.0mV
2025-06-28 13:18:56,902 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:58,435 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:18:58,914 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:18:58,941 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:18:58,941 - LVER - DEBUG - 收集CSV行: '1,R_ohm,514'
2025-06-28 13:18:58,941 - LVER - DEBUG - 收集CSV行: '1,R_sei,341'
2025-06-28 13:18:58,993 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:18:59,044 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:18:59,045 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,514', '1,R_sei,341', '1,R_ct,125']
2025-06-28 13:18:59,045 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,514', '1,R_sei,341', '1,R_ct,125']
2025-06-28 13:18:59,045 - LVER - DEBUG - 添加测试数据: 测试#104, 电压=3290.0mV
2025-06-28 13:19:00,928 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:01,064 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:19:01,570 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:19:01,570 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:19:01,570 - LVER - DEBUG - 收集CSV行: '1,R_sei,320'
2025-06-28 13:19:01,621 - LVER - DEBUG - 收集CSV行: '1,R_ct,150'
2025-06-28 13:19:01,672 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:19:01,672 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,150']
2025-06-28 13:19:01,673 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,320', '1,R_ct,150']
2025-06-28 13:19:01,673 - LVER - DEBUG - 添加测试数据: 测试#105, 电压=3290.0mV
2025-06-28 13:19:02,941 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:03,690 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:19:04,197 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:19:04,197 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:19:04,198 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:19:04,249 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:19:04,300 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:19:04,300 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,337', '1,R_ct,125']
2025-06-28 13:19:04,300 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,337', '1,R_ct,125']
2025-06-28 13:19:04,300 - LVER - DEBUG - 添加测试数据: 测试#106, 电压=3290.0mV
2025-06-28 13:19:04,955 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:06,320 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:19:06,830 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:19:06,830 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:19:06,830 - LVER - DEBUG - 收集CSV行: '1,R_sei,341'
2025-06-28 13:19:06,830 - LVER - DEBUG - 收集CSV行: '1,R_ct,120'
2025-06-28 13:19:06,881 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:19:06,881 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,341', '1,R_ct,120']
2025-06-28 13:19:06,881 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,341', '1,R_ct,120']
2025-06-28 13:19:06,881 - LVER - DEBUG - 添加测试数据: 测试#107, 电压=3290.0mV
2025-06-28 13:19:06,969 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:08,898 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:19:08,982 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:09,407 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:19:09,407 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:19:09,407 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:19:09,407 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:19:09,458 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:19:09,458 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,325', '1,R_ct,129']
2025-06-28 13:19:09,458 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,325', '1,R_ct,129']
2025-06-28 13:19:09,458 - LVER - DEBUG - 添加测试数据: 测试#108, 电压=3290.0mV
2025-06-28 13:19:10,995 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:11,475 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:19:11,984 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:19:11,984 - LVER - DEBUG - 收集CSV行: '1,R_ohm,522'
2025-06-28 13:19:11,984 - LVER - DEBUG - 收集CSV行: '1,R_sei,341'
2025-06-28 13:19:11,984 - LVER - DEBUG - 收集CSV行: '1,R_ct,116'
2025-06-28 13:19:12,035 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:19:12,035 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,522', '1,R_sei,341', '1,R_ct,116']
2025-06-28 13:19:12,035 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,522', '1,R_sei,341', '1,R_ct,116']
2025-06-28 13:19:12,035 - LVER - DEBUG - 添加测试数据: 测试#109, 电压=3290.0mV
2025-06-28 13:19:13,009 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:14,052 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:19:14,558 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:19:14,559 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:19:14,559 - LVER - DEBUG - 收集CSV行: '1,R_sei,325'
2025-06-28 13:19:14,610 - LVER - DEBUG - 收集CSV行: '1,R_ct,154'
2025-06-28 13:19:14,660 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:19:14,661 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,325', '1,R_ct,154']
2025-06-28 13:19:14,661 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,325', '1,R_ct,154']
2025-06-28 13:19:14,661 - LVER - DEBUG - 添加测试数据: 测试#110, 电压=3290.0mV
2025-06-28 13:19:15,023 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:16,677 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:19:17,035 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:17,185 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:19:17,185 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:19:17,185 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:19:17,185 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:19:17,236 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:19:17,236 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,141']
2025-06-28 13:19:17,236 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,141']
2025-06-28 13:19:17,236 - LVER - DEBUG - 添加测试数据: 测试#111, 电压=3290.0mV
2025-06-28 13:19:19,048 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:19,254 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:19:19,762 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:19:19,763 - LVER - DEBUG - 收集CSV行: '1,R_ohm,543'
2025-06-28 13:19:19,763 - LVER - DEBUG - 收集CSV行: '1,R_sei,304'
2025-06-28 13:19:19,763 - LVER - DEBUG - 收集CSV行: '1,R_ct,150'
2025-06-28 13:19:19,814 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:19:19,814 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,543', '1,R_sei,304', '1,R_ct,150']
2025-06-28 13:19:19,814 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,543', '1,R_sei,304', '1,R_ct,150']
2025-06-28 13:19:19,814 - LVER - DEBUG - 添加测试数据: 测试#112, 电压=3290.0mV
2025-06-28 13:19:21,062 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:21,830 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:19:22,338 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:19:22,339 - LVER - DEBUG - 收集CSV行: '1,R_ohm,535'
2025-06-28 13:19:22,339 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:19:22,390 - LVER - DEBUG - 收集CSV行: '1,R_ct,125'
2025-06-28 13:19:22,441 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:19:22,441 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,125']
2025-06-28 13:19:22,441 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,535', '1,R_sei,329', '1,R_ct,125']
2025-06-28 13:19:22,441 - LVER - DEBUG - 添加测试数据: 测试#113, 电压=3290.0mV
2025-06-28 13:19:23,076 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:24,472 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:19:24,979 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:19:24,980 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:19:24,980 - LVER - DEBUG - 收集CSV行: '1,R_sei,316'
2025-06-28 13:19:25,031 - LVER - DEBUG - 收集CSV行: '1,R_ct,112'
2025-06-28 13:19:25,081 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:19:25,082 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,316', '1,R_ct,112']
2025-06-28 13:19:25,082 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,316', '1,R_ct,112']
2025-06-28 13:19:25,082 - LVER - DEBUG - 添加测试数据: 测试#114, 电压=3290.0mV
2025-06-28 13:19:25,089 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:27,100 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:19:27,107 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:27,607 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:19:27,607 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:19:27,607 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:19:27,658 - LVER - DEBUG - 收集CSV行: '1,R_ct,141'
2025-06-28 13:19:27,709 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:19:27,709 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,141']
2025-06-28 13:19:27,709 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,141']
2025-06-28 13:19:27,710 - LVER - DEBUG - 添加测试数据: 测试#115, 电压=3290.0mV
2025-06-28 13:19:29,121 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:29,727 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:19:30,235 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:19:30,236 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:19:30,236 - LVER - DEBUG - 收集CSV行: '1,R_sei,329'
2025-06-28 13:19:30,236 - LVER - DEBUG - 收集CSV行: '1,R_ct,129'
2025-06-28 13:19:30,286 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:19:30,286 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,129']
2025-06-28 13:19:30,287 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,329', '1,R_ct,129']
2025-06-28 13:19:30,287 - LVER - DEBUG - 添加测试数据: 测试#116, 电压=3290.0mV
2025-06-28 13:19:31,134 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
2025-06-28 13:19:32,306 - LVER - DEBUG - 发送测试命令 0xAA
2025-06-28 13:19:32,815 - LVER - DEBUG - 收集CSV行: '1,V,3290'
2025-06-28 13:19:32,815 - LVER - DEBUG - 收集CSV行: '1,R_ohm,531'
2025-06-28 13:19:32,815 - LVER - DEBUG - 收集CSV行: '1,R_sei,337'
2025-06-28 13:19:32,815 - LVER - DEBUG - 收集CSV行: '1,R_ct,137'
2025-06-28 13:19:32,865 - LVER - DEBUG - 总共接收到 4 行响应
2025-06-28 13:19:32,865 - LVER - DEBUG - 其中CSV行 4 行: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,137']
2025-06-28 13:19:32,866 - LVER - DEBUG - 测试命令响应: ['1,V,3290', '1,R_ohm,531', '1,R_sei,337', '1,R_ct,137']
2025-06-28 13:19:32,866 - LVER - DEBUG - 添加测试数据: 测试#117, 电压=3290.0mV
2025-06-28 13:19:33,148 - LVER - DEBUG - 发现 2 个串口: ['COM5', 'COM1']
