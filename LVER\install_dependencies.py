#!/usr/bin/env python3
"""
LVER 依赖项安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        print(f"正在安装 {package}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package
        ], capture_output=True, text=True, check=True)
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {package} 安装失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✓ {package_name} 已安装")
        return True
    except ImportError:
        print(f"✗ {package_name} 未安装")
        return False

def main():
    """主函数"""
    print("LVER 串口通信应用程序 - 依赖项安装")
    print("=" * 50)
    
    # 检查当前Python版本
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print()
    
    # 需要安装的包
    packages = [
        ("pyserial", "serial"),
        ("matplotlib", "matplotlib")
    ]
    
    # 检查已安装的包
    print("检查已安装的包:")
    installed = []
    missing = []
    
    for package, import_name in packages:
        if check_package(package, import_name):
            installed.append(package)
        else:
            missing.append(package)
    
    print()
    
    if not missing:
        print("所有依赖项都已安装！")
        print("您可以运行 'python main.py' 启动应用程序")
        return
    
    # 安装缺失的包
    print(f"需要安装的包: {', '.join(missing)}")
    print()
    
    # 尝试从requirements.txt安装
    if os.path.exists("requirements.txt"):
        print("尝试从 requirements.txt 安装依赖项...")
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
            ], capture_output=True, text=True, check=True)
            print("✓ 从 requirements.txt 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"✗ 从 requirements.txt 安装失败: {e}")
            print("尝试单独安装每个包...")
            
            # 单独安装每个包
            for package in missing:
                install_package(package)
    else:
        # 单独安装每个包
        for package in missing:
            install_package(package)
    
    print()
    print("安装完成！")
    
    # 再次检查
    print("验证安装结果:")
    all_installed = True
    for package, import_name in packages:
        if not check_package(package, import_name):
            all_installed = False
    
    print()
    if all_installed:
        print("✓ 所有依赖项安装成功！")
        print("您现在可以运行 'python main.py' 启动应用程序")
    else:
        print("✗ 部分依赖项安装失败")
        print("请手动安装缺失的依赖项:")
        for package in missing:
            print(f"  pip install {package}")

if __name__ == "__main__":
    main()
