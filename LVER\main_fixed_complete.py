#!/usr/bin/env python3
"""
鲸测云LCER电池测试仪 - 完全修复版
修复内容:
1. 图表显示功能完全修复
2. 数据导出功能完全修复 (解决 initialname 参数错误)
3. 多参数实时显示优化
4. 错误处理增强
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import time
import random
import csv
from datetime import datetime
import re

# 尝试导入pyserial
try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
    print("✓ pyserial 可用")
except ImportError:
    SERIAL_AVAILABLE = False
    print("⚠️  pyserial 不可用，使用内置模拟")
    
    # 模拟serial模块
    class MockSerial:
        PARITY_NONE = 'N'
        STOPBITS_ONE = 1
        
        def __init__(self, port=None, baudrate=9600, timeout=None, **kwargs):
            self.port = port
            self.baudrate = baudrate
            self.timeout = timeout
            self.is_open = False
            
        def open(self):
            self.is_open = True
            print(f"模拟连接到 {self.port}")
            
        def close(self):
            self.is_open = False
            print(f"模拟断开 {self.port}")
            
        def write(self, data):
            print(f"模拟发送: {data}")
            return len(data)
            
        def read(self, size=1):
            return b''
            
        def readline(self):
            return b''
    
    # 模拟串口列表
    class MockListPorts:
        @staticmethod
        def comports():
            return [
                type('ComPort', (), {'device': 'COM3', 'description': '模拟串口3'})(),
                type('ComPort', (), {'device': 'COM5', 'description': '模拟串口5 (鲸测云LCER设备)'})(),
            ]
    
    # 创建模拟模块
    serial = type('serial', (), {
        'Serial': MockSerial,
        'PARITY_NONE': 'N',
        'STOPBITS_ONE': 1,
        'tools': type('tools', (), {
            'list_ports': MockListPorts
        })()
    })()

# 尝试导入matplotlib
try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
    MATPLOTLIB_AVAILABLE = True
    print("✓ matplotlib 可用")
except ImportError:
    MATPLOTLIB_AVAILABLE = False
    print("⚠️  matplotlib 不可用，使用内置图表")

class JingCeYunLCERApp:
    def __init__(self, root):
        self.root = root
        self.root.title("🔋 鲸测云LCER电池测试仪")
        self.root.geometry("1400x1000")
        
        # 数据存储
        self.test_data = []
        self.current_values = {
            'V': '--',
            'R_sei': '--',
            'R_ct': '--', 
            'R_ohm': '--'
        }
        
        # 串口相关
        self.serial_connection = None
        self.is_connected = False
        self.continuous_testing = False
        self.test_thread = None

        # 初始化数据处理器
        self.data_buffer = ""
        self.last_measurements = {}
        self.debug_mode = True

        # 创建界面
        self.create_widgets()
        self.refresh_ports()

        print("✓ 鲸测云LCER电池测试仪应用程序初始化完成")
    
    def create_widgets(self):
        """创建界面组件"""
        # 串口连接区域
        self.create_connection_frame()
        
        # 测试控制区域
        self.create_control_frame()
        
        # 实时数据显示区域
        self.create_realtime_display_frame()
        
        # 图表和数据区域
        self.create_chart_and_data_frame()
    
    def create_connection_frame(self):
        """创建串口连接控制区域"""
        conn_frame = ttk.LabelFrame(self.root, text="串口连接", padding=10)
        conn_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # 串口选择
        ttk.Label(conn_frame, text="串口:").pack(side=tk.LEFT, padx=(0, 5))
        self.port_var = tk.StringVar()
        self.port_combo = ttk.Combobox(conn_frame, textvariable=self.port_var, width=15)
        self.port_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # 波特率选择
        ttk.Label(conn_frame, text="波特率:").pack(side=tk.LEFT, padx=(0, 5))
        self.baudrate_var = tk.StringVar(value="115200")
        baudrate_combo = ttk.Combobox(conn_frame, textvariable=self.baudrate_var, 
                                     values=["9600", "19200", "38400", "57600", "115200"], width=10)
        baudrate_combo.pack(side=tk.LEFT, padx=(0, 10))
        
        # 连接按钮
        self.connect_btn = ttk.Button(conn_frame, text="连接", command=self.toggle_connection)
        self.connect_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 刷新按钮
        ttk.Button(conn_frame, text="刷新", command=self.refresh_ports).pack(side=tk.LEFT, padx=(0, 10))
        
        # 连接状态
        self.status_label = ttk.Label(conn_frame, text="未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT, padx=(10, 0))
    
    def create_control_frame(self):
        """创建测试控制区域"""
        control_frame = ttk.LabelFrame(self.root, text="测试控制", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # 测试按钮
        ttk.Button(control_frame, text="测试命令",
                  command=self.send_test_command).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="电压测量",
                  command=self.send_voltage_command).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(control_frame, text="复位命令",
                  command=self.send_reset_command).pack(side=tk.LEFT, padx=(0, 10))
        
        # 连续测试
        self.continuous_var = tk.BooleanVar()
        ttk.Checkbutton(control_frame, text="连续测试", 
                       variable=self.continuous_var, 
                       command=self.toggle_continuous_testing).pack(side=tk.LEFT, padx=(20, 10))
        
        # 测试间隔
        ttk.Label(control_frame, text="间隔(秒):").pack(side=tk.LEFT, padx=(0, 5))
        self.interval_var = tk.StringVar(value="1.0")
        interval_spin = ttk.Spinbox(control_frame, from_=0.5, to=10.0, increment=0.5,
                                   textvariable=self.interval_var, width=8)
        interval_spin.pack(side=tk.LEFT, padx=(0, 10))
        
        # 数据管理
        export_menu_btn = ttk.Menubutton(control_frame, text="导出数据 ▼")
        export_menu_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # 创建导出菜单
        export_menu = tk.Menu(export_menu_btn, tearoff=0)
        export_menu_btn.config(menu=export_menu)
        export_menu.add_command(label="📄 统一导出 (单文件)", command=self.export_data_unified)
        export_menu.add_command(label="📁 分类导出 (多文件)", command=self.export_data_classified)
        export_menu.add_separator()
        export_menu.add_command(label="Excel表格导出 (单表格式)", command=self.export_data_excel_single)
        export_menu.add_command(label="Excel格式导出 (多工作表)", command=self.export_data_excel)

        ttk.Button(control_frame, text="清空数据",
                  command=self.clear_data).pack(side=tk.RIGHT, padx=(10, 0))
    
    def create_realtime_display_frame(self):
        """创建实时数据显示区域"""
        display_frame = ttk.LabelFrame(self.root, text="实时多参数显示", padding=10)
        display_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # 创建2x3网格布局
        grid_frame = ttk.Frame(display_frame)
        grid_frame.pack(fill=tk.X)
        
        # 配置网格权重
        for i in range(3):
            grid_frame.columnconfigure(i, weight=1)
        for i in range(2):
            grid_frame.rowconfigure(i, weight=1)
        
        # 创建数据卡片
        self.voltage_card = self.create_data_card(grid_frame, "电压 (V)", "mV", "blue", 0, 0)
        self.r_sei_card = self.create_data_card(grid_frame, "SEI电阻", "μΩ", "green", 0, 1)
        self.r_ct_card = self.create_data_card(grid_frame, "CT电阻", "μΩ", "purple", 0, 2)
        self.r_ohm_card = self.create_data_card(grid_frame, "欧姆电阻", "μΩ", "red", 1, 0)
        self.count_card = self.create_data_card(grid_frame, "测试次数", "次", "orange", 1, 1)
        self.status_card = self.create_data_card(grid_frame, "连接状态", "", "gray", 1, 2)
        
        # 初始化状态卡片
        self.count_card.value_label.config(text="0")
        self.status_card.value_label.config(text="未连接")
    
    def create_data_card(self, parent, title, unit, color, row, col):
        """创建数据显示卡片"""
        card_frame = ttk.LabelFrame(parent, text=title, padding=10)
        card_frame.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")
        
        # 数值显示
        value_label = tk.Label(card_frame, text="--", font=("Arial", 24, "bold"), 
                              fg=color, bg="white", relief="sunken", bd=2)
        value_label.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # 单位显示
        if unit:
            unit_label = tk.Label(card_frame, text=unit, font=("Arial", 12))
            unit_label.pack()
        
        # 返回卡片对象
        card = type('Card', (), {})()
        card.frame = card_frame
        card.value_label = value_label
        return card
    
    def create_chart_and_data_frame(self):
        """创建图表和数据显示区域 - 左右分栏布局"""
        # 使用PanedWindow分割图表和数据表格
        paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))

        # 左侧：实时曲线图区域 (70%空间)
        chart_frame = ttk.LabelFrame(paned, text="实时曲线图", padding=5)
        paned.add(chart_frame, weight=7)

        if MATPLOTLIB_AVAILABLE:
            self.create_matplotlib_chart(chart_frame)
        else:
            self.create_simple_chart(chart_frame)

        # 右侧：数据记录表格 (30%空间)
        data_frame = ttk.LabelFrame(paned, text="数据记录表格", padding=5)
        paned.add(data_frame, weight=3)

        # 创建数据记录表格 - 按时间组织的格式
        self.create_data_record_table(data_frame)

    def create_data_record_table(self, parent):
        """创建数据记录表格 - 按时间组织的格式"""
        # 创建表格容器
        table_container = ttk.Frame(parent)
        table_container.pack(fill=tk.BOTH, expand=True)

        # 数据表格 - 新的列结构：时间 | V电压(mV) | R_sei电阻(μΩ) | R_ct电阻(μΩ) | R_ohm电阻(μΩ)
        columns = ('时间', 'V电压(mV)', 'R_sei电阻(μΩ)', 'R_ct电阻(μΩ)', 'R_ohm电阻(μΩ)')
        self.data_tree = ttk.Treeview(table_container, columns=columns, show='headings', height=15)

        # 设置列标题和宽度
        column_widths = {
            '时间': 90,
            'V电压(mV)': 85,
            'R_sei电阻(μΩ)': 100,
            'R_ct电阻(μΩ)': 95,
            'R_ohm电阻(μΩ)': 105
        }

        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=column_widths[col], anchor='center')

        # 滚动条
        scrollbar = ttk.Scrollbar(table_container, orient=tk.VERTICAL, command=self.data_tree.yview)
        self.data_tree.configure(yscrollcommand=scrollbar.set)

        # 布局
        self.data_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 添加表格操作按钮
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Button(button_frame, text="复制选中", command=self.copy_selected_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空表格", command=self.clear_table_data).pack(side=tk.LEFT, padx=(0, 5))

        # 初始化时间数据字典，用于按时间组织数据
        self.time_data_dict = {}

    def copy_selected_data(self):
        """复制选中的表格数据到剪贴板"""
        try:
            selected_items = self.data_tree.selection()
            if not selected_items:
                messagebox.showinfo("提示", "请先选择要复制的数据行")
                return

            # 获取表头
            columns = self.data_tree['columns']
            header = '\t'.join(columns)

            # 获取选中的数据
            data_lines = [header]
            for item in selected_items:
                values = self.data_tree.item(item)['values']
                data_lines.append('\t'.join(str(v) for v in values))

            # 复制到剪贴板
            clipboard_text = '\n'.join(data_lines)
            self.root.clipboard_clear()
            self.root.clipboard_append(clipboard_text)

            messagebox.showinfo("成功", f"已复制 {len(selected_items)} 行数据到剪贴板")

        except Exception as e:
            messagebox.showerror("错误", f"复制数据失败: {str(e)}")

    def clear_table_data(self):
        """清空表格数据"""
        try:
            result = messagebox.askyesno("确认", "确定要清空所有表格数据吗？\n\n此操作不可撤销。")
            if result:
                # 清空表格
                for item in self.data_tree.get_children():
                    self.data_tree.delete(item)

                # 清空时间数据字典
                self.time_data_dict.clear()

                messagebox.showinfo("成功", "表格数据已清空")

        except Exception as e:
            messagebox.showerror("错误", f"清空数据失败: {str(e)}")
    
    def create_matplotlib_chart(self, parent):
        """创建matplotlib图表 - 完全修复版"""
        try:
            # 创建图表容器
            chart_container = ttk.Frame(parent)
            chart_container.pack(fill=tk.BOTH, expand=True)
            
            # 创建matplotlib图表
            self.fig = plt.figure(figsize=(12, 8), facecolor='white')
            self.ax = self.fig.add_subplot(111)
            
            # 创建画布
            self.canvas = FigureCanvasTkAgg(self.fig, chart_container)
            self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            # 添加工具栏
            toolbar = NavigationToolbar2Tk(self.canvas, chart_container)
            toolbar.update()
            
            # 初始化图表样式
            self.ax.set_title("实时曲线图", fontsize=16, fontweight='bold', pad=20)
            self.ax.set_xlabel("测量序号", fontsize=12)
            self.ax.set_ylabel("测量值", fontsize=12)
            self.ax.grid(True, alpha=0.3, linestyle='--')
            self.ax.set_facecolor('#f8f9fa')
            
            # 显示初始状态
            self.ax.text(0.5, 0.5, '等待测量数据...\n\n点击"测试命令"开始测量',
                        horizontalalignment='center', verticalalignment='center',
                        transform=self.ax.transAxes, fontsize=14,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.5))
            
            self.fig.tight_layout(pad=3.0)
            self.canvas.draw()
            
            print("✓ matplotlib图表创建成功")
            
        except Exception as e:
            print(f"❌ matplotlib图表创建失败: {e}")
            self.create_simple_chart(parent)
    
    def create_simple_chart(self, parent):
        """创建简单文本图表 - 备用方案"""
        self.chart_text = tk.Text(parent, height=20, font=("Consolas", 10), 
                                 bg="white", relief="sunken", bd=2)
        self.chart_text.pack(fill=tk.BOTH, expand=True)
        
        initial_text = """
多参数测量数据显示

当前状态: 等待测量数据...

功能说明:
- 实时显示测量数据统计
- 显示最新测量记录
- 支持多参数数据分析

测试说明:
1. 点击"测试命令"获取4种参数
2. 点击"电压测量"获取电压值
3. 启用"连续测试"进行自动监控

数据类型:
• V: 电压 (mV)
• R_sei: SEI电阻 (μΩ)
• R_ct: CT电阻 (μΩ)
• R_ohm: 欧姆电阻 (μΩ)
"""
        self.chart_text.insert(1.0, initial_text)
        self.chart_text.config(state=tk.DISABLED)
        
        print("✓ 文本图表创建成功")

    def refresh_ports(self):
        """刷新串口列表"""
        try:
            ports = []
            for port in serial.tools.list_ports.comports():
                ports.append(port.device)

            # 确保COM5在列表中
            if 'COM5' not in ports:
                ports.append('COM5')

            self.port_combo['values'] = ports
            if ports:
                if 'COM5' in ports:
                    self.port_var.set('COM5')
                else:
                    self.port_var.set(ports[0])

            print(f"[{datetime.now().strftime('%H:%M:%S')}] 串口列表已刷新，发现 {len(ports)} 个串口")

        except Exception as e:
            print(f"刷新串口列表失败: {e}")

    def toggle_connection(self):
        """切换串口连接状态"""
        if self.is_connected:
            self.disconnect()
        else:
            self.connect()

    def connect(self):
        """连接串口"""
        try:
            port = self.port_var.get()
            baudrate = int(self.baudrate_var.get())

            if not port:
                messagebox.showerror("错误", "请选择串口")
                return

            self.serial_connection = serial.Serial(
                port=port,
                baudrate=baudrate,
                timeout=1,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE
            )

            if not SERIAL_AVAILABLE:
                self.serial_connection.open()

            self.is_connected = True
            self.connect_btn.config(text="断开")
            self.status_label.config(text=f"已连接 - {port}", foreground="green")
            self.status_card.value_label.config(text="已连接", fg="green")

            print(f"[{datetime.now().strftime('%H:%M:%S')}] ✓ 成功连接到 {port}, 波特率: {baudrate}")

        except Exception as e:
            messagebox.showerror("连接失败", f"无法连接到串口: {e}")
            print(f"连接失败: {e}")

    def disconnect(self):
        """断开串口连接"""
        try:
            if self.serial_connection:
                self.serial_connection.close()
                self.serial_connection = None

            self.is_connected = False
            self.connect_btn.config(text="连接")
            self.status_label.config(text="未连接", foreground="red")
            self.status_card.value_label.config(text="未连接", fg="red")

            # 停止连续测试
            if self.continuous_testing:
                self.continuous_var.set(False)
                self.toggle_continuous_testing()

            print(f"[{datetime.now().strftime('%H:%M:%S')}] 串口连接已断开")

        except Exception as e:
            print(f"断开连接失败: {e}")

    def send_test_command(self):
        """发送测试命令 (0xAA) - 返回4种参数 - 修复版"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接串口")
            return

        try:
            # 发送真实命令到串口
            command = b'\xAA'
            if self.serial_connection:
                self.serial_connection.write(command)
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 发送测试命令: 0xAA")

                # 处理真实串口响应数据
                measurements = self.process_serial_response(timeout=3.0)

                if measurements:
                    for measurement in measurements:
                        measure_type = measurement['param_type']
                        value = measurement['param_value']
                        self.add_measurement_data(measure_type, value)
                        print(f"✓ {measure_type}: {value}")

                    print(f"[{datetime.now().strftime('%H:%M:%S')}] ✓ 测试命令完成: {len(measurements)}个参数已更新")
                else:
                    print("警告: 未接收到有效的测量数据")
            else:
                # 如果没有真实串口连接，使用模拟数据（仅用于开发测试）
                print("警告: 使用模拟数据 - 请连接真实设备获取准确数据")
                measurements = [
                    ("R_sei", 281),  # 使用固定值而非随机值
                    ("R_ct", 165),
                    ("V", 3290),     # 使用参考值3290
                    ("R_ohm", 502)
                ]

                for measure_type, value in measurements:
                    self.add_measurement_data(measure_type, value)

        except Exception as e:
            messagebox.showerror("命令失败", f"发送测试命令失败: {e}")
            print(f"测试命令失败: {e}")

    def process_serial_response(self, timeout=3.0):
        """
        处理串口响应数据 - 核心修复函数
        替换原有的模拟数据生成逻辑
        """
        if not self.serial_connection or not self.serial_connection.is_open:
            return []

        start_time = time.time()
        response_data = ""

        # 读取串口数据直到超时
        while time.time() - start_time < timeout:
            if self.serial_connection.in_waiting > 0:
                try:
                    chunk = self.serial_connection.read(self.serial_connection.in_waiting).decode('utf-8', errors='ignore')
                    response_data += chunk

                    if self.debug_mode:
                        print(f"[DEBUG] 接收数据块: {repr(chunk)}")

                    # 检查是否接收完整
                    if self._is_response_complete(response_data):
                        break

                except Exception as e:
                    print(f"串口读取错误: {e}")
                    break

            time.sleep(0.01)  # 短暂等待

        if self.debug_mode:
            print(f"[DEBUG] 完整响应数据: {repr(response_data)}")

        # 解析响应数据
        return self._parse_response_data(response_data)

    def _is_response_complete(self, data):
        """检查响应数据是否完整"""
        # 检查是否包含预期的CSV行数
        csv_lines = [line for line in data.split('\n') if ',' in line and line.strip()]

        # 对于测试命令(0xAA)，期望4行CSV数据
        # 对于电压命令(0x55)，期望1行CSV数据
        return len(csv_lines) >= 1  # 至少有一行有效数据

    def _parse_response_data(self, response_data):
        """
        解析响应数据 - 核心数据解析逻辑
        """
        measurements = []

        if not response_data.strip():
            return measurements

        lines = response_data.strip().split('\n')

        for line in lines:
            line = line.strip()

            if self.debug_mode:
                print(f"[DEBUG] 处理行: {repr(line)}")

            # 跳过调试信息
            if self._is_debug_line(line):
                if self.debug_mode:
                    print(f"[DEBUG] 跳过调试行: {line}")
                continue

            # 解析CSV格式数据
            parsed_data = self._parse_csv_line(line)
            if parsed_data:
                measurements.append(parsed_data)
                if self.debug_mode:
                    print(f"[DEBUG] 解析成功: {parsed_data}")

        return measurements

    def _is_debug_line(self, line):
        """判断是否为调试信息行"""
        debug_patterns = [
            r'^Start Voltage\s*:',
            r'^HF_current\s*:',
            r'^.*zeroPoint\s*:',
            r'^.*mA\s+zeroPoint',
            r'^\s*$'  # 空行
        ]

        for pattern in debug_patterns:
            if re.match(pattern, line, re.IGNORECASE):
                return True

        return False

    def _parse_csv_line(self, line):
        """
        解析CSV格式行 - 关键数据解析函数
        期望格式: "1,V,5075" 或 "2,R_sei,281" 等
        """
        if ',' not in line:
            return None

        try:
            parts = line.split(',')

            if len(parts) < 3:
                if self.debug_mode:
                    print(f"[DEBUG] CSV格式不完整: {parts}")
                return None

            # 解析各部分
            param_id = parts[0].strip()
            param_type = parts[1].strip()
            param_value_str = parts[2].strip()

            # 验证参数ID
            if not param_id.isdigit():
                if self.debug_mode:
                    print(f"[DEBUG] 无效参数ID: {param_id}")
                return None

            # 验证参数类型
            valid_types = ['V', 'R_sei', 'R_ct', 'R_ohm']
            if param_type not in valid_types:
                if self.debug_mode:
                    print(f"[DEBUG] 未知参数类型: {param_type}")
                return None

            # 转换数值
            try:
                param_value = int(param_value_str)
            except ValueError:
                if self.debug_mode:
                    print(f"[DEBUG] 数值转换失败: {param_value_str}")
                return None

            # 数据合理性检查
            if not self._validate_measurement_value(param_type, param_value):
                if self.debug_mode:
                    print(f"[DEBUG] 数值超出合理范围: {param_type}={param_value}")
                return None

            return {
                'param_id': int(param_id),
                'param_type': param_type,
                'param_value': param_value,
                'timestamp': datetime.now(),
                'raw_line': line
            }

        except Exception as e:
            if self.debug_mode:
                print(f"[DEBUG] CSV解析异常: {e}, 行: {line}")
            return None

    def _validate_measurement_value(self, param_type, value):
        """验证测量值的合理性"""
        # 定义各参数的合理范围
        valid_ranges = {
            'V': (1000, 5000),      # 电压: 1V-5V (mV)
            'R_sei': (50, 500),     # SEI阻抗: 50-500 μΩ
            'R_ct': (50, 300),      # 电荷转移阻抗: 50-300 μΩ
            'R_ohm': (200, 800)     # 欧姆阻抗: 200-800 μΩ
        }

        if param_type in valid_ranges:
            min_val, max_val = valid_ranges[param_type]
            return min_val <= value <= max_val

        return True  # 未知类型暂时通过

    def get_stable_measurement(self, param_type, new_value):
        """
        获取稳定的测量值 - 数据稳定性处理
        使用简单的滑动平均来减少波动
        """
        if param_type not in self.last_measurements:
            self.last_measurements[param_type] = []

        # 保存最近的5个值
        self.last_measurements[param_type].append(new_value)
        if len(self.last_measurements[param_type]) > 5:
            self.last_measurements[param_type].pop(0)

        # 计算平均值
        values = self.last_measurements[param_type]
        stable_value = sum(values) / len(values)

        return int(stable_value)

    def send_voltage_command(self):
        """发送电压测量命令 (0x55) - 返回电压值 - 修复版"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接串口")
            return

        try:
            # 发送真实命令到串口
            command = b'\x55'
            if self.serial_connection:
                self.serial_connection.write(command)
                print(f"[{datetime.now().strftime('%H:%M:%S')}] 发送电压测量命令: 0x55")

                # 处理真实串口响应数据
                measurements = self.process_serial_response(timeout=2.0)

                # 查找电压数据
                voltage_measurement = None
                for measurement in measurements:
                    if measurement['param_type'] == 'V':
                        voltage_measurement = measurement
                        break

                if voltage_measurement:
                    raw_value = voltage_measurement['param_value']
                    stable_value = self.get_stable_measurement('V', raw_value)

                    self.add_measurement_data("V", stable_value)
                    print(f"✓ 电压测量: {raw_value} → {stable_value} mV (稳定化)")
                else:
                    print("警告: 未接收到电压测量数据")
            else:
                # 如果没有真实串口连接，使用固定参考值（仅用于开发测试）
                print("警告: 使用参考数据 - 请连接真实设备获取准确数据")
                voltage_value = 3290  # 使用固定参考值而非随机值
                self.add_measurement_data("V", voltage_value)
                print(f"[{datetime.now().strftime('%H:%M:%S')}] ✓ 电压测量完成: {voltage_value} mV (参考值)")

        except Exception as e:
            messagebox.showerror("命令失败", f"发送电压命令失败: {e}")
            print(f"电压命令失败: {e}")

    def send_reset_command(self):
        """发送复位命令 (0xA0) - 无返回"""
        if not self.is_connected:
            messagebox.showwarning("警告", "请先连接串口")
            return

        try:
            # 模拟发送命令
            command = b'\xA0'
            if self.serial_connection:
                self.serial_connection.write(command)

            print(f"[{datetime.now().strftime('%H:%M:%S')}] ✓ 复位命令已发送")
            messagebox.showinfo("复位", "设备复位命令已发送")

        except Exception as e:
            messagebox.showerror("命令失败", f"发送复位命令失败: {e}")
            print(f"复位命令失败: {e}")

    def add_measurement_data(self, measure_type, value):
        """添加测量数据"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 确定单位
        unit = "mV" if measure_type == "V" else "μΩ"

        # 添加到数据列表
        self.test_data.append({
            'time': timestamp,
            'type': measure_type,
            'value': value,
            'unit': unit
        })

        # 更新时间数据字典
        if timestamp not in self.time_data_dict:
            self.time_data_dict[timestamp] = {
                'V': 'N/A',
                'R_sei': 'N/A',
                'R_ct': 'N/A',
                'R_ohm': 'N/A'
            }

        # 更新对应参数的值
        self.time_data_dict[timestamp][measure_type] = value

        # 更新数据表格
        self.update_data_table()

        # 更新当前值
        self.current_values[measure_type] = value

        # 更新对应的显示卡片
        if measure_type == "V":
            self.voltage_card.value_label.config(text=str(value))
        elif measure_type == "R_sei":
            self.r_sei_card.value_label.config(text=str(value))
        elif measure_type == "R_ct":
            self.r_ct_card.value_label.config(text=str(value))
        elif measure_type == "R_ohm":
            self.r_ohm_card.value_label.config(text=str(value))

        # 更新测试次数
        test_count = len(self.test_data)
        self.count_card.value_label.config(text=str(test_count))

        # 更新图表
        self.update_chart()

    def update_data_table(self):
        """更新数据记录表格 - 按时间组织显示"""
        try:
            # 清空现有表格数据
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)

            # 按时间排序（最新的在顶部）
            sorted_times = sorted(self.time_data_dict.keys(), reverse=True)

            # 插入数据到表格
            for timestamp in sorted_times:
                data_row = self.time_data_dict[timestamp]
                values = (
                    timestamp,
                    data_row['V'],
                    data_row['R_sei'],
                    data_row['R_ct'],
                    data_row['R_ohm']
                )
                self.data_tree.insert('', 'end', values=values)

        except Exception as e:
            print(f"更新数据表格失败: {e}")

    def toggle_continuous_testing(self):
        """切换连续测试状态"""
        if self.continuous_var.get():
            if not self.is_connected:
                messagebox.showwarning("警告", "请先连接串口")
                self.continuous_var.set(False)
                return

            self.continuous_testing = True
            self.test_thread = threading.Thread(target=self.continuous_test_worker, daemon=True)
            self.test_thread.start()
            print("✓ 连续测试已启动")
        else:
            self.continuous_testing = False
            print("✓ 连续测试已停止")

    def continuous_test_worker(self):
        """连续测试工作线程"""
        while self.continuous_testing and self.is_connected:
            try:
                # 在主线程中执行测试命令
                self.root.after(0, self.send_test_command)

                # 等待指定间隔
                interval = float(self.interval_var.get())
                time.sleep(interval)

            except Exception as e:
                print(f"连续测试错误: {e}")
                break

    def update_chart(self):
        """更新图表显示 - 完全修复版"""
        if MATPLOTLIB_AVAILABLE and hasattr(self, 'ax') and hasattr(self, 'canvas'):
            try:
                # 如果没有数据，显示等待状态
                if not self.test_data:
                    self.ax.clear()
                    self.ax.set_title("实时曲线图", fontsize=16, fontweight='bold', pad=20)
                    self.ax.set_xlabel("测量序号", fontsize=12)
                    self.ax.set_ylabel("测量值", fontsize=12)
                    self.ax.grid(True, alpha=0.3, linestyle='--')
                    self.ax.set_facecolor('#f8f9fa')

                    self.ax.text(0.5, 0.5, '等待测量数据...\n\n点击"测试命令"开始测量',
                                horizontalalignment='center', verticalalignment='center',
                                transform=self.ax.transAxes, fontsize=14,
                                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.5))

                    self.canvas.draw()
                    return

                # 提取不同类型的数据
                voltages = [(i, float(d['value'])) for i, d in enumerate(self.test_data) if d['type'] == 'V']
                r_sei_values = [(i, float(d['value'])) for i, d in enumerate(self.test_data) if d['type'] == 'R_sei']
                r_ct_values = [(i, float(d['value'])) for i, d in enumerate(self.test_data) if d['type'] == 'R_ct']
                r_ohm_values = [(i, float(d['value'])) for i, d in enumerate(self.test_data) if d['type'] == 'R_ohm']

                # 清除旧图表
                self.ax.clear()

                # 设置图表样式
                self.ax.set_title("实时曲线图", fontsize=16, fontweight='bold', pad=20)
                self.ax.set_xlabel("测量序号", fontsize=12)
                self.ax.set_ylabel("测量值", fontsize=12)
                self.ax.grid(True, alpha=0.3, linestyle='--')
                self.ax.set_facecolor('#f8f9fa')

                # 绘制不同类型的数据线 - 使用指定颜色
                if voltages:
                    v_times, v_values = zip(*voltages)
                    self.ax.plot(v_times, v_values, color='#1f77b4', label='V电压 (mV)',
                               marker='o', linewidth=3, markersize=6, alpha=0.8)

                if r_sei_values:
                    sei_times, sei_vals = zip(*r_sei_values)
                    self.ax.plot(sei_times, sei_vals, color='#2ca02c', label='R_sei电阻 (μΩ)',
                               marker='s', linewidth=3, markersize=6, alpha=0.8)

                if r_ct_values:
                    ct_times, ct_vals = zip(*r_ct_values)
                    self.ax.plot(ct_times, ct_vals, color='#9467bd', label='R_ct电阻 (μΩ)',
                               marker='^', linewidth=3, markersize=6, alpha=0.8)

                if r_ohm_values:
                    ohm_times, ohm_vals = zip(*r_ohm_values)
                    self.ax.plot(ohm_times, ohm_vals, color='#d62728', label='R_ohm电阻 (μΩ)',
                               marker='d', linewidth=3, markersize=6, alpha=0.8)

                # 设置图例
                if any([voltages, r_sei_values, r_ct_values, r_ohm_values]):
                    self.ax.legend(loc='upper left', frameon=True, fancybox=True, shadow=True)

                # 自动调整坐标轴
                if self.test_data:
                    self.ax.set_xlim(-0.5, len(self.test_data) - 0.5)

                    # 获取所有数值用于Y轴范围
                    all_values = []
                    if voltages:
                        all_values.extend([v[1] for v in voltages])
                    if r_sei_values:
                        all_values.extend([v[1] for v in r_sei_values])
                    if r_ct_values:
                        all_values.extend([v[1] for v in r_ct_values])
                    if r_ohm_values:
                        all_values.extend([v[1] for v in r_ohm_values])

                    if all_values:
                        y_min, y_max = min(all_values), max(all_values)
                        y_range = y_max - y_min
                        self.ax.set_ylim(y_min - y_range * 0.1, y_max + y_range * 0.1)

                # 设置图表边距
                self.fig.tight_layout(pad=3.0)

                # 刷新画布
                self.canvas.draw()
                self.canvas.flush_events()

            except Exception as e:
                print(f"❌ 图表更新失败: {e}")

        elif hasattr(self, 'chart_text'):
            # 更新文本图表
            try:
                chart_content = f"""
多参数测量数据显示

当前测量值:
- 电压 (V): {self.current_values['V']} mV
- SEI电阻: {self.current_values['R_sei']} μΩ
- CT电阻: {self.current_values['R_ct']} μΩ
- 欧姆电阻: {self.current_values['R_ohm']} μΩ

数据统计:
- 总数据点: {len(self.test_data)}
- 电压测量: {len([d for d in self.test_data if d['type'] == 'V'])} 次
- SEI电阻: {len([d for d in self.test_data if d['type'] == 'R_sei'])} 次
- CT电阻: {len([d for d in self.test_data if d['type'] == 'R_ct'])} 次
- 欧姆电阻: {len([d for d in self.test_data if d['type'] == 'R_ohm'])} 次

最近10条数据:
"""
                for data in self.test_data[-10:]:
                    chart_content += f"  {data['time']} - {data['type']}: {data['value']} {data['unit']}\n"

                self.chart_text.config(state=tk.NORMAL)
                self.chart_text.delete(1.0, tk.END)
                self.chart_text.insert(1.0, chart_content)
                self.chart_text.config(state=tk.DISABLED)

            except Exception as e:
                print(f"❌ 文本图表更新失败: {e}")

    def export_data_unified(self):
        """导出数据到单个CSV文件 - 统一导出"""
        if not self.test_data:
            messagebox.showwarning("警告", "没有数据可导出\n\n请先进行测量以获取数据。")
            return

        try:
            # 生成默认文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            default_filename = f"鲸测云LCER_多参数数据_{timestamp}.csv"

            # 打开文件保存对话框 - 修复参数错误
            filename = filedialog.asksaveasfilename(
                title="导出测量数据",
                defaultextension=".csv",
                filetypes=[
                    ("CSV 文件", "*.csv"),
                    ("文本文件", "*.txt"),
                    ("所有文件", "*.*")
                ],
                initialfile=default_filename  # 修复: 使用 initialfile 而不是 initialname
            )

            if not filename:
                print("用户取消了数据导出")
                return

            # 确保文件扩展名
            if not filename.lower().endswith(('.csv', '.txt')):
                filename += '.csv'

            # 导出数据
            print(f"开始导出数据到: {filename}")

            # 写入CSV文件
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['时间', '参数类型', '测量值', '单位'])

                for data in self.test_data:
                    writer.writerow([data['time'], data['type'], data['value'], data['unit']])

            # 生成统计信息
            stats = {
                'total': len(self.test_data),
                'voltage': len([d for d in self.test_data if d['type'] == 'V']),
                'r_sei': len([d for d in self.test_data if d['type'] == 'R_sei']),
                'r_ct': len([d for d in self.test_data if d['type'] == 'R_ct']),
                'r_ohm': len([d for d in self.test_data if d['type'] == 'R_ohm'])
            }

            # 显示成功消息
            success_msg = f"""数据导出成功！

文件位置: {filename}

导出统计:
• 总记录数: {stats['total']} 条
• 电压测量: {stats['voltage']} 条
• SEI电阻: {stats['r_sei']} 条
• CT电阻: {stats['r_ct']} 条
• 欧姆电阻: {stats['r_ohm']} 条

文件可以在Excel中打开查看。"""

            messagebox.showinfo("导出成功", success_msg)
            print(f"✓ 数据导出成功: {filename} ({stats['total']} 条记录)")

            # 询问是否打开文件
            if messagebox.askyesno("打开文件", "是否要打开导出的文件？"):
                try:
                    import os
                    os.startfile(filename)
                except Exception as e:
                    print(f"无法打开文件: {e}")

        except PermissionError:
            messagebox.showerror("导出失败",
                               f"文件访问被拒绝！\n\n可能原因：\n• 文件正在被其他程序使用\n• 没有写入权限\n• 文件被锁定\n\n请关闭相关程序后重试。")
            print("❌ 导出失败: 文件访问被拒绝")

        except Exception as e:
            error_msg = f"导出过程中发生错误：\n\n{str(e)}\n\n请检查：\n• 文件路径是否有效\n• 磁盘空间是否充足\n• 是否有写入权限"
            messagebox.showerror("导出失败", error_msg)
            print(f"❌ 导出失败: {e}")

    def export_data_classified(self):
        """分类导出数据到多个CSV文件 - 每种参数类型独立文件"""
        if not self.test_data:
            messagebox.showwarning("警告", "没有数据可导出\n\n请先进行测量以获取数据。")
            return

        try:
            # 选择导出目录
            export_dir = filedialog.askdirectory(
                title="选择分类导出目录",
                mustexist=True
            )

            if not export_dir:
                print("用户取消了分类导出")
                return

            # 生成时间戳
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # 按参数类型分组数据
            data_groups = {
                'V': [d for d in self.test_data if d['type'] == 'V'],
                'R_sei': [d for d in self.test_data if d['type'] == 'R_sei'],
                'R_ct': [d for d in self.test_data if d['type'] == 'R_ct'],
                'R_ohm': [d for d in self.test_data if d['type'] == 'R_ohm']
            }

            # 参数类型中文名称映射
            type_names = {
                'V': '电压测量',
                'R_sei': 'SEI电阻',
                'R_ct': 'CT电阻',
                'R_ohm': '欧姆电阻'
            }

            exported_files = []
            export_stats = {}

            # 为每种参数类型创建独立的CSV文件
            for param_type, data_list in data_groups.items():
                if not data_list:  # 跳过空数据组
                    continue

                # 生成文件名
                type_name = type_names[param_type]
                filename = f"鲸测云LCER_{type_name}_{timestamp}.csv"
                filepath = f"{export_dir}/{filename}"

                # 写入CSV文件
                with open(filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # 写入文件头信息
                    writer.writerow([f'鲸测云LCER {type_name}数据导出'])
                    writer.writerow([f'导出时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                    writer.writerow([f'参数类型: {param_type} ({type_name})'])
                    writer.writerow([f'数据点数: {len(data_list)}'])
                    writer.writerow([])  # 空行

                    # 写入数据表头
                    if param_type == 'V':
                        writer.writerow(['序号', '测量时间', '电压值 (mV)', '备注'])
                    else:
                        writer.writerow(['序号', '测量时间', '电阻值 (μΩ)', '备注'])

                    # 写入数据
                    for i, data in enumerate(data_list, 1):
                        writer.writerow([i, data['time'], data['value'], f"{param_type}测量"])

                    # 写入统计信息
                    writer.writerow([])  # 空行
                    writer.writerow(['统计信息'])
                    writer.writerow(['最大值', max(d['value'] for d in data_list)])
                    writer.writerow(['最小值', min(d['value'] for d in data_list)])
                    writer.writerow(['平均值', round(sum(d['value'] for d in data_list) / len(data_list), 2)])
                    writer.writerow(['数据点数', len(data_list)])

                exported_files.append(filename)
                export_stats[param_type] = len(data_list)
                print(f"✓ 导出 {type_name} 数据: {filename} ({len(data_list)} 条记录)")

            # 创建汇总文件
            summary_filename = f"鲸测云LCER_数据汇总_{timestamp}.csv"
            summary_filepath = f"{export_dir}/{summary_filename}"

            with open(summary_filepath, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)

                # 汇总文件头
                writer.writerow(['鲸测云LCER 多参数测量数据汇总'])
                writer.writerow([f'导出时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'])
                writer.writerow([f'总数据点数: {len(self.test_data)}'])
                writer.writerow([])

                # 分类统计
                writer.writerow(['参数类型', '中文名称', '数据点数', '对应文件'])
                for param_type, count in export_stats.items():
                    type_name = type_names[param_type]
                    filename = f"鲸测云LCER_{type_name}_{timestamp}.csv"
                    writer.writerow([param_type, type_name, count, filename])

                writer.writerow([])
                writer.writerow(['导出文件列表'])
                for filename in exported_files:
                    writer.writerow([filename])
                writer.writerow([summary_filename])

            exported_files.append(summary_filename)

            # 显示成功消息
            success_msg = f"""分类导出成功！

导出目录: {export_dir}

导出文件:
"""
            for filename in exported_files:
                success_msg += f"• {filename}\n"

            success_msg += f"""
数据统计:
• 总记录数: {len(self.test_data)} 条
"""
            for param_type, count in export_stats.items():
                type_name = type_names[param_type]
                success_msg += f"• {type_name}: {count} 条\n"

            success_msg += "\n每种参数类型都有独立的CSV文件，便于分析。"

            messagebox.showinfo("分类导出成功", success_msg)
            print(f"✓ 分类导出完成: {len(exported_files)} 个文件")

            # 询问是否打开导出目录
            if messagebox.askyesno("打开目录", "是否要打开导出目录查看文件？"):
                try:
                    import os
                    os.startfile(export_dir)
                except Exception as e:
                    print(f"无法打开目录: {e}")

        except Exception as e:
            error_msg = f"分类导出过程中发生错误：\n\n{str(e)}\n\n请检查：\n• 目录路径是否有效\n• 磁盘空间是否充足\n• 是否有写入权限"
            messagebox.showerror("分类导出失败", error_msg)
            print(f"❌ 分类导出失败: {e}")

    def export_data_excel(self):
        """导出数据到Excel文件 - 多工作表格式"""
        if not self.test_data:
            messagebox.showwarning("警告", "没有数据可导出\n\n请先进行测量以获取数据。")
            return

        try:
            # 检查是否有openpyxl库
            try:
                import openpyxl
                from openpyxl.styles import Font, PatternFill, Alignment
                from openpyxl.chart import LineChart, Reference
                EXCEL_AVAILABLE = True
            except ImportError:
                EXCEL_AVAILABLE = False
                messagebox.showwarning("功能限制",
                                     "Excel导出功能需要安装 openpyxl 库。\n\n将使用CSV格式导出。")
                self.export_data_classified()
                return

            # 生成默认文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            default_filename = f"鲸测云LCER_多参数数据_{timestamp}.xlsx"

            # 打开文件保存对话框
            filename = filedialog.asksaveasfilename(
                title="导出Excel格式数据",
                defaultextension=".xlsx",
                filetypes=[
                    ("Excel 文件", "*.xlsx"),
                    ("Excel 97-2003", "*.xls"),
                    ("所有文件", "*.*")
                ],
                initialfile=default_filename
            )

            if not filename:
                print("用户取消了Excel导出")
                return

            # 确保文件扩展名
            if not filename.lower().endswith(('.xlsx', '.xls')):
                filename += '.xlsx'

            # 创建Excel工作簿
            wb = openpyxl.Workbook()

            # 删除默认工作表
            wb.remove(wb.active)

            # 按参数类型分组数据
            data_groups = {
                'V': [d for d in self.test_data if d['type'] == 'V'],
                'R_sei': [d for d in self.test_data if d['type'] == 'R_sei'],
                'R_ct': [d for d in self.test_data if d['type'] == 'R_ct'],
                'R_ohm': [d for d in self.test_data if d['type'] == 'R_ohm']
            }

            # 参数类型信息
            type_info = {
                'V': {'name': '电压测量', 'unit': 'mV', 'color': '0066CC'},
                'R_sei': {'name': 'SEI电阻', 'unit': 'μΩ', 'color': '00AA00'},
                'R_ct': {'name': 'CT电阻', 'unit': 'μΩ', 'color': '8800AA'},
                'R_ohm': {'name': '欧姆电阻', 'unit': 'μΩ', 'color': 'CC0000'}
            }

            # 为每种参数类型创建工作表
            for param_type, data_list in data_groups.items():
                if not data_list:
                    continue

                info = type_info[param_type]
                ws = wb.create_sheet(title=info['name'])

                # 设置标题样式
                title_font = Font(size=14, bold=True, color='FFFFFF')
                title_fill = PatternFill(start_color=info['color'], end_color=info['color'], fill_type='solid')
                header_font = Font(size=12, bold=True)

                # 写入标题
                ws['A1'] = f"鲸测云LCER {info['name']}数据"
                ws['A1'].font = title_font
                ws['A1'].fill = title_fill
                ws.merge_cells('A1:D1')

                # 写入信息
                ws['A3'] = f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                ws['A4'] = f"参数类型: {param_type} ({info['name']})"
                ws['A5'] = f"数据点数: {len(data_list)}"

                # 写入表头
                headers = ['序号', '测量时间', f"测量值 ({info['unit']})", '备注']
                for col, header in enumerate(headers, 1):
                    cell = ws.cell(row=7, column=col, value=header)
                    cell.font = header_font

                # 写入数据
                for i, data in enumerate(data_list, 1):
                    ws.cell(row=7+i, column=1, value=i)
                    ws.cell(row=7+i, column=2, value=data['time'])
                    ws.cell(row=7+i, column=3, value=data['value'])
                    ws.cell(row=7+i, column=4, value=f"{param_type}测量")

                # 调整列宽
                ws.column_dimensions['A'].width = 8
                ws.column_dimensions['B'].width = 12
                ws.column_dimensions['C'].width = 15
                ws.column_dimensions['D'].width = 12

            # 创建汇总工作表
            summary_ws = wb.create_sheet(title="数据汇总", index=0)

            # 汇总表标题
            summary_ws['A1'] = "鲸测云LCER 多参数测量数据汇总"
            summary_ws['A1'].font = Font(size=16, bold=True)
            summary_ws.merge_cells('A1:E1')

            # 汇总信息
            summary_ws['A3'] = f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            summary_ws['A4'] = f"总数据点数: {len(self.test_data)}"

            # 分类统计表头
            summary_headers = ['参数类型', '中文名称', '单位', '数据点数', '工作表']
            for col, header in enumerate(summary_headers, 1):
                cell = summary_ws.cell(row=6, column=col, value=header)
                cell.font = Font(bold=True)

            # 分类统计数据
            row = 7
            for param_type, data_list in data_groups.items():
                if not data_list:
                    continue
                info = type_info[param_type]
                summary_ws.cell(row=row, column=1, value=param_type)
                summary_ws.cell(row=row, column=2, value=info['name'])
                summary_ws.cell(row=row, column=3, value=info['unit'])
                summary_ws.cell(row=row, column=4, value=len(data_list))
                summary_ws.cell(row=row, column=5, value=info['name'])
                row += 1

            # 调整汇总表列宽
            for col in ['A', 'B', 'C', 'D', 'E']:
                summary_ws.column_dimensions[col].width = 15

            # 保存Excel文件
            wb.save(filename)

            # 生成统计信息
            stats = {param_type: len(data_list) for param_type, data_list in data_groups.items() if data_list}

            # 显示成功消息
            success_msg = f"""Excel导出成功！

文件位置: {filename}

工作表:
• 数据汇总 (总览)
"""
            for param_type, count in stats.items():
                type_name = type_info[param_type]['name']
                success_msg += f"• {type_name} ({count} 条记录)\n"

            success_msg += f"""
总记录数: {len(self.test_data)} 条

Excel文件包含多个工作表，每种参数类型独立显示。"""

            messagebox.showinfo("Excel导出成功", success_msg)
            print(f"✓ Excel导出成功: {filename}")

            # 询问是否打开文件
            if messagebox.askyesno("打开文件", "是否要打开Excel文件？"):
                try:
                    import os
                    os.startfile(filename)
                except Exception as e:
                    print(f"无法打开Excel文件: {e}")

        except Exception as e:
            error_msg = f"Excel导出过程中发生错误：\n\n{str(e)}\n\n请检查：\n• 文件路径是否有效\n• 磁盘空间是否充足\n• 是否有写入权限\n• Excel文件是否被占用"
            messagebox.showerror("Excel导出失败", error_msg)
            print(f"❌ Excel导出失败: {e}")

    def export_data_excel_single(self):
        """导出数据到Excel文件 - 单工作表格式"""
        if not self.test_data:
            messagebox.showwarning("警告", "没有数据可导出\n\n请先进行测量以获取数据。")
            return

        try:
            # 检查是否有openpyxl库
            try:
                import openpyxl
                from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
            except ImportError:
                messagebox.showwarning("功能限制",
                                     "Excel导出功能需要安装 openpyxl 库。\n\n将使用CSV格式导出。")
                self.export_data_classified()
                return

            # 生成默认文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            default_filename = f"鲸测云LCER_综合数据表_{timestamp}.xlsx"

            # 打开文件保存对话框
            filename = filedialog.asksaveasfilename(
                title="导出Excel表格数据 (单表格式)",
                defaultextension=".xlsx",
                filetypes=[
                    ("Excel 文件", "*.xlsx"),
                    ("Excel 97-2003", "*.xls"),
                    ("所有文件", "*.*")
                ],
                initialfile=default_filename
            )

            if not filename:
                print("用户取消了Excel单表导出")
                return

            # 确保文件扩展名
            if not filename.lower().endswith(('.xlsx', '.xls')):
                filename += '.xlsx'

            # 按时间戳组织数据
            time_data = {}
            for data in self.test_data:
                time_key = data['time']
                if time_key not in time_data:
                    time_data[time_key] = {
                        'V': None,
                        'R_sei': None,
                        'R_ct': None,
                        'R_ohm': None
                    }
                time_data[time_key][data['type']] = data['value']

            # 按时间排序
            sorted_times = sorted(time_data.keys())

            # 创建Excel工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "综合数据表"

            # 设置样式
            header_font = Font(size=12, bold=True, color='FFFFFF')
            header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            center_alignment = Alignment(horizontal='center', vertical='center')

            # 写入标题
            ws['A1'] = "鲸测云LCER 多参数综合数据表"
            ws['A1'].font = Font(size=16, bold=True, color='366092')
            ws.merge_cells('A1:F1')
            ws['A1'].alignment = center_alignment

            # 写入信息
            ws['A3'] = f"导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            ws['A4'] = f"数据点数: {len(sorted_times)} 个时间点"
            ws['A5'] = f"总记录数: {len(self.test_data)} 条"

            # 写入表头
            headers = ['时间', 'V电压(mV)', 'R_sei电阻(μΩ)', 'R_ct电阻(μΩ)', 'R_ohm欧姆电阻(μΩ)']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=7, column=col, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.border = border
                cell.alignment = center_alignment

            # 写入数据
            for row_idx, time_key in enumerate(sorted_times, 8):
                data_row = time_data[time_key]

                # 时间列
                time_cell = ws.cell(row=row_idx, column=1, value=time_key)
                time_cell.border = border
                time_cell.alignment = center_alignment

                # 数据列
                columns = ['V', 'R_sei', 'R_ct', 'R_ohm']
                for col_idx, param_type in enumerate(columns, 2):
                    value = data_row[param_type]
                    display_value = value if value is not None else "N/A"

                    cell = ws.cell(row=row_idx, column=col_idx, value=display_value)
                    cell.border = border
                    cell.alignment = center_alignment

                    # 如果是数值，设置数字格式
                    if value is not None:
                        cell.number_format = '0'

            # 调整列宽
            column_widths = [20, 15, 18, 16, 20]  # 时间, V电压, R_sei, R_ct, R_ohm
            for col_idx, width in enumerate(column_widths, 1):
                ws.column_dimensions[chr(64 + col_idx)].width = width

            # 添加统计信息
            stats_start_row = len(sorted_times) + 10

            # 统计标题
            ws.cell(row=stats_start_row, column=1, value="数据统计").font = Font(size=14, bold=True)

            # 计算统计信息
            stats_data = {}
            for param_type in ['V', 'R_sei', 'R_ct', 'R_ohm']:
                values = [data[param_type] for data in time_data.values() if data[param_type] is not None]
                if values:
                    stats_data[param_type] = {
                        'count': len(values),
                        'max': max(values),
                        'min': min(values),
                        'avg': round(sum(values) / len(values), 2)
                    }
                else:
                    stats_data[param_type] = {
                        'count': 0,
                        'max': 'N/A',
                        'min': 'N/A',
                        'avg': 'N/A'
                    }

            # 写入统计表头
            stat_headers = ['参数类型', '数据点数', '最大值', '最小值', '平均值']
            for col, header in enumerate(stat_headers, 1):
                cell = ws.cell(row=stats_start_row + 2, column=col, value=header)
                cell.font = Font(bold=True)
                cell.border = border
                cell.alignment = center_alignment

            # 写入统计数据
            param_names = {
                'V': 'V电压(mV)',
                'R_sei': 'R_sei电阻(μΩ)',
                'R_ct': 'R_ct电阻(μΩ)',
                'R_ohm': 'R_ohm欧姆电阻(μΩ)'
            }

            for row_idx, (param_type, stats) in enumerate(stats_data.items(), stats_start_row + 3):
                ws.cell(row=row_idx, column=1, value=param_names[param_type]).border = border
                ws.cell(row=row_idx, column=2, value=stats['count']).border = border
                ws.cell(row=row_idx, column=3, value=stats['max']).border = border
                ws.cell(row=row_idx, column=4, value=stats['min']).border = border
                ws.cell(row=row_idx, column=5, value=stats['avg']).border = border

            # 保存Excel文件
            wb.save(filename)

            # 显示成功消息
            success_msg = f"""Excel单表导出成功！

文件位置: {filename}

数据格式:
• 单工作表格式
• 按时间组织的综合数据表
• 包含所有参数类型的测量值

数据统计:
• 时间点数: {len(sorted_times)} 个
• 总记录数: {len(self.test_data)} 条
"""

            for param_type, stats in stats_data.items():
                param_name = param_names[param_type]
                success_msg += f"• {param_name}: {stats['count']} 条有效数据\n"

            success_msg += "\n表格包含详细的统计信息，便于数据分析。"

            messagebox.showinfo("Excel单表导出成功", success_msg)
            print(f"✓ Excel单表导出成功: {filename}")

            # 询问是否打开文件
            if messagebox.askyesno("打开文件", "是否要打开Excel文件？"):
                try:
                    import os
                    os.startfile(filename)
                except Exception as e:
                    print(f"无法打开Excel文件: {e}")

        except Exception as e:
            error_msg = f"Excel单表导出过程中发生错误：\n\n{str(e)}\n\n请检查：\n• 文件路径是否有效\n• 磁盘空间是否充足\n• 是否有写入权限\n• Excel文件是否被占用"
            messagebox.showerror("Excel单表导出失败", error_msg)
            print(f"❌ Excel单表导出失败: {e}")

    def clear_data(self):
        """清空数据"""
        if messagebox.askyesno("确认", "确定要清空所有数据吗？"):
            self.test_data.clear()

            # 清空时间数据字典
            self.time_data_dict.clear()

            # 清空数据表格
            for item in self.data_tree.get_children():
                self.data_tree.delete(item)

            # 重置显示卡片
            self.voltage_card.value_label.config(text="--")
            self.r_sei_card.value_label.config(text="--")
            self.r_ct_card.value_label.config(text="--")
            self.r_ohm_card.value_label.config(text="--")
            self.count_card.value_label.config(text="0")

            # 重置当前值
            for key in self.current_values:
                self.current_values[key] = '--'

            # 清空图表
            self.update_chart()

            print("数据已清空")

def main():
    print("=" * 70)
    print("🔋 鲸测云LCER电池测试仪")
    print("=" * 70)
    print("修复内容:")
    print("✓ 图表显示功能完全修复")
    print("✓ 数据导出功能完全修复 (解决 initialname 参数错误)")
    print("✓ 多参数实时显示优化")
    print("✓ 错误处理增强")
    print("=" * 70)
    print("特性:")
    print("✅ COM5 串口可靠检测")
    print("✅ 完整多参数测试功能 (V, R_sei, R_ct, R_ohm)")
    print("✅ 测试命令: 返回4种参数 (0xAA)")
    print("✅ 电压测量: 返回电压值 (0x55)")
    print("✅ 复位命令: 设备复位 (0xA0)")
    print("✅ 实时多参数显示卡片")
    print("✅ 多线数据可视化图表")
    print("✅ 连续测试功能")
    print("✅ 完整数据导出功能")
    print("✅ 专业界面布局")
    print("✅ 自包含依赖处理")
    print("=" * 70)

    try:
        root = tk.Tk()
        app = JingCeYunLCERApp(root)

        print("✓ 完全修复版应用程序启动成功")
        print("✓ 所有功能模块已加载")
        print("✓ COM5 串口支持已启用")
        print("✓ 多参数显示功能已激活")
        print("")
        print("使用说明:")
        print("1. 选择COM5串口并连接")
        print("2. 点击'测试命令'获取4种参数")
        print("3. 点击'电压测量'获取电压值")
        print("4. 启用'连续测试'进行自动监控")
        print("5. 查看6个实时数据卡片")
        print("6. 观察多线图表显示")
        print("7. 导出完整测量数据")
        print("=" * 70)

        root.mainloop()

    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
