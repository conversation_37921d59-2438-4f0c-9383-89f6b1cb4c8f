#!/usr/bin/env python3
"""
测试应用程序启动的独立脚本
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟serial模块
class MockSerial:
    def __init__(self, *args, **kwargs):
        pass
    def close(self):
        pass
    def write(self, data):
        return len(data)
    def read(self, size):
        return b''
    def readline(self):
        return b''

class MockListPorts:
    @staticmethod
    def comports():
        return []

# 创建模拟模块
import types
serial_module = types.ModuleType('serial')
serial_module.Serial = MockSerial
serial_module.SerialException = Exception
serial_module.tools = types.ModuleType('tools')
serial_module.tools.list_ports = MockListPorts

# 将模拟模块添加到sys.modules
sys.modules['serial'] = serial_module
sys.modules['serial.tools'] = serial_module.tools
sys.modules['serial.tools.list_ports'] = MockListPorts

def main():
    """主函数"""
    try:
        print("正在测试应用程序启动...")
        
        # 导入GUI模块
        from gui import LVERApp
        print("✓ GUI模块导入成功")
        
        # 创建主窗口
        root = tk.Tk()
        root.title("LVER 应用程序启动测试")
        root.geometry("1200x800")
        
        print("✓ 主窗口创建成功")
        
        # 创建应用程序实例
        app = LVERApp(root)
        print("✓ 应用程序实例创建成功")
        
        # 添加一些测试数据
        test_data_values = [
            (3295, 514, 308, 104),
            (3290, 527, 295, 129),
            (3285, 535, 283, 112),
        ]

        for voltage, r_ohm, r_sei, r_ct in test_data_values:
            app.data_manager.add_test_data(voltage, r_ohm, r_sei, r_ct)
        
        print("✓ 测试数据添加成功")
        
        # 测试图表更新
        if hasattr(app, 'chart_widget') and app.chart_widget:
            app.chart_widget.update_chart()
            print("✓ 图表更新成功")
        else:
            print("! 图表组件未找到或未初始化")
        
        # 创建状态标签
        status_frame = ttk.Frame(root)
        status_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)
        
        status_label = ttk.Label(
            status_frame,
            text="✓ 应用程序启动测试成功！所有组件正常工作。",
            foreground="green",
            font=("Arial", 12, "bold")
        )
        status_label.pack()
        
        # 添加说明
        info_label = ttk.Label(
            status_frame,
            text="图表功能已修复：简化了双Y轴处理，添加了数据检查，恢复了数据导出功能",
            foreground="blue",
            font=("Arial", 10)
        )
        info_label.pack(pady=(5, 0))
        
        print("\n应用程序启动测试完成！")
        print("主要修复内容：")
        print("1. ✓ 修复了图表组件初始化错误")
        print("2. ✓ 简化了双Y轴处理逻辑")
        print("3. ✓ 添加了数据检查和提示")
        print("4. ✓ 恢复了数据导出功能")
        print("5. ✓ 简化了图表导出（只支持PNG）")
        print("6. ✓ 改进了错误处理机制")
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        print(f"✗ 应用程序启动测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
