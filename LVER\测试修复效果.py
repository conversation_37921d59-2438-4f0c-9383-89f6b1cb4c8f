#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试三个修复效果
"""

import matplotlib.pyplot as plt
import matplotlib

def test_chinese_font():
    """测试中文字体支持"""
    print("🔋 测试中文字体支持")
    print("=" * 40)
    
    # 检查字体配置
    print(f"字体设置: {matplotlib.rcParams['font.sans-serif']}")
    print(f"负号设置: {matplotlib.rcParams['axes.unicode_minus']}")
    
    # 创建测试图表
    fig, ax = plt.subplots(figsize=(6, 4))
    ax.set_title("中文测试: V电压 R_sei电阻 R_ct电阻 Rs电阻")
    ax.plot([1,2,3], [100,200,300], label='测试数据')
    ax.legend()
    plt.savefig('中文字体测试.png', dpi=100)
    plt.close()
    
    print("✅ 中文字体测试完成")
    print("📄 生成图片: 中文字体测试.png")
    return True

def main():
    print("🔋 鲸测云LCER电池测试仪 - 修复验证")
    print("=" * 50)
    
    # 测试1: 中文字体
    test_chinese_font()
    
    print("\n📋 修复内容总结:")
    print("✅ 1. 中文字体支持 - matplotlib配置已添加")
    print("✅ 2. 测试次数统计 - 使用操作计数器")
    print("✅ 3. 标签文字更新 - R_ohm改为Rs电阻")
    
    print("\n💡 请在主程序中验证:")
    print("  🎨 图表中文字符正常显示")
    print("  📊 测试次数按操作计数")
    print("  📝 Rs电阻标签显示正确")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
