#!/usr/bin/env python3
"""
LVER 应用程序打包脚本
使用PyInstaller将Python应用程序打包成独立的Windows可执行文件
"""

import os
import sys
import subprocess
import shutil
from datetime import datetime

def check_dependencies():
    """检查打包所需的依赖"""
    print("=" * 60)
    print("检查打包依赖")
    print("=" * 60)
    
    required_packages = [
        'pyinstaller',
        'tkinter',  # 通常内置
        'matplotlib',
        'pyserial',
        'openpyxl'  # Excel导出功能
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'tkinter':
                import tkinter
                print(f"✓ {package} - 已安装 (内置)")
            elif package == 'pyinstaller':
                import PyInstaller
                print(f"✓ {package} - 已安装 (版本: {PyInstaller.__version__})")
            elif package == 'matplotlib':
                try:
                    import matplotlib
                    print(f"✓ {package} - 已安装 (版本: {matplotlib.__version__})")
                except ImportError:
                    print(f"❌ {package} - 未安装")
                    missing_packages.append(package)
                    continue
            elif package == 'pyserial':
                try:
                    import serial
                    print(f"✓ {package} - 已安装 (版本: {serial.__version__})")
                except ImportError:
                    print(f"❌ {package} - 未安装")
                    missing_packages.append(package)
                    continue
            elif package == 'openpyxl':
                try:
                    import openpyxl
                    print(f"✓ {package} - 已安装 (版本: {openpyxl.__version__})")
                except ImportError:
                    print(f"⚠️  {package} - 未安装 (Excel导出功能将不可用)")
                    missing_packages.append(package)
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        for package in missing_packages:
            if package != 'openpyxl':  # openpyxl是可选的
                print(f"  pip install {package}")
        
        if 'openpyxl' in missing_packages:
            print(f"  pip install openpyxl  # 可选，用于Excel导出功能")
        
        return False
    
    print("\n✓ 所有必需依赖都已安装")
    return True

def create_spec_file():
    """创建PyInstaller规格文件"""
    print("\n" + "=" * 60)
    print("创建PyInstaller规格文件")
    print("=" * 60)
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件和隐藏导入
added_files = []
hiddenimports = [
    'tkinter',
    'tkinter.ttk',
    'tkinter.messagebox',
    'tkinter.filedialog',
    'matplotlib',
    'matplotlib.backends.backend_tkagg',
    'matplotlib.figure',
    'matplotlib.pyplot',
    'serial',
    'serial.tools.list_ports',
    'csv',
    'threading',
    'datetime',
    'random',
    'time',
    'openpyxl',
    'openpyxl.styles',
    'openpyxl.chart'
]

a = Analysis(
    ['main_fixed_complete.py'],
    pathex=[],
    binaries=[],
    datas=added_files,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='LVER_串口通信工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以添加图标文件路径
    version_file=None,
)
'''
    
    spec_filename = 'LVER_app.spec'
    with open(spec_filename, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"✓ 创建规格文件: {spec_filename}")
    return spec_filename

def build_executable():
    """构建可执行文件"""
    print("\n" + "=" * 60)
    print("开始构建可执行文件")
    print("=" * 60)
    
    # 检查主文件是否存在
    main_file = 'main_fixed_complete.py'
    if not os.path.exists(main_file):
        print(f"❌ 主文件不存在: {main_file}")
        return False
    
    # 创建规格文件
    spec_file = create_spec_file()
    
    # 构建命令
    build_cmd = [
        'pyinstaller',
        '--clean',  # 清理临时文件
        '--noconfirm',  # 不询问覆盖
        spec_file
    ]
    
    print(f"执行构建命令: {' '.join(build_cmd)}")
    print("这可能需要几分钟时间...")
    
    try:
        # 执行构建
        result = subprocess.run(build_cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✓ 构建成功完成")
            
            # 检查输出文件
            exe_path = os.path.join('dist', 'LVER_串口通信工具.exe')
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"✓ 可执行文件已生成: {exe_path}")
                print(f"✓ 文件大小: {file_size:.1f} MB")
                return True
            else:
                print("❌ 可执行文件未找到")
                return False
        else:
            print("❌ 构建失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中发生错误: {e}")
        return False

def test_executable():
    """测试可执行文件"""
    print("\n" + "=" * 60)
    print("测试可执行文件")
    print("=" * 60)
    
    exe_path = os.path.join('dist', 'LVER_串口通信工具.exe')
    
    if not os.path.exists(exe_path):
        print("❌ 可执行文件不存在，无法测试")
        return False
    
    print(f"可执行文件位置: {exe_path}")
    print("请手动测试以下功能:")
    print("1. 应用程序是否能正常启动")
    print("2. 串口检测功能是否正常")
    print("3. 测试命令功能是否正常")
    print("4. 图表显示功能是否正常")
    print("5. 数据导出功能是否正常")
    print("6. 分类导出功能是否正常")
    
    # 询问是否启动测试
    try:
        response = input("\n是否要启动可执行文件进行测试? (y/n): ")
        if response.lower() in ['y', 'yes']:
            print("启动可执行文件...")
            subprocess.Popen([exe_path])
            print("✓ 可执行文件已启动")
            return True
    except KeyboardInterrupt:
        print("\n用户取消测试")
    
    return False

def create_distribution_package():
    """创建分发包"""
    print("\n" + "=" * 60)
    print("创建分发包")
    print("=" * 60)
    
    exe_path = os.path.join('dist', 'LVER_串口通信工具.exe')
    
    if not os.path.exists(exe_path):
        print("❌ 可执行文件不存在，无法创建分发包")
        return False
    
    # 创建分发目录
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    dist_dir = f'LVER_发布包_{timestamp}'
    
    if os.path.exists(dist_dir):
        shutil.rmtree(dist_dir)
    
    os.makedirs(dist_dir)
    
    # 复制可执行文件
    shutil.copy2(exe_path, dist_dir)
    
    # 创建使用说明
    readme_content = f"""# LVER 串口通信工具

## 版本信息
- 构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 版本: 增强版 (分类导出功能)

## 系统要求
- Windows 7/8/10/11 (64位)
- 无需安装Python环境

## 功能特性
✅ 串口通信 (支持COM1-COM20)
✅ 多参数测量 (电压、SEI电阻、CT电阻、欧姆电阻)
✅ 实时数据显示和图表
✅ 连续测试功能
✅ 多种导出格式:
   • 统一导出 (单CSV文件)
   • 分类导出 (多CSV文件)
   • Excel格式导出 (多工作表)

## 使用方法
1. 双击 "LVER_串口通信工具.exe" 启动程序
2. 选择串口并连接设备
3. 使用测试命令获取数据
4. 查看实时数据显示和图表
5. 使用导出功能保存数据

## 支持的命令
- 测试命令 (0xAA): 返回4种参数
- 电压测量 (0x55): 返回电压值
- 复位命令 (0xA0): 设备复位

## 导出功能
- **统一导出**: 所有数据导出到单个CSV文件
- **分类导出**: 每种参数类型独立的CSV文件
- **Excel导出**: 多工作表Excel文件，专业格式

## 故障排除
如果程序无法启动:
1. 确认Windows版本兼容性
2. 检查防病毒软件是否阻止
3. 以管理员身份运行
4. 检查串口设备连接

## 技术支持
如有问题请联系技术支持。

---
LVER 串口通信工具 - 增强版
构建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    readme_path = os.path.join(dist_dir, 'README.txt')
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"✓ 分发包已创建: {dist_dir}")
    print(f"✓ 包含文件:")
    print(f"  • LVER_串口通信工具.exe")
    print(f"  • README.txt")
    
    # 显示分发包信息
    exe_size = os.path.getsize(os.path.join(dist_dir, 'LVER_串口通信工具.exe')) / (1024 * 1024)
    print(f"✓ 可执行文件大小: {exe_size:.1f} MB")
    
    return True

def main():
    """主函数"""
    print("LVER 应用程序打包工具")
    print("=" * 60)
    print("将Python应用程序打包成独立的Windows可执行文件")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装缺少的包后重试")
        return False
    
    # 构建可执行文件
    if not build_executable():
        print("\n❌ 构建失败")
        return False
    
    # 创建分发包
    if not create_distribution_package():
        print("\n❌ 分发包创建失败")
        return False
    
    # 测试可执行文件
    test_executable()
    
    print("\n" + "=" * 60)
    print("✅ 打包完成!")
    print("=" * 60)
    print("可执行文件位置: dist/LVER_串口通信工具.exe")
    print("分发包已创建，包含使用说明")
    print("可以将分发包复制到其他Windows系统使用")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    main()
