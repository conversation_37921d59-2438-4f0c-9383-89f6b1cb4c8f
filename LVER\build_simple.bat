@echo off
chcp 65001 >nul
echo ======================================================================
echo LVER 应用程序打包脚本
echo ======================================================================
echo.
echo 正在将Python应用程序打包成Windows可执行文件...
echo 这可能需要几分钟时间，请耐心等待...
echo.

REM 清理之前的构建文件
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del *.spec

echo 开始打包...
pyinstaller --onefile --windowed --name="LVER_串口通信工具" ^
    --hidden-import=tkinter ^
    --hidden-import=tkinter.ttk ^
    --hidden-import=tkinter.messagebox ^
    --hidden-import=tkinter.filedialog ^
    --hidden-import=matplotlib ^
    --hidden-import=matplotlib.backends.backend_tkagg ^
    --hidden-import=serial ^
    --hidden-import=serial.tools.list_ports ^
    --hidden-import=openpyxl ^
    --hidden-import=openpyxl.styles ^
    --hidden-import=csv ^
    --hidden-import=threading ^
    --hidden-import=datetime ^
    --add-data="*.py;." ^
    main_fixed_complete.py

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ======================================================================
    echo ✅ 打包成功完成!
    echo ======================================================================
    echo 可执行文件位置: dist\LVER_串口通信工具.exe
    echo.
    if exist "dist\LVER_串口通信工具.exe" (
        for %%A in ("dist\LVER_串口通信工具.exe") do (
            set size=%%~zA
            set /a sizeMB=!size!/1024/1024
        )
        echo 文件大小: !sizeMB! MB
    )
    echo.
    echo 您现在可以:
    echo 1. 运行 dist\LVER_串口通信工具.exe 测试应用程序
    echo 2. 将exe文件复制到其他Windows系统使用
    echo 3. 无需安装Python环境即可运行
    echo ======================================================================
    
    set /p choice="是否要启动可执行文件进行测试? (y/n): "
    if /i "%choice%"=="y" (
        echo 启动应用程序...
        start "" "dist\LVER_串口通信工具.exe"
    )
) else (
    echo.
    echo ======================================================================
    echo ❌ 打包失败
    echo ======================================================================
    echo 请检查:
    echo 1. 是否安装了PyInstaller: pip install pyinstaller
    echo 2. 是否安装了所需依赖: pip install matplotlib pyserial openpyxl
    echo 3. main_fixed_complete.py 文件是否存在
    echo ======================================================================
)

echo.
pause
