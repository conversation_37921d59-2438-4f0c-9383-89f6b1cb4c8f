#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COM端口检测和修复工具
"""

import serial.tools.list_ports
import subprocess
import time

def list_all_com_ports():
    """列出所有可用的COM端口"""
    print("🔍 检测系统中的所有COM端口...")
    print("=" * 50)
    
    # 方法1: 使用pyserial检测
    try:
        ports = serial.tools.list_ports.comports()
        if ports:
            print("✅ 发现以下COM端口:")
            for i, port in enumerate(ports, 1):
                print(f"  {i}. {port.device}")
                print(f"     描述: {port.description}")
                print(f"     硬件ID: {port.hwid}")
                print(f"     制造商: {port.manufacturer}")
                print()
        else:
            print("❌ 未发现任何COM端口")
    except Exception as e:
        print(f"❌ COM端口检测失败: {e}")
    
    # 方法2: 使用Windows命令检测
    print("\n🔍 使用Windows命令检测COM端口...")
    try:
        result = subprocess.run(['mode'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            com_lines = [line for line in lines if 'COM' in line.upper()]
            if com_lines:
                print("✅ Windows系统检测到:")
                for line in com_lines:
                    if line.strip():
                        print(f"  {line.strip()}")
            else:
                print("❌ Windows系统未检测到COM端口")
    except Exception as e:
        print(f"❌ Windows命令检测失败: {e}")

def check_usb_devices():
    """检查USB设备"""
    print("\n🔍 检测USB转串口设备...")
    print("=" * 50)
    
    try:
        # 使用PowerShell检测USB设备
        ps_command = """
        Get-WmiObject -Class Win32_PnPEntity | Where-Object {
            $_.Name -like '*USB*' -and ($_.Name -like '*Serial*' -or $_.Name -like '*UART*' -or $_.Name -like '*COM*' -or $_.Name -like '*CP210*')
        } | Select-Object Name, Status, DeviceID
        """
        
        result = subprocess.run(['powershell', '-Command', ps_command], 
                              capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0 and result.stdout.strip():
            print("✅ 发现USB转串口设备:")
            print(result.stdout)
        else:
            print("❌ 未发现USB转串口设备")
            
    except Exception as e:
        print(f"❌ USB设备检测失败: {e}")

def check_device_manager():
    """检查设备管理器中的端口状态"""
    print("\n🔍 检查设备管理器中的端口状态...")
    print("=" * 50)
    
    try:
        # 检查端口设备状态
        ps_command = """
        Get-WmiObject -Class Win32_PnPEntity | Where-Object {
            $_.Name -like '*COM*' -or $_.Name -like '*端口*'
        } | Select-Object Name, Status, ConfigManagerErrorCode, DeviceID
        """
        
        result = subprocess.run(['powershell', '-Command', ps_command], 
                              capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0 and result.stdout.strip():
            print("✅ 设备管理器中的端口状态:")
            print(result.stdout)
        else:
            print("❌ 设备管理器中未发现端口设备")
            
    except Exception as e:
        print(f"❌ 设备管理器检查失败: {e}")

def suggest_solutions():
    """提供解决方案建议"""
    print("\n💡 解决方案建议:")
    print("=" * 50)
    print("1. 🔌 USB设备连接问题:")
    print("   • 确保USB转串口设备已正确插入")
    print("   • 尝试更换USB端口")
    print("   • 检查USB线缆是否完好")
    print()
    print("2. 🔧 驱动程序问题:")
    print("   • 下载并安装最新的CP210x驱动程序")
    print("   • 在设备管理器中更新驱动程序")
    print("   • 卸载并重新安装驱动程序")
    print()
    print("3. 🖥️ 系统问题:")
    print("   • 重启计算机")
    print("   • 检查Windows更新")
    print("   • 运行硬件故障排除程序")
    print()
    print("4. 🔄 设备重置:")
    print("   • 在设备管理器中禁用并重新启用设备")
    print("   • 拔出设备等待10秒后重新插入")
    print("   • 尝试在其他计算机上测试设备")

def test_alternative_ports():
    """测试其他可用端口"""
    print("\n🧪 测试其他可用端口...")
    print("=" * 50)
    
    try:
        ports = serial.tools.list_ports.comports()
        if ports:
            for port in ports:
                try:
                    print(f"测试端口: {port.device}")
                    ser = serial.Serial(port.device, 9600, timeout=1)
                    print(f"✅ {port.device} 可以正常访问")
                    ser.close()
                except Exception as e:
                    print(f"❌ {port.device} 访问失败: {e}")
        else:
            print("❌ 没有可测试的端口")
    except Exception as e:
        print(f"❌ 端口测试失败: {e}")

def main():
    """主函数"""
    print("🔋 鲸测云LCER电池测试仪 - COM端口检测工具")
    print("=" * 60)
    
    # 检测所有COM端口
    list_all_com_ports()
    
    # 检查USB设备
    check_usb_devices()
    
    # 检查设备管理器
    check_device_manager()
    
    # 测试其他端口
    test_alternative_ports()
    
    # 提供解决方案
    suggest_solutions()
    
    print("\n" + "=" * 60)
    print("🎯 检测完成!")
    print("如果COM5仍未出现，请按照上述建议进行排查。")
    print("\n按回车键退出...")
    input()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 检测工具异常: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
