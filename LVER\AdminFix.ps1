# Admin COM5 Fix and Launch Script

Write-Host "LCER Battery Tester - Admin COM5 Fix" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Gray

# Check if running as administrator
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "Requesting administrator privileges..." -ForegroundColor Yellow
    Start-Process PowerShell -Verb RunAs -ArgumentList "-File `"$PSCommandPath`""
    exit
}

Write-Host "Running with administrator privileges" -ForegroundColor Green

# Change to application directory
Set-Location "D:\code\haha\LVER"

# Step 1: Kill processes that might be using COM5
Write-Host "`nStep 1: Killing serial port processes..." -ForegroundColor Cyan
$processNames = @("sscom32", "sscom", "putty", "teraterm", "hyperterminal", "serialport", "comtool")
$killedCount = 0

foreach ($processName in $processNames) {
    try {
        $processes = Get-Process -Name $processName -ErrorAction SilentlyContinue
        if ($processes) {
            $processes | Stop-Process -Force
            Write-Host "Killed: $processName" -ForegroundColor Green
            $killedCount++
        }
    } catch {
        # Ignore errors
    }
}

if ($killedCount -gt 0) {
    Write-Host "Killed $killedCount processes" -ForegroundColor Green
    Start-Sleep -Seconds 2
} else {
    Write-Host "No processes to kill" -ForegroundColor Yellow
}

# Step 2: Reset COM5 port
Write-Host "`nStep 2: Resetting COM5 port..." -ForegroundColor Cyan
try {
    $com5Device = Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like "*COM5*"}
    if ($com5Device) {
        Write-Host "Found COM5 device: $($com5Device.Name)" -ForegroundColor Yellow
        $com5Device.Disable()
        Start-Sleep -Seconds 2
        $com5Device.Enable()
        Start-Sleep -Seconds 2
        Write-Host "COM5 port reset completed" -ForegroundColor Green
    } else {
        Write-Host "COM5 device not found" -ForegroundColor Red
    }
} catch {
    Write-Host "Port reset failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 3: Test COM5 access
Write-Host "`nStep 3: Testing COM5 access..." -ForegroundColor Cyan
try {
    $pythonPath = "D:\code\haha\.venv\Scripts\python.exe"
    $testScript = @"
import serial
try:
    ser = serial.Serial('COM5', 9600, timeout=1)
    print('SUCCESS: COM5 access OK')
    ser.close()
except Exception as e:
    print(f'FAILED: {e}')
"@
    
    $testResult = & $pythonPath -c $testScript
    Write-Host "Test result: $testResult" -ForegroundColor $(if ($testResult -like "*SUCCESS*") { "Green" } else { "Red" })
} catch {
    Write-Host "Test failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Step 4: Launch application
Write-Host "`nStep 4: Launching application..." -ForegroundColor Cyan
try {
    $pythonPath = "D:\code\haha\.venv\Scripts\python.exe"
    $scriptPath = "main_fixed_complete.py"
    
    if (Test-Path $pythonPath -and Test-Path $scriptPath) {
        Start-Process -FilePath $pythonPath -ArgumentList $scriptPath -WorkingDirectory (Get-Location)
        Write-Host "Application launched successfully" -ForegroundColor Green
        Write-Host "Please test COM5 connection in the new window" -ForegroundColor Yellow
    } else {
        Write-Host "Python or script not found" -ForegroundColor Red
    }
} catch {
    Write-Host "Launch failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=====================================" -ForegroundColor Gray
Write-Host "Admin fix completed. Press Enter to exit..." -ForegroundColor Green
Read-Host
