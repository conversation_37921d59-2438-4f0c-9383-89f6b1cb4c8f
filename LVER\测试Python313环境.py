#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Python 3.13环境下的串口功能
验证是否能正确检测和连接COM5
"""

import sys
from datetime import datetime

def test_environment():
    """测试Python环境"""
    print("🔋 鲸测云LCER电池测试仪 - Python 3.13环境测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")

def test_imports():
    """测试模块导入"""
    print("\n📦 测试模块导入...")
    
    modules = [
        ("serial", "pyserial串口通信"),
        ("tkinter", "GUI界面"),
        ("matplotlib", "图表显示"),
        ("openpyxl", "Excel导出")
    ]
    
    all_ok = True
    for module_name, description in modules:
        try:
            if module_name == "serial":
                import serial
                import serial.tools.list_ports
                print(f"✅ {module_name}: {description} (版本: {serial.VERSION})")
            else:
                __import__(module_name)
                print(f"✅ {module_name}: {description}")
        except ImportError as e:
            print(f"❌ {module_name}: {description} - 导入失败: {e}")
            all_ok = False
    
    return all_ok

def test_serial_ports():
    """测试串口检测"""
    print("\n🔌 测试串口检测...")
    
    try:
        import serial.tools.list_ports
        
        ports = list(serial.tools.list_ports.comports())
        print(f"发现串口数量: {len(ports)}")
        
        if ports:
            for port in ports:
                print(f"  📍 {port.device}: {port.description}")
                if port.device == 'COM5':
                    print(f"    ✅ 发现目标设备COM5!")
        else:
            print("  ⚠️  未发现任何串口")
        
        # 检查COM5
        com5_found = any(port.device == 'COM5' for port in ports)
        if com5_found:
            print("\n🎯 COM5状态: ✅ 已发现")
            return True
        else:
            print("\n🎯 COM5状态: ❌ 未发现")
            return False
            
    except Exception as e:
        print(f"❌ 串口检测失败: {e}")
        return False

def test_serial_connection():
    """测试串口连接"""
    print("\n🔗 测试COM5连接...")
    
    try:
        import serial
        
        # 尝试连接COM5
        conn = serial.Serial(
            port='COM5',
            baudrate=9600,
            timeout=1,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE
        )
        
        print("✅ COM5连接成功!")
        print(f"  端口: {conn.port}")
        print(f"  波特率: {conn.baudrate}")
        print(f"  状态: {'打开' if conn.is_open else '关闭'}")
        
        # 关闭连接
        conn.close()
        print("✅ 连接已正常关闭")
        return True
        
    except Exception as e:
        print(f"❌ COM5连接失败: {e}")
        return False

def main():
    """主测试函数"""
    # 1. 测试环境
    test_environment()
    
    # 2. 测试模块导入
    imports_ok = test_imports()
    
    # 3. 测试串口检测
    ports_ok = test_serial_ports()
    
    # 4. 测试串口连接
    connection_ok = test_serial_connection()
    
    # 5. 总结
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print(f"  模块导入: {'✅ 通过' if imports_ok else '❌ 失败'}")
    print(f"  串口检测: {'✅ 通过' if ports_ok else '❌ 失败'}")
    print(f"  COM5连接: {'✅ 通过' if connection_ok else '❌ 失败'}")
    
    if imports_ok and ports_ok and connection_ok:
        print("\n🎉 环境测试完全通过!")
        print("💡 现在可以运行主程序获取真实数据:")
        print("   命令: py main_fixed_complete.py")
        print("\n📋 预期效果:")
        print("  ✅ 程序启动显示'✅ pyserial 可用'")
        print("  ✅ 连接COM5不显示'模拟连接'")
        print("  ✅ 测试命令返回真实变化的数据")
        print("  ✅ 电压值不再固定为3290 mV")
        return True
    else:
        print("\n❌ 环境测试存在问题")
        print("💡 请解决上述问题后再运行主程序")
        return False

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n退出代码: {0 if success else 1}")
        input("\n按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程出现异常: {e}")
        import traceback
        traceback.print_exc()
