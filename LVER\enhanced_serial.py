"""
增强的串口管理模块
提供自动检测、自动连接、多波特率尝试等功能
"""

import serial
import serial.tools.list_ports
import time
import threading
from typing import List, Optional, Callable, Tuple
from error_handler import global_error_handler, ErrorType


class EnhancedSerialManager:
    """增强的串口管理器"""
    
    def __init__(self):
        self.serial_port = None
        self.is_connected = False
        self.current_port = None
        self.current_baudrate = None
        self.error_handler = global_error_handler
        
        # 常用波特率列表（按优先级排序）
        self.common_baudrates = [115200, 9600, 38400, 57600, 19200, 4800]
        
        # 串口监控
        self.port_monitor_thread = None
        self.port_monitor_running = False
        self.port_change_callback = None
        self.last_ports = set()
        
    def get_available_ports(self) -> List[Tuple[str, str]]:
        """
        获取可用串口列表
        
        Returns:
            List[Tuple[str, str]]: [(端口名, 描述), ...]
        """
        try:
            ports = serial.tools.list_ports.comports()
            port_list = [(port.device, port.description) for port in ports]
            self.error_handler.log_debug(f"发现 {len(port_list)} 个串口: {[p[0] for p in port_list]}")
            return port_list
        except Exception as e:
            self.error_handler.handle_serial_error("获取串口列表", e)
            return []
    
    def start_port_monitoring(self, callback: Callable[[List[Tuple[str, str]]], None]):
        """
        开始监控串口变化
        
        Args:
            callback: 串口列表变化时的回调函数
        """
        self.port_change_callback = callback
        self.port_monitor_running = True
        
        if self.port_monitor_thread is None or not self.port_monitor_thread.is_alive():
            self.port_monitor_thread = threading.Thread(target=self._port_monitor_worker, daemon=True)
            self.port_monitor_thread.start()
            self.error_handler.log_debug("串口监控已启动")
    
    def stop_port_monitoring(self):
        """停止监控串口变化"""
        self.port_monitor_running = False
        if self.port_monitor_thread and self.port_monitor_thread.is_alive():
            self.port_monitor_thread.join(timeout=1)
        self.error_handler.log_debug("串口监控已停止")
    
    def _port_monitor_worker(self):
        """串口监控工作线程"""
        while self.port_monitor_running:
            try:
                current_ports = set(port[0] for port in self.get_available_ports())
                
                if current_ports != self.last_ports:
                    self.error_handler.log_debug(f"串口列表变化: {current_ports}")
                    self.last_ports = current_ports
                    
                    if self.port_change_callback:
                        port_list = self.get_available_ports()
                        self.port_change_callback(port_list)
                
                time.sleep(2)  # 每2秒检查一次
                
            except Exception as e:
                self.error_handler.handle_serial_error("串口监控", e)
                time.sleep(5)  # 出错时等待更长时间
    
    def auto_connect(self, preferred_port: Optional[str] = None) -> bool:
        """
        自动连接串口
        
        Args:
            preferred_port: 首选端口，如果为None则自动选择
            
        Returns:
            bool: 连接是否成功
        """
        available_ports = self.get_available_ports()
        
        if not available_ports:
            self.error_handler.handle_error(ErrorType.SERIAL_CONNECTION, "未发现可用串口")
            return False
        
        # 确定要尝试的端口列表
        ports_to_try = []
        
        if preferred_port:
            # 如果指定了首选端口，优先尝试
            for port, desc in available_ports:
                if port == preferred_port:
                    ports_to_try.append((port, desc))
                    break
        
        # 添加其他端口
        for port, desc in available_ports:
            if port != preferred_port:
                ports_to_try.append((port, desc))
        
        # 尝试连接每个端口
        for port, desc in ports_to_try:
            self.error_handler.log_debug(f"尝试连接端口: {port} ({desc})")
            
            if self._try_connect_port(port):
                self.error_handler.log_info(f"成功连接到 {port} (波特率: {self.current_baudrate})")
                return True
        
        self.error_handler.handle_error(ErrorType.SERIAL_CONNECTION, "所有端口连接尝试均失败")
        return False
    
    def _try_connect_port(self, port: str) -> bool:
        """
        尝试连接指定端口，自动尝试多个波特率
        
        Args:
            port: 端口名
            
        Returns:
            bool: 连接是否成功
        """
        for baudrate in self.common_baudrates:
            try:
                self.error_handler.log_debug(f"尝试 {port} 波特率 {baudrate}")
                
                # 尝试连接
                test_serial = serial.Serial(
                    port=port,
                    baudrate=baudrate,
                    bytesize=serial.EIGHTBITS,
                    parity=serial.PARITY_NONE,
                    stopbits=serial.STOPBITS_ONE,
                    timeout=1,
                    write_timeout=1
                )
                
                # 简单的连接测试
                test_serial.reset_input_buffer()
                test_serial.reset_output_buffer()
                
                # 保存连接信息
                self.serial_port = test_serial
                self.is_connected = True
                self.current_port = port
                self.current_baudrate = baudrate
                
                return True
                
            except Exception as e:
                self.error_handler.log_debug(f"端口 {port} 波特率 {baudrate} 连接失败: {e}")
                continue
        
        return False
    
    def connect(self, port: str, baudrate: int) -> bool:
        """
        连接指定端口和波特率
        
        Args:
            port: 端口名
            baudrate: 波特率
            
        Returns:
            bool: 连接是否成功
        """
        try:
            if self.is_connected:
                self.disconnect()
            
            self.error_handler.log_info(f"尝试连接串口 {port}, 波特率: {baudrate}")
            
            self.serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=2,
                write_timeout=2
            )
            
            self.is_connected = True
            self.current_port = port
            self.current_baudrate = baudrate
            
            self.error_handler.log_info(f"成功连接到串口 {port}")
            return True
            
        except Exception as e:
            self.error_handler.handle_serial_error("连接串口", e)
            return False
    
    def disconnect(self):
        """断开串口连接"""
        try:
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
                self.error_handler.log_info("串口连接已断开")
        except Exception as e:
            self.error_handler.handle_serial_error("断开串口连接", e)
        finally:
            self.is_connected = False
            self.serial_port = None
            self.current_port = None
            self.current_baudrate = None
    
    def get_connection_info(self) -> Optional[Tuple[str, int]]:
        """
        获取当前连接信息
        
        Returns:
            Optional[Tuple[str, int]]: (端口名, 波特率) 或 None
        """
        if self.is_connected and self.current_port and self.current_baudrate:
            return (self.current_port, self.current_baudrate)
        return None
    
    def __del__(self):
        """析构函数"""
        self.stop_port_monitoring()
        self.disconnect()
