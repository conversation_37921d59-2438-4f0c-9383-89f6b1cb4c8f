@echo off
echo 🔋 启动鲸测云LCER电池测试仪 - 真实数据版本
echo ================================================
echo.

echo 📋 检查Python环境...
D:\code\haha\.venv\Scripts\python.exe -c "import serial; print('✅ pyserial版本:', serial.VERSION)"
if %errorlevel% neq 0 (
    echo ❌ pyserial未安装，无法获取真实数据
    pause
    exit /b 1
)

echo.
echo 🚀 启动应用程序...
echo 💡 提示: 此版本将使用真实串口通信，不再显示模拟数据
echo.

D:\code\haha\.venv\Scripts\python.exe main_fixed_complete.py

echo.
echo 程序已退出
pause
