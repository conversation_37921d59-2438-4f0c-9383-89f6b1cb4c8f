#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断真实串口连接
检查是否能连接到真实的COM5设备并获取真实数据
"""

import sys
import time
from datetime import datetime

def check_pyserial():
    """检查pyserial是否可用"""
    print("🔍 检查pyserial库...")
    try:
        import serial
        import serial.tools.list_ports
        print("✅ pyserial库已安装")
        return True, serial
    except ImportError:
        print("❌ pyserial库未安装")
        print("💡 请运行: pip install pyserial")
        return False, None

def list_available_ports(serial_module):
    """列出可用串口"""
    print("\n📋 扫描可用串口...")
    try:
        ports = serial_module.tools.list_ports.comports()
        if ports:
            print(f"✅ 发现 {len(ports)} 个串口:")
            for port in ports:
                print(f"   📍 {port.device}: {port.description}")
            return [port.device for port in ports]
        else:
            print("❌ 未发现任何串口")
            return []
    except Exception as e:
        print(f"❌ 扫描串口失败: {e}")
        return []

def test_real_connection(serial_module, port):
    """测试真实串口连接"""
    print(f"\n🔌 测试连接到 {port}...")
    
    try:
        # 尝试连接真实串口
        conn = serial_module.Serial(
            port=port,
            baudrate=9600,
            timeout=2,
            parity=serial_module.PARITY_NONE,
            stopbits=serial_module.STOPBITS_ONE
        )
        
        print(f"✅ 成功连接到 {port}")
        print(f"   波特率: {conn.baudrate}")
        print(f"   超时: {conn.timeout}s")
        print(f"   状态: {'打开' if conn.is_open else '关闭'}")
        
        return conn
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return None

def send_test_command(conn):
    """发送测试命令并读取响应"""
    print(f"\n🧪 发送测试命令 0xAA...")
    
    try:
        # 清空输入缓冲区
        if hasattr(conn, 'reset_input_buffer'):
            conn.reset_input_buffer()
        
        # 发送命令
        command = b'\xAA'
        bytes_sent = conn.write(command)
        print(f"📤 发送了 {bytes_sent} 字节: {command.hex().upper()}")
        
        # 等待响应
        print("⏳ 等待设备响应...")
        start_time = time.time()
        response_data = ""
        
        while time.time() - start_time < 5.0:  # 5秒超时
            if conn.in_waiting > 0:
                chunk = conn.read(conn.in_waiting).decode('utf-8', errors='ignore')
                response_data += chunk
                print(f"📥 接收数据: {repr(chunk)}")
                
                # 检查是否接收完整
                if '\n' in response_data and ',' in response_data:
                    break
            
            time.sleep(0.1)
        
        if response_data:
            print(f"\n📋 完整响应数据:")
            print(f"   原始数据: {repr(response_data)}")
            
            # 分析响应
            lines = response_data.strip().split('\n')
            print(f"   响应行数: {len(lines)}")
            
            csv_lines = []
            debug_lines = []
            
            for i, line in enumerate(lines):
                line = line.strip()
                if ',' in line:
                    csv_lines.append(line)
                    print(f"   CSV数据[{i}]: {line}")
                else:
                    debug_lines.append(line)
                    print(f"   调试信息[{i}]: {line}")
            
            if csv_lines:
                print(f"\n✅ 发现 {len(csv_lines)} 行CSV数据")
                for csv_line in csv_lines:
                    try:
                        parts = csv_line.split(',')
                        if len(parts) >= 3:
                            param_id = parts[0].strip()
                            param_type = parts[1].strip()
                            param_value = parts[2].strip()
                            print(f"   解析: ID={param_id}, 类型={param_type}, 值={param_value}")
                    except Exception as e:
                        print(f"   解析失败: {e}")
                
                return True, csv_lines
            else:
                print("❌ 未发现CSV格式数据")
                return False, []
        else:
            print("❌ 未接收到任何响应数据")
            return False, []
            
    except Exception as e:
        print(f"❌ 测试命令失败: {e}")
        return False, []

def main():
    """主诊断函数"""
    print("🔋 鲸测云LCER电池测试仪 - 真实串口诊断")
    print("=" * 60)
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查pyserial
    has_pyserial, serial_module = check_pyserial()
    if not has_pyserial:
        print("\n❌ 无法进行真实串口测试")
        print("💡 解决方案: pip install pyserial")
        return False
    
    # 2. 扫描串口
    available_ports = list_available_ports(serial_module)
    if not available_ports:
        print("\n❌ 未发现任何串口设备")
        return False
    
    # 3. 检查COM5
    if 'COM5' not in available_ports:
        print(f"\n⚠️  COM5不在可用串口列表中")
        print(f"可用串口: {available_ports}")
        
        # 尝试其他串口
        for port in available_ports:
            if 'COM' in port:
                print(f"\n🔄 尝试连接 {port}...")
                conn = test_real_connection(serial_module, port)
                if conn:
                    success, data = send_test_command(conn)
                    conn.close()
                    if success:
                        print(f"✅ {port} 可以获取真实数据")
                        return True
                    else:
                        print(f"❌ {port} 无法获取有效数据")
        
        return False
    
    # 4. 测试COM5连接
    conn = test_real_connection(serial_module, 'COM5')
    if not conn:
        return False
    
    # 5. 发送测试命令
    success, csv_data = send_test_command(conn)
    
    # 6. 关闭连接
    conn.close()
    print(f"\n🔌 已断开连接")
    
    # 7. 总结
    print("\n" + "=" * 60)
    if success:
        print("🎉 诊断结果: COM5可以获取真实设备数据")
        print("✅ 您的设备连接正常")
        print("💡 问题可能在于主程序的数据处理逻辑")
        
        print("\n📋 建议操作:")
        print("1. 确认主程序使用真实串口而非模拟串口")
        print("2. 检查数据解析逻辑是否正确")
        print("3. 验证程序是否正确处理接收到的数据")
        
    else:
        print("❌ 诊断结果: 无法从COM5获取有效数据")
        print("💡 可能的原因:")
        print("1. 设备未正确连接")
        print("2. 设备不支持0xAA命令")
        print("3. 波特率或其他参数不匹配")
        print("4. 设备需要特殊的初始化序列")
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        print(f"\n退出代码: {0 if success else 1}")
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断诊断")
    except Exception as e:
        print(f"\n❌ 诊断过程出现异常: {e}")
        import traceback
        traceback.print_exc()
