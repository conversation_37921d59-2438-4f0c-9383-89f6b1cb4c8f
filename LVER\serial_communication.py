"""
串口通信核心模块
实现与设备的串口通信功能
"""

import serial
import serial.tools.list_ports
import time
import threading
from typing import List, Optional, Callable
from error_handler import global_error_handler, ErrorType, ResponseValidator


class SerialCommunication:
    """串口通信类"""
    
    def __init__(self):
        self.serial_port: Optional[serial.Serial] = None
        self.is_connected = False
        self.timeout = 2.0  # 2秒超时
        self.baudrate = 9600  # 默认波特率
        self.response_callback: Optional[Callable] = None
        self.error_handler = global_error_handler
        self.validator = ResponseValidator(self.error_handler)
        
    def get_available_ports(self) -> List[str]:
        """获取可用的串口列表"""
        try:
            ports = serial.tools.list_ports.comports()
            port_list = [port.device for port in ports]
            self.error_handler.log_debug(f"发现 {len(port_list)} 个串口: {port_list}")
            return port_list
        except Exception as e:
            self.error_handler.handle_error(ErrorType.HARDWARE, "获取串口列表失败", e)
            return []
    
    def connect(self, port: str, baudrate: int = 9600) -> bool:
        """
        连接到指定串口

        Args:
            port: 串口名称 (如 'COM1')
            baudrate: 波特率

        Returns:
            bool: 连接是否成功
        """
        try:
            if self.is_connected:
                self.disconnect()

            self.error_handler.log_info(f"尝试连接串口 {port}, 波特率: {baudrate}")

            self.serial_port = serial.Serial(
                port=port,
                baudrate=baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=self.timeout
            )

            self.is_connected = True
            self.baudrate = baudrate
            self.error_handler.log_info(f"成功连接到串口 {port}")
            return True

        except Exception as e:
            self.error_handler.handle_serial_error(f"连接串口 {port}", e)
            self.is_connected = False
            return False
    
    def disconnect(self):
        """断开串口连接"""
        try:
            if self.serial_port and self.serial_port.is_open:
                self.serial_port.close()
                self.error_handler.log_info("串口连接已断开")
            self.is_connected = False
            self.serial_port = None
        except Exception as e:
            self.error_handler.handle_serial_error("断开串口连接", e)
    
    def send_command(self, command: bytes) -> Optional[str]:
        """
        发送命令并接收响应

        Args:
            command: 要发送的命令字节

        Returns:
            str: 接收到的响应，如果失败返回None
        """
        if not self.is_connected or not self.serial_port:
            self.error_handler.handle_error(ErrorType.SERIAL_CONNECTION, "串口未连接", show_user=False)
            return None

        try:
            # 清空输入缓冲区
            self.serial_port.reset_input_buffer()

            # 发送命令
            self.serial_port.write(command)
            self.serial_port.flush()

            # 接收响应
            response = ""
            start_time = time.time()

            while time.time() - start_time < self.timeout:
                if self.serial_port.in_waiting > 0:
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    response += data.decode('utf-8', errors='ignore')

                    # 检查是否接收完整（以换行符结束）
                    if '\n' in response or '\r' in response:
                        break

                time.sleep(0.01)  # 短暂等待

            return response.strip() if response else None

        except Exception as e:
            self.error_handler.handle_serial_error("发送命令", e)
            return None

    def _send_single_response_command(self, command: bytes) -> Optional[str]:
        """
        发送命令并接收单行响应（专用于电压测量命令）

        Args:
            command: 要发送的命令字节

        Returns:
            str: 接收到的响应，如果失败返回None
        """
        if not self.is_connected or not self.serial_port:
            self.error_handler.handle_error(ErrorType.SERIAL_CONNECTION, "串口未连接", show_user=False)
            return None

        try:
            # 清空输入缓冲区
            self.serial_port.reset_input_buffer()

            # 发送命令
            self.serial_port.write(command)
            self.serial_port.flush()

            # 等待更长时间以确保设备响应
            time.sleep(0.1)

            # 接收响应，使用更长的超时时间
            response = ""
            start_time = time.time()
            extended_timeout = self.timeout * 2  # 双倍超时时间

            while time.time() - start_time < extended_timeout:
                if self.serial_port.in_waiting > 0:
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    response += data.decode('utf-8', errors='ignore')

                    # 检查是否接收到完整的CSV行
                    lines = response.strip().split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and ',' in line:
                            # 找到有效的CSV行
                            self.error_handler.log_debug(f"接收到电压响应: '{line}'")
                            return line

                time.sleep(0.05)  # 稍长的等待时间

            # 如果有部分响应，记录并返回
            if response.strip():
                self.error_handler.log_debug(f"部分响应: '{response.strip()}'")
                return response.strip()

            return None

        except Exception as e:
            self.error_handler.handle_serial_error("发送单响应命令", e)
            return None
    
    def send_test_command(self) -> Optional[List[str]]:
        """
        发送测试命令 (0xAA)

        Returns:
            List[str]: 四行响应数据，如果失败返回None
        """
        if not self.is_connected:
            self.error_handler.handle_error(ErrorType.SERIAL_CONNECTION, "串口未连接", show_user=False)
            return None

        try:
            self.error_handler.log_debug("发送测试命令 0xAA")

            # 清空输入缓冲区
            self.serial_port.reset_input_buffer()

            # 发送测试命令
            self.serial_port.write(bytes([0xAA]))
            self.serial_port.flush()

            # 接收四行响应
            responses = []
            start_time = time.time()
            buffer = ""

            while len(responses) < 4 and time.time() - start_time < self.timeout:
                if self.serial_port.in_waiting > 0:
                    data = self.serial_port.read(self.serial_port.in_waiting)
                    buffer += data.decode('utf-8', errors='ignore')

                    # 处理完整的行
                    while '\n' in buffer or '\r' in buffer:
                        if '\r\n' in buffer:
                            line, buffer = buffer.split('\r\n', 1)
                        elif '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                        elif '\r' in buffer:
                            line, buffer = buffer.split('\r', 1)

                        if line.strip():
                            responses.append(line.strip())

                time.sleep(0.01)

            # 验证响应
            if len(responses) < 4:
                self.error_handler.handle_timeout_error("测试命令响应", self.timeout)
                return None

            if not self.validator.validate_test_response(responses):
                return None

            self.error_handler.log_debug(f"测试命令响应: {responses}")
            return responses

        except Exception as e:
            self.error_handler.handle_serial_error("发送测试命令", e)
            return None
    
    def send_reset_command(self) -> bool:
        """
        发送重置命令 (0xA0)

        Returns:
            bool: 命令是否发送成功
        """
        if not self.is_connected or not self.serial_port:
            self.error_handler.handle_error(ErrorType.SERIAL_CONNECTION, "串口未连接", show_user=False)
            return False

        try:
            self.error_handler.log_debug("发送重置命令 0xA0")
            self.serial_port.write(bytes([0xA0]))
            self.serial_port.flush()
            self.error_handler.log_info("重置命令发送成功")
            return True

        except Exception as e:
            self.error_handler.handle_serial_error("发送重置命令", e)
            return False
    
    def send_voltage_command(self) -> Optional[str]:
        """
        发送电压测量命令 (0x55)

        Returns:
            str: 电压响应数据，如果失败返回None
        """
        if not self.is_connected:
            self.error_handler.handle_error(ErrorType.SERIAL_CONNECTION, "串口未连接", show_user=False)
            return None

        try:
            self.error_handler.log_debug("发送电压测量命令 0x55")

            # 使用更长的超时时间和特殊的接收逻辑
            response = self._send_single_response_command(bytes([0x55]))

            if response and self.validator.validate_voltage_response(response):
                self.error_handler.log_debug(f"电压测量响应: {response}")
                return response
            elif response:
                self.error_handler.handle_invalid_response("1,B2_Voltage,数值", response)
                self.error_handler.log_debug(f"原始响应: '{response}'")
                return None
            else:
                self.error_handler.handle_timeout_error("电压测量命令", self.timeout)
                return None

        except Exception as e:
            self.error_handler.handle_serial_error("发送电压测量命令", e)
            return None
    
    def set_response_callback(self, callback: Callable):
        """设置响应回调函数"""
        self.response_callback = callback
    
    def __del__(self):
        """析构函数，确保串口被正确关闭"""
        self.disconnect()
