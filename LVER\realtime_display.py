#!/usr/bin/env python3
"""
实时数值显示组件
提供专业的大字体数值显示界面，适合实时监控
"""

import tkinter as tk
from tkinter import ttk
from datetime import datetime
from typing import Optional, Dict, Any
import threading


class RealtimeValueCard:
    """单个数值显示卡片"""
    
    def __init__(self, parent, title: str, unit: str, color: str = "#333333", 
                 bg_color: str = "#f8f9fa", value_font_size: int = 24):
        self.parent = parent
        self.title = title
        self.unit = unit
        self.color = color
        self.bg_color = bg_color
        self.current_value = "--"
        self.last_value = None
        self.trend_direction = None  # "up", "down", "stable"
        
        # 创建卡片框架
        self.card_frame = tk.Frame(parent, bg=bg_color, relief="raised", bd=1)
        
        # 标题标签
        self.title_label = tk.Label(
            self.card_frame,
            text=title,
            font=("Arial", 10, "bold"),
            fg="#666666",
            bg=bg_color
        )
        self.title_label.pack(pady=(8, 2))
        
        # 数值显示框架
        value_frame = tk.Frame(self.card_frame, bg=bg_color)
        value_frame.pack(pady=(0, 2))
        
        # 主要数值标签
        self.value_label = tk.Label(
            value_frame,
            text=self.current_value,
            font=("Arial", value_font_size, "bold"),
            fg=color,
            bg=bg_color
        )
        self.value_label.pack(side=tk.LEFT)
        
        # 趋势指示器
        self.trend_label = tk.Label(
            value_frame,
            text="",
            font=("Arial", 16, "bold"),
            fg="#888888",
            bg=bg_color
        )
        self.trend_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 单位标签
        self.unit_label = tk.Label(
            self.card_frame,
            text=unit,
            font=("Arial", 9),
            fg="#888888",
            bg=bg_color
        )
        self.unit_label.pack(pady=(0, 8))
        
        # 闪烁效果相关
        self.flash_job = None
        self.original_bg = bg_color
        
    def pack(self, **kwargs):
        """打包卡片"""
        self.card_frame.pack(**kwargs)
        
    def grid(self, **kwargs):
        """网格布局卡片"""
        self.card_frame.grid(**kwargs)
        
    def update_value(self, new_value: str, animate: bool = True):
        """更新数值"""
        if new_value != self.current_value:
            # 计算趋势
            if self.current_value != "--" and new_value != "--":
                try:
                    old_val = float(self.current_value.replace(",", ""))
                    new_val = float(new_value.replace(",", ""))
                    if new_val > old_val:
                        self.trend_direction = "up"
                        self.trend_label.config(text="↑", fg="#28a745")
                    elif new_val < old_val:
                        self.trend_direction = "down"
                        self.trend_label.config(text="↓", fg="#dc3545")
                    else:
                        self.trend_direction = "stable"
                        self.trend_label.config(text="→", fg="#6c757d")
                except ValueError:
                    self.trend_label.config(text="")
            
            self.current_value = new_value
            self.value_label.config(text=new_value)
            
            # 闪烁动画
            if animate:
                self.flash_update()
    
    def flash_update(self):
        """闪烁效果表示数据更新"""
        if self.flash_job:
            self.card_frame.after_cancel(self.flash_job)
        
        # 变为高亮色
        highlight_color = "#e3f2fd"
        self.card_frame.config(bg=highlight_color)
        self.title_label.config(bg=highlight_color)
        self.value_label.config(bg=highlight_color)
        self.trend_label.config(bg=highlight_color)
        self.unit_label.config(bg=highlight_color)
        
        # 500ms后恢复原色
        self.flash_job = self.card_frame.after(500, self.restore_color)
    
    def restore_color(self):
        """恢复原始颜色"""
        self.card_frame.config(bg=self.original_bg)
        self.title_label.config(bg=self.original_bg)
        self.value_label.config(bg=self.original_bg)
        self.trend_label.config(bg=self.original_bg)
        self.unit_label.config(bg=self.original_bg)


class RealtimeDisplayWidget:
    """实时数值显示组件"""
    
    def __init__(self, parent, data_manager=None):
        self.parent = parent
        self.data_manager = data_manager
        
        # 创建主框架
        self.main_frame = ttk.LabelFrame(parent, text="实时数值监控", padding="10")
        
        # 创建数值卡片容器
        self.cards_frame = tk.Frame(self.main_frame, bg="#ffffff")
        self.cards_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # 创建数值卡片
        self.create_value_cards()
        
        # 创建状态信息区域
        self.create_status_section()
        
        # 数据更新频率统计
        self.update_count = 0
        self.last_update_time = None
        
    def create_value_cards(self):
        """创建数值显示卡片"""
        # 第一行：电压显示
        voltage_row = tk.Frame(self.cards_frame, bg="#ffffff")
        voltage_row.pack(fill=tk.X, pady=(0, 10))
        
        # 电压卡片（大卡片，跨两列）
        self.voltage_card = RealtimeValueCard(
            voltage_row, 
            title="电压", 
            unit="V", 
            color="#1976d2",  # 蓝色
            value_font_size=28
        )
        self.voltage_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 电压mV显示
        self.voltage_mv_card = RealtimeValueCard(
            voltage_row,
            title="电压 (mV)",
            unit="mV",
            color="#1565c0",  # 深蓝色
            value_font_size=20
        )
        self.voltage_mv_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 第二行：电阻值显示
        resistance_row = tk.Frame(self.cards_frame, bg="#ffffff")
        resistance_row.pack(fill=tk.X, pady=(0, 10))
        
        # R_ohm 电阻
        self.r_ohm_card = RealtimeValueCard(
            resistance_row,
            title="电阻 (R_ohm)",
            unit="μΩ",
            color="#388e3c",  # 绿色
            value_font_size=22
        )
        self.r_ohm_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # R_sei 电阻
        self.r_sei_card = RealtimeValueCard(
            resistance_row,
            title="SEI电阻 (R_sei)",
            unit="μΩ",
            color="#43a047",  # 浅绿色
            value_font_size=22
        )
        self.r_sei_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(2.5, 2.5))
        
        # R_ct 电阻
        self.r_ct_card = RealtimeValueCard(
            resistance_row,
            title="CT电阻 (R_ct)",
            unit="μΩ",
            color="#4caf50",  # 更浅绿色
            value_font_size=22
        )
        self.r_ct_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))
        
        # 第三行：统计信息
        stats_row = tk.Frame(self.cards_frame, bg="#ffffff")
        stats_row.pack(fill=tk.X)
        
        # 测试次数
        self.test_count_card = RealtimeValueCard(
            stats_row,
            title="测试次数",
            unit="次",
            color="#f57c00",  # 橙色
            value_font_size=20
        )
        self.test_count_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 更新频率
        self.update_rate_card = RealtimeValueCard(
            stats_row,
            title="更新频率",
            unit="次/分",
            color="#7b1fa2",  # 紫色
            value_font_size=18
        )
        self.update_rate_card.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(5, 0))
    
    def create_status_section(self):
        """创建状态信息区域"""
        status_frame = tk.Frame(self.main_frame, bg="#f8f9fa", relief="sunken", bd=1)
        status_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 最后更新时间
        time_frame = tk.Frame(status_frame, bg="#f8f9fa")
        time_frame.pack(fill=tk.X, padx=10, pady=8)
        
        tk.Label(
            time_frame,
            text="最后更新时间:",
            font=("Arial", 9, "bold"),
            fg="#495057",
            bg="#f8f9fa"
        ).pack(side=tk.LEFT)
        
        self.timestamp_label = tk.Label(
            time_frame,
            text="--",
            font=("Arial", 9),
            fg="#6c757d",
            bg="#f8f9fa"
        )
        self.timestamp_label.pack(side=tk.LEFT, padx=(10, 0))
        
        # 数据有效性指示
        self.validity_label = tk.Label(
            time_frame,
            text="● 等待数据",
            font=("Arial", 9, "bold"),
            fg="#6c757d",
            bg="#f8f9fa"
        )
        self.validity_label.pack(side=tk.RIGHT)
    
    def pack(self, **kwargs):
        """打包组件"""
        self.main_frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局组件"""
        self.main_frame.grid(**kwargs)
    
    def update_data(self, voltage_mv: float, r_ohm: float, r_sei: float, r_ct: float, test_count: int = None):
        """更新显示数据"""
        try:
            # 更新电压显示
            voltage_v = voltage_mv / 1000.0
            self.voltage_card.update_value(f"{voltage_v:.3f}")
            self.voltage_mv_card.update_value(f"{voltage_mv:.0f}")
            
            # 更新电阻显示
            self.r_ohm_card.update_value(f"{r_ohm:.3f}")
            self.r_sei_card.update_value(f"{r_sei:.3f}")
            self.r_ct_card.update_value(f"{r_ct:.3f}")
            
            # 更新测试次数
            if test_count is not None:
                self.test_count_card.update_value(str(test_count))
            
            # 更新时间戳
            current_time = datetime.now()
            time_str = current_time.strftime("%H:%M:%S")
            self.timestamp_label.config(text=time_str)
            
            # 计算更新频率
            self.update_count += 1
            if self.last_update_time:
                time_diff = (current_time - self.last_update_time).total_seconds()
                if time_diff > 0:
                    rate = 60.0 / time_diff  # 每分钟更新次数
                    self.update_rate_card.update_value(f"{rate:.1f}")
            
            self.last_update_time = current_time
            
            # 更新数据有效性指示
            self.validity_label.config(text="● 数据有效", fg="#28a745")
            
            # 3秒后将有效性指示变为等待状态
            self.main_frame.after(3000, self.set_waiting_status)
            
        except Exception as e:
            print(f"更新实时显示数据时出错: {e}")
    
    def set_waiting_status(self):
        """设置等待状态"""
        self.validity_label.config(text="● 等待数据", fg="#6c757d")
    
    def clear_data(self):
        """清空显示数据"""
        self.voltage_card.update_value("--", animate=False)
        self.voltage_mv_card.update_value("--", animate=False)
        self.r_ohm_card.update_value("--", animate=False)
        self.r_sei_card.update_value("--", animate=False)
        self.r_ct_card.update_value("--", animate=False)
        self.test_count_card.update_value("0", animate=False)
        self.update_rate_card.update_value("--", animate=False)
        
        self.timestamp_label.config(text="--")
        self.validity_label.config(text="● 数据已清空", fg="#dc3545")
        
        self.update_count = 0
        self.last_update_time = None
