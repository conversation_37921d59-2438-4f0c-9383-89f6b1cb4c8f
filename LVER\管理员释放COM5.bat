@echo off
echo Requesting Administrator privileges...
echo.

cd /d "D:\code\haha\LVER"

REM 以管理员身份运行COM5释放工具
powershell -Command "Start-Process 'D:\code\haha\.venv\Scripts\python.exe' -ArgumentList '释放COM5端口.py' -Verb RunAs -Wait"

echo.
echo COM5 port release tool completed.
echo Now starting the main application with admin privileges...
echo.

REM 以管理员身份启动主应用程序
powershell -Command "Start-Process 'D:\code\haha\.venv\Scripts\python.exe' -ArgumentList 'main_fixed_complete.py' -Verb RunAs"

echo.
echo Application startup commands executed.
echo Please check the new windows.
pause
