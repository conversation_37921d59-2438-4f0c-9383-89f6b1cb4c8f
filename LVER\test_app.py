#!/usr/bin/env python3
"""
LVER应用程序测试脚本
用于测试各个模块的功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        from serial_communication import SerialCommunication
        print("✓ 串口通信模块导入成功")
    except ImportError as e:
        print(f"✗ 串口通信模块导入失败: {e}")
        return False
    
    try:
        from device_protocol import DeviceProtocol
        print("✓ 设备协议模块导入成功")
    except ImportError as e:
        print(f"✗ 设备协议模块导入失败: {e}")
        return False
    
    try:
        from error_handler import ErrorHandler, ResponseValidator
        print("✓ 错误处理模块导入成功")
    except ImportError as e:
        print(f"✗ 错误处理模块导入失败: {e}")
        return False
    
    try:
        from gui import LVERApp
        print("✓ GUI模块导入成功")
    except ImportError as e:
        print(f"✗ GUI模块导入失败: {e}")
        return False
    
    return True


def test_serial_communication():
    """测试串口通信功能"""
    print("\n测试串口通信功能...")
    
    try:
        from serial_communication import SerialCommunication
        
        # 创建串口通信实例
        serial_comm = SerialCommunication()
        print("✓ 串口通信实例创建成功")
        
        # 测试获取可用端口
        ports = serial_comm.get_available_ports()
        print(f"✓ 发现 {len(ports)} 个可用串口: {ports}")
        
        return True
        
    except Exception as e:
        print(f"✗ 串口通信测试失败: {e}")
        return False


def test_device_protocol():
    """测试设备协议功能"""
    print("\n测试设备协议功能...")
    
    try:
        from device_protocol import DeviceProtocol
        
        # 测试解析测试响应
        test_responses = [
            "1,V,3.456",
            "1,R_ohm,123.45",
            "1,R_sei,67.89",
            "1,R_ct,234.56"
        ]
        
        result = DeviceProtocol.parse_test_response(test_responses)
        if result:
            print("✓ 测试响应解析成功")
            formatted = DeviceProtocol.format_test_results(result)
            print(f"  格式化结果:\n{formatted}")
        else:
            print("✗ 测试响应解析失败")
            return False
        
        # 测试解析电压响应
        voltage_response = "1,B2_Voltage,4.123"
        voltage = DeviceProtocol.parse_voltage_response(voltage_response)
        if voltage is not None:
            print("✓ 电压响应解析成功")
            formatted = DeviceProtocol.format_voltage_result(voltage)
            print(f"  格式化结果: {formatted}")
        else:
            print("✗ 电压响应解析失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 设备协议测试失败: {e}")
        return False


def test_error_handler():
    """测试错误处理功能"""
    print("\n测试错误处理功能...")
    
    try:
        from error_handler import ErrorHandler, ErrorType, ResponseValidator
        
        # 创建错误处理器
        error_handler = ErrorHandler()
        print("✓ 错误处理器创建成功")
        
        # 测试响应验证器
        validator = ResponseValidator(error_handler)
        
        # 测试有效的测试响应
        valid_responses = [
            "1,V,3.456",
            "1,R_ohm,123.45",
            "1,R_sei,67.89",
            "1,R_ct,234.56"
        ]
        
        if validator.validate_test_response(valid_responses):
            print("✓ 有效测试响应验证成功")
        else:
            print("✗ 有效测试响应验证失败")
            return False
        
        # 测试有效的电压响应
        valid_voltage = "1,B2_Voltage,4.123"
        if validator.validate_voltage_response(valid_voltage):
            print("✓ 有效电压响应验证成功")
        else:
            print("✗ 有效电压响应验证失败")
            return False
        
        # 测试无效响应
        invalid_responses = ["invalid", "data"]
        if not validator.validate_test_response(invalid_responses):
            print("✓ 无效响应正确识别")
        else:
            print("✗ 无效响应识别失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 错误处理测试失败: {e}")
        return False


def test_dependencies():
    """测试依赖项"""
    print("\n测试依赖项...")
    
    try:
        import serial
        print("✓ pyserial 可用")
    except ImportError:
        print("✗ pyserial 不可用，请运行: pip install pyserial")
        return False
    
    try:
        import tkinter
        print("✓ tkinter 可用")
    except ImportError:
        print("✗ tkinter 不可用")
        return False
    
    return True


def main():
    """主测试函数"""
    print("LVER 应用程序测试")
    print("=" * 50)
    
    tests = [
        ("依赖项", test_dependencies),
        ("模块导入", test_imports),
        ("串口通信", test_serial_communication),
        ("设备协议", test_device_protocol),
        ("错误处理", test_error_handler),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{passed + 1}/{total}] {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✓ {test_name} 测试通过")
        else:
            print(f"✗ {test_name} 测试失败")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用程序可以正常运行。")
        return True
    else:
        print("❌ 部分测试失败，请检查错误信息。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
