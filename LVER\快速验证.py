#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证pyserial是否可用
"""

print("🔋 快速验证 - pyserial状态")
print("=" * 40)

# 模拟main_fixed_complete.py的导入逻辑
try:
    import serial
    import serial.tools.list_ports
    SERIAL_AVAILABLE = True
    print("✅ pyserial 可用 - 将使用真实串口通信")
    print(f"   版本: {serial.VERSION}")
except ImportError:
    print("❌ 关键错误: pyserial库未安装!")
    print("💡 请安装pyserial库以获取真实设备数据")
    SERIAL_AVAILABLE = False

if SERIAL_AVAILABLE:
    print("\n📋 扫描串口...")
    try:
        ports = list(serial.tools.list_ports.comports())
        print(f"发现 {len(ports)} 个串口:")
        for port in ports:
            print(f"  📍 {port.device}: {port.description}")
            if port.device == 'COM5':
                print(f"    ✅ 目标设备COM5已发现!")
    except Exception as e:
        print(f"❌ 串口扫描失败: {e}")

print(f"\n🎯 结果: {'真实数据模式' if SERIAL_AVAILABLE else '模拟数据模式'}")

if SERIAL_AVAILABLE:
    print("🎉 现在运行主程序将获取真实设备数据!")
    print("💡 命令: D:\\code\\haha\\.venv\\Scripts\\python.exe main_fixed_complete.py")
else:
    print("⚠️  运行主程序仍将显示模拟数据")

input("\n按回车键退出...")
