#!/usr/bin/env python3
"""
LVER 演示模式
不需要真实串口设备，用于演示应用程序功能
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
from datetime import datetime
import time
import random

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from device_protocol import DeviceProtocol
from error_handler import ErrorHandler, ErrorType


class MockSerialCommunication:
    """模拟串口通信类"""
    
    def __init__(self):
        self.is_connected = False
        self.timeout = 2.0
        self.baudrate = 9600
        self.error_handler = ErrorHandler()
        self.protocol = DeviceProtocol()
    
    def get_available_ports(self):
        """返回模拟的串口列表"""
        return ["COM1", "COM3", "COM5", "/dev/ttyUSB0", "/dev/ttyACM0"]
    
    def connect(self, port, baudrate=9600):
        """模拟连接"""
        time.sleep(0.5)  # 模拟连接延迟
        self.is_connected = True
        self.baudrate = baudrate
        self.error_handler.log_info(f"模拟连接到 {port}")
        return True
    
    def disconnect(self):
        """模拟断开连接"""
        self.is_connected = False
        self.error_handler.log_info("模拟断开连接")
    
    def send_test_command(self):
        """模拟发送测试命令"""
        if not self.is_connected:
            return None
        
        time.sleep(0.3)  # 模拟通信延迟
        
        # 生成模拟数据
        voltage = round(random.uniform(3.0, 4.2), 3)
        r_ohm = round(random.uniform(100, 200), 3)
        r_sei = round(random.uniform(50, 100), 3)
        r_ct = round(random.uniform(200, 300), 3)
        
        responses = [
            f"1,V,{voltage}",
            f"1,R_ohm,{r_ohm}",
            f"1,R_sei,{r_sei}",
            f"1,R_ct,{r_ct}"
        ]
        
        self.error_handler.log_debug(f"模拟测试响应: {responses}")
        return responses
    
    def send_reset_command(self):
        """模拟发送重置命令"""
        if not self.is_connected:
            return False
        
        time.sleep(0.1)  # 模拟通信延迟
        self.error_handler.log_info("模拟重置命令发送成功")
        return True
    
    def send_voltage_command(self):
        """模拟发送电压测量命令"""
        if not self.is_connected:
            return None
        
        time.sleep(0.2)  # 模拟通信延迟
        
        # 生成模拟电压数据
        voltage = round(random.uniform(3.5, 4.1), 3)
        response = f"1,B2_Voltage,{voltage}"
        
        self.error_handler.log_debug(f"模拟电压响应: {response}")
        return response


class LVERDemoApp:
    """LVER演示应用程序"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("LVER 串口通信工具 (演示模式)")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 使用模拟串口通信
        self.serial_comm = MockSerialCommunication()
        self.protocol = DeviceProtocol()
        
        # 创建界面
        self.create_widgets()
        self.update_port_list()
        self.update_status()
        
        # 显示演示模式提示
        self.log_message("=== 演示模式 ===", "command")
        self.log_message("这是演示模式，使用模拟数据。", "command")
        self.log_message("在真实环境中，请使用 main.py 并连接真实设备。", "command")
        self.log_message("", "data")
        
        # 定期更新状态
        self.root.after(1000, self.periodic_update)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 串口配置区域
        self.create_serial_config_section(main_frame)
        
        # 命令按钮区域
        self.create_command_section(main_frame)
        
        # 状态显示区域
        self.create_status_section(main_frame)
        
        # 响应显示区域
        self.create_response_section(main_frame)
    
    def create_serial_config_section(self, parent):
        """创建串口配置区域"""
        # 串口配置框架
        config_frame = ttk.LabelFrame(parent, text="串口配置 (演示模式)", padding="5")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # 串口选择
        ttk.Label(config_frame, text="串口:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.port_var = tk.StringVar()
        self.port_combo = ttk.Combobox(config_frame, textvariable=self.port_var, state="readonly")
        self.port_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # 刷新按钮
        self.refresh_btn = ttk.Button(config_frame, text="刷新", command=self.update_port_list)
        self.refresh_btn.grid(row=0, column=2, padx=(0, 10))
        
        # 波特率选择
        ttk.Label(config_frame, text="波特率:").grid(row=0, column=3, sticky=tk.W, padx=(0, 5))
        self.baudrate_var = tk.StringVar(value="9600")
        baudrate_combo = ttk.Combobox(config_frame, textvariable=self.baudrate_var, 
                                     values=["9600", "19200", "38400", "57600", "115200"], 
                                     state="readonly", width=10)
        baudrate_combo.grid(row=0, column=4, padx=(0, 10))
        
        # 连接按钮
        self.connect_btn = ttk.Button(config_frame, text="连接", command=self.toggle_connection)
        self.connect_btn.grid(row=0, column=5)
    
    def create_command_section(self, parent):
        """创建命令按钮区域"""
        # 命令按钮框架
        cmd_frame = ttk.LabelFrame(parent, text="设备命令", padding="5")
        cmd_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 测试命令按钮
        self.test_btn = ttk.Button(cmd_frame, text="测试命令 (0xAA)", 
                                  command=self.send_test_command, state="disabled")
        self.test_btn.grid(row=0, column=0, padx=(0, 10), pady=5)
        
        # 重置命令按钮
        self.reset_btn = ttk.Button(cmd_frame, text="重置命令 (0xA0)", 
                                   command=self.send_reset_command, state="disabled")
        self.reset_btn.grid(row=0, column=1, padx=(0, 10), pady=5)
        
        # 电压测量按钮
        self.voltage_btn = ttk.Button(cmd_frame, text="电压测量 (0x55)", 
                                     command=self.send_voltage_command, state="disabled")
        self.voltage_btn.grid(row=0, column=2, padx=(0, 10), pady=5)
        
        # 清空显示按钮
        self.clear_btn = ttk.Button(cmd_frame, text="清空显示", command=self.clear_response)
        self.clear_btn.grid(row=0, column=3, pady=5)
    
    def create_status_section(self, parent):
        """创建状态显示区域"""
        # 状态框架
        status_frame = ttk.LabelFrame(parent, text="连接状态", padding="5")
        status_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        # 状态指示器
        ttk.Label(status_frame, text="状态:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.status_label = ttk.Label(status_frame, text="未连接", foreground="red")
        self.status_label.grid(row=0, column=1, sticky=tk.W)
        
        # 最后操作时间
        ttk.Label(status_frame, text="最后操作:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        self.last_action_label = ttk.Label(status_frame, text="无")
        self.last_action_label.grid(row=1, column=1, sticky=tk.W)
    
    def create_response_section(self, parent):
        """创建响应显示区域"""
        # 响应显示框架
        response_frame = ttk.LabelFrame(parent, text="设备响应", padding="5")
        response_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        response_frame.columnconfigure(0, weight=1)
        response_frame.rowconfigure(0, weight=1)
        
        # 响应文本区域
        self.response_text = scrolledtext.ScrolledText(response_frame, height=15, width=80)
        self.response_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置文本标签
        self.response_text.tag_configure("timestamp", foreground="gray")
        self.response_text.tag_configure("command", foreground="blue", font=("TkDefaultFont", 9, "bold"))
        self.response_text.tag_configure("success", foreground="green")
        self.response_text.tag_configure("error", foreground="red")
        self.response_text.tag_configure("data", foreground="black")
    
    def update_port_list(self):
        """更新串口列表"""
        ports = self.serial_comm.get_available_ports()
        self.port_combo['values'] = ports
        if ports and not self.port_var.get():
            self.port_var.set(ports[0])
    
    def toggle_connection(self):
        """切换连接状态"""
        if self.serial_comm.is_connected:
            self.disconnect()
        else:
            self.connect()
    
    def connect(self):
        """连接串口"""
        port = self.port_var.get()
        if not port:
            messagebox.showerror("错误", "请选择串口")
            return
        
        try:
            baudrate = int(self.baudrate_var.get())
            if self.serial_comm.connect(port, baudrate):
                self.log_message(f"成功连接到 {port} (波特率: {baudrate}) [模拟]", "success")
                self.update_status()
            else:
                messagebox.showerror("连接失败", f"无法连接到串口 {port}")
        except ValueError:
            messagebox.showerror("错误", "无效的波特率")
    
    def disconnect(self):
        """断开连接"""
        self.serial_comm.disconnect()
        self.log_message("已断开连接 [模拟]", "command")
        self.update_status()
    
    def update_status(self):
        """更新状态显示"""
        if self.serial_comm.is_connected:
            self.status_label.config(text="已连接 (演示模式)", foreground="green")
            self.connect_btn.config(text="断开")
            
            # 启用命令按钮
            self.test_btn.config(state="normal")
            self.reset_btn.config(state="normal")
            self.voltage_btn.config(state="normal")
        else:
            self.status_label.config(text="未连接", foreground="red")
            self.connect_btn.config(text="连接")
            
            # 禁用命令按钮
            self.test_btn.config(state="disabled")
            self.reset_btn.config(state="disabled")
            self.voltage_btn.config(state="disabled")
    
    def send_test_command(self):
        """发送测试命令"""
        def execute():
            self.log_message("发送测试命令 (0xAA)... [模拟]", "command")
            self.update_last_action("发送测试命令")
            
            responses = self.serial_comm.send_test_command()
            if responses:
                # 解析响应
                data = self.protocol.parse_test_response(responses)
                if data:
                    formatted_result = self.protocol.format_test_results(data)
                    self.log_message("测试命令响应:", "success")
                    self.log_message(formatted_result, "data")
                else:
                    self.log_message("响应格式错误", "error")
                    self.log_message(f"原始响应: {responses}", "data")
            else:
                self.log_message("测试命令超时或失败", "error")
        
        threading.Thread(target=execute, daemon=True).start()
    
    def send_reset_command(self):
        """发送重置命令"""
        def execute():
            self.log_message("发送重置命令 (0xA0)... [模拟]", "command")
            self.update_last_action("发送重置命令")
            
            if self.serial_comm.send_reset_command():
                self.log_message("重置命令发送成功", "success")
            else:
                self.log_message("重置命令发送失败", "error")
        
        threading.Thread(target=execute, daemon=True).start()
    
    def send_voltage_command(self):
        """发送电压测量命令"""
        def execute():
            self.log_message("发送电压测量命令 (0x55)... [模拟]", "command")
            self.update_last_action("发送电压测量命令")
            
            response = self.serial_comm.send_voltage_command()
            if response:
                voltage = self.protocol.parse_voltage_response(response)
                if voltage is not None:
                    formatted_result = self.protocol.format_voltage_result(voltage)
                    self.log_message("电压测量响应:", "success")
                    self.log_message(formatted_result, "data")
                else:
                    self.log_message("电压响应格式错误", "error")
                    self.log_message(f"原始响应: {response}", "data")
            else:
                self.log_message("电压测量超时或失败", "error")
        
        threading.Thread(target=execute, daemon=True).start()
    
    def log_message(self, message, tag="data"):
        """记录消息到响应区域"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 在主线程中更新UI
        def update_ui():
            self.response_text.insert(tk.END, f"[{timestamp}] ", "timestamp")
            self.response_text.insert(tk.END, f"{message}\n", tag)
            self.response_text.see(tk.END)
        
        if threading.current_thread() == threading.main_thread():
            update_ui()
        else:
            self.root.after(0, update_ui)
    
    def update_last_action(self, action):
        """更新最后操作时间"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        def update_ui():
            self.last_action_label.config(text=f"{action} ({timestamp})")
        
        if threading.current_thread() == threading.main_thread():
            update_ui()
        else:
            self.root.after(0, update_ui)
    
    def clear_response(self):
        """清空响应显示"""
        self.response_text.delete(1.0, tk.END)
        self.log_message("显示已清空", "command")
    
    def periodic_update(self):
        """定期更新"""
        self.root.after(1000, self.periodic_update)
    
    def on_closing(self):
        """程序关闭时的清理工作"""
        if self.serial_comm.is_connected:
            self.serial_comm.disconnect()
        self.root.destroy()


def main():
    """主函数"""
    root = tk.Tk()
    app = LVERDemoApp(root)
    
    # 设置关闭事件处理
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    
    # 启动主循环
    root.mainloop()


if __name__ == "__main__":
    main()
