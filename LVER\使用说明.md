# LVER 串口通信工具 - 使用说明

## 版本信息
- **版本**: 增强版 v2.1 (Excel单表导出功能)
- **构建时间**: 2025-06-30
- **文件大小**: 约 30.5 MB
- **系统要求**: Windows 7/8/10/11 (64位)

## 🎯 功能特性

### ✅ 核心功能
- **串口通信**: 支持COM1-COM20端口自动检测
- **多参数测量**: 电压(V)、SEI电阻(R_sei)、CT电阻(R_ct)、欧姆电阻(R_ohm)
- **实时数据显示**: 专业数据卡片布局，大字体显示
- **图表可视化**: 4色多参数实时曲线图
- **连续测试**: 自动循环测试功能

### 📊 增强导出功能
1. **📄 统一导出 (单文件)**
   - 所有数据导出到一个CSV文件
   - 保持原有的导出格式
   - 适合简单的数据查看

2. **📁 分类导出 (多文件)**
   - 每种参数类型独立的CSV文件
   - 包含详细的统计信息
   - 生成数据汇总文件
   - 便于分类分析和处理

3. **📊 Excel表格导出 (单表格式)** 🆕
   - 单工作表包含所有数据
   - 按时间组织，每行包含同一时间点的所有参数
   - 缺失数据显示为"N/A"
   - 专业表格格式，便于直观查看

4. **📊 Excel格式导出 (多工作表)**
   - 多工作表Excel文件
   - 每种参数类型独立工作表
   - 专业的格式和样式
   - 包含汇总工作表

## 🚀 快速开始

### 1. 启动应用程序
- 双击 `LVER_串口通信工具.exe` 启动程序
- 无需安装Python环境或其他依赖

### 2. 连接设备
1. 在"串口选择"下拉菜单中选择正确的COM端口
2. 点击"连接"按钮建立串口连接
3. 连接成功后状态显示为"已连接"

### 3. 执行测试
- **测试命令 (0xAA)**: 返回4种参数的完整测量数据
- **电压测量 (0x55)**: 单独测量电压值
- **复位命令 (0xA0)**: 重置设备状态
- **连续测试**: 启用后自动循环执行测试命令

### 4. 查看数据
- **实时显示区域**: 6个专业数据卡片显示最新测量值
- **图表区域**: 4色实时曲线图显示数据趋势
- **颜色编码**: 蓝色(电压)、绿色(SEI电阻)、紫色(CT电阻)、红色(欧姆电阻)

### 5. 导出数据
1. 点击"导出数据 ▼"按钮
2. 选择导出格式:
   - 统一导出 (单文件)
   - 分类导出 (多文件)
   - Excel表格导出 (单表格式) 🆕
   - Excel格式导出 (多工作表)
3. 选择保存位置并确认

## 📋 支持的设备命令

| 命令 | 十六进制 | 功能描述 | 返回数据 |
|------|----------|----------|----------|
| 测试命令 | 0xAA | 完整多参数测量 | 4行CSV数据 |
| 电压测量 | 0x55 | 单独电压测量 | 1行CSV数据 |
| 复位命令 | 0xA0 | 设备复位 | 无返回 |

## 📁 导出文件格式

### 统一导出格式
```csv
时间,参数类型,测量值,单位
2025-06-30 10:15:30,V,3250,mV
2025-06-30 10:15:31,R_sei,275,μΩ
2025-06-30 10:15:32,R_ct,165,μΩ
2025-06-30 10:15:33,R_ohm,520,μΩ
```

### 分类导出格式
- **LVER_电压测量_20250630_101530.csv**: 电压数据
- **LVER_SEI电阻_20250630_101530.csv**: SEI电阻数据
- **LVER_CT电阻_20250630_101530.csv**: CT电阻数据
- **LVER_欧姆电阻_20250630_101530.csv**: 欧姆电阻数据
- **LVER_数据汇总_20250630_101530.csv**: 汇总信息

### Excel单表导出格式 🆕
```
时间      | V电压(mV) | R_sei电阻(μΩ) | R_ct电阻(μΩ) | R_ohm欧姆电阻(μΩ)
----------|-----------|---------------|--------------|-------------------
10:15:30  | 3250      | 275           | 165          | 520
10:15:32  | 3240      | N/A           | N/A          | N/A
10:15:34  | N/A       | 280           | 170          | 525
```
- **文件名**: LVER_综合数据表_时间戳.xlsx
- **特点**: 单工作表，按时间组织，缺失值显示N/A

### Excel多工作表导出格式
- **数据汇总**: 总览工作表
- **电压测量**: 电压数据工作表
- **SEI电阻**: SEI电阻数据工作表
- **CT电阻**: CT电阻数据工作表
- **欧姆电阻**: 欧姆电阻数据工作表

## 🔧 故障排除

### 常见问题

**1. 程序无法启动**
- 确认Windows版本兼容性 (需要64位系统)
- 检查防病毒软件是否阻止程序运行
- 尝试以管理员身份运行

**2. 串口连接失败**
- 确认设备已正确连接到计算机
- 检查设备驱动程序是否已安装
- 确认串口号是否正确 (通常为COM3、COM5等)
- 检查串口是否被其他程序占用

**3. 无法检测到串口**
- 重新插拔USB设备
- 在设备管理器中检查串口设备
- 重启应用程序

**4. 数据显示异常**
- 检查设备连接状态
- 确认设备协议是否匹配
- 重新执行测试命令

**5. 导出功能失败**
- 确认有足够的磁盘空间
- 检查目标文件夹的写入权限
- 确保文件名不包含特殊字符

### 技术规格

- **串口参数**: 9600波特率, 8数据位, 无校验, 1停止位
- **超时设置**: 1秒读取超时
- **数据格式**: UTF-8编码，支持中文
- **图表更新**: 实时更新，最多显示100个数据点
- **内存使用**: 约50-100MB运行内存

## 📞 技术支持

如遇到问题或需要技术支持，请提供以下信息：
1. Windows系统版本
2. 设备型号和连接方式
3. 错误信息截图
4. 操作步骤描述

## 📝 更新日志

### v2.1 增强版 (2025-06-30)
- ✅ 新增Excel单表导出功能
- ✅ 优化导出菜单界面
- ✅ 增强数据组织和对齐功能
- ✅ 改进缺失数据处理
- ✅ 完善统计分析功能

### v2.0 增强版 (2025-06-30)
- ✅ 新增分类导出功能
- ✅ 新增Excel格式导出
- ✅ 优化数据显示界面
- ✅ 增强错误处理机制
- ✅ 打包为独立可执行文件

### v1.0 基础版
- ✅ 基础串口通信功能
- ✅ 多参数数据显示
- ✅ 实时图表功能
- ✅ CSV数据导出

---

**LVER 串口通信工具 - 增强版**  
构建时间: 2025-06-30  
文件大小: 30.5 MB  
支持系统: Windows 64位
