"""
错误处理模块
提供统一的错误处理和日志记录功能
"""

import logging
import traceback
from datetime import datetime
from typing import Optional, Callable
from enum import Enum


class ErrorType(Enum):
    """错误类型枚举"""
    SERIAL_CONNECTION = "串口连接错误"
    SERIAL_COMMUNICATION = "串口通信错误"
    PROTOCOL_PARSE = "协议解析错误"
    TIMEOUT = "通信超时"
    INVALID_RESPONSE = "无效响应"
    HARDWARE = "硬件错误"
    SOFTWARE = "软件错误"


class ErrorHandler:
    """错误处理器类"""
    
    def __init__(self, log_file: Optional[str] = None):
        self.log_file = log_file
        self.error_callback: Optional[Callable] = None
        self.setup_logging()
    
    def setup_logging(self):
        """设置日志记录"""
        # 创建日志记录器
        self.logger = logging.getLogger('LVER')
        self.logger.setLevel(logging.DEBUG)
        
        # 清除现有的处理器
        self.logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器（如果指定了日志文件）
        if self.log_file:
            try:
                file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
                file_handler.setLevel(logging.DEBUG)
                file_handler.setFormatter(formatter)
                self.logger.addHandler(file_handler)
            except Exception as e:
                print(f"无法创建日志文件: {e}")
    
    def set_error_callback(self, callback: Callable):
        """设置错误回调函数"""
        self.error_callback = callback
    
    def handle_error(self, error_type: ErrorType, message: str, 
                    exception: Optional[Exception] = None, 
                    show_user: bool = True) -> str:
        """
        处理错误
        
        Args:
            error_type: 错误类型
            message: 错误消息
            exception: 异常对象（可选）
            show_user: 是否显示给用户
            
        Returns:
            str: 格式化的错误消息
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 构建错误消息
        error_msg = f"[{error_type.value}] {message}"
        
        if exception:
            error_msg += f" - {str(exception)}"
            # 记录详细的异常信息到日志
            self.logger.error(f"{error_msg}\n{traceback.format_exc()}")
        else:
            self.logger.error(error_msg)
        
        # 如果设置了错误回调，调用它
        if self.error_callback and show_user:
            try:
                self.error_callback(error_type, error_msg)
            except Exception as e:
                self.logger.error(f"错误回调函数执行失败: {e}")
        
        return error_msg
    
    def handle_serial_error(self, operation: str, exception: Exception) -> str:
        """处理串口相关错误"""
        if "timeout" in str(exception).lower():
            return self.handle_error(
                ErrorType.TIMEOUT,
                f"串口操作超时: {operation}",
                exception
            )
        elif "permission" in str(exception).lower() or "access" in str(exception).lower():
            return self.handle_error(
                ErrorType.SERIAL_CONNECTION,
                f"串口访问权限不足: {operation}",
                exception
            )
        elif "not found" in str(exception).lower() or "does not exist" in str(exception).lower():
            return self.handle_error(
                ErrorType.SERIAL_CONNECTION,
                f"串口设备不存在: {operation}",
                exception
            )
        else:
            return self.handle_error(
                ErrorType.SERIAL_COMMUNICATION,
                f"串口通信错误: {operation}",
                exception
            )
    
    def handle_protocol_error(self, operation: str, data: str, exception: Optional[Exception] = None) -> str:
        """处理协议解析错误"""
        message = f"协议解析失败: {operation}, 数据: {data[:100]}..."
        return self.handle_error(
            ErrorType.PROTOCOL_PARSE,
            message,
            exception
        )
    
    def handle_timeout_error(self, operation: str, timeout_duration: float) -> str:
        """处理超时错误"""
        message = f"操作超时: {operation} (超时时间: {timeout_duration}秒)"
        return self.handle_error(ErrorType.TIMEOUT, message)
    
    def handle_invalid_response(self, expected: str, received: str) -> str:
        """处理无效响应错误"""
        message = f"响应格式无效 - 期望: {expected}, 收到: {received[:100]}..."
        return self.handle_error(ErrorType.INVALID_RESPONSE, message)
    
    def log_info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)
    
    def log_debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)
    
    def log_warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)


class ResponseValidator:
    """响应验证器类"""
    
    def __init__(self, error_handler: ErrorHandler):
        self.error_handler = error_handler
    
    def validate_test_response(self, responses: list) -> bool:
        """验证测试命令响应"""
        if not responses:
            self.error_handler.handle_invalid_response("4行CSV数据", "空响应")
            return False
        
        if len(responses) != 4:
            self.error_handler.handle_invalid_response(
                "4行CSV数据", 
                f"{len(responses)}行数据"
            )
            return False
        
        expected_types = ['V', 'R_ohm', 'R_sei', 'R_ct']
        for i, (response, expected_type) in enumerate(zip(responses, expected_types)):
            if not self._validate_csv_format(response, expected_type):
                self.error_handler.handle_invalid_response(
                    f"第{i+1}行: 1,{expected_type},数值",
                    response
                )
                return False
        
        return True
    
    def validate_voltage_response(self, response: str) -> bool:
        """验证电压测量响应"""
        if not response:
            self.error_handler.handle_invalid_response("1,V,数值", "空响应")
            return False

        # 接受 1,V,数值 格式（实际设备返回的格式）
        if not self._validate_csv_format(response, "V"):
            self.error_handler.handle_invalid_response("1,V,数值", response)
            return False

        return True
    
    def _validate_csv_format(self, line: str, expected_type: str) -> bool:
        """验证CSV格式"""
        try:
            parts = line.strip().split(',')
            if len(parts) != 3:
                return False
            
            if parts[0].strip() != "1":
                return False
            
            if parts[1].strip() != expected_type:
                return False
            
            # 验证数值部分
            float(parts[2].strip())
            return True
            
        except (ValueError, IndexError):
            return False


# 全局错误处理器实例
global_error_handler = ErrorHandler("lver_error.log")
