#!/usr/bin/env python3
"""
串口检测测试工具
用于诊断串口检测问题
"""

import sys
import os

def test_serial_import():
    """测试serial模块导入"""
    print("="*50)
    print("测试 pyserial 模块导入")
    print("="*50)
    
    try:
        import serial
        print("✓ serial 模块导入成功")
        print(f"  版本: {serial.__version__ if hasattr(serial, '__version__') else '未知'}")
        
        try:
            import serial.tools.list_ports
            print("✓ serial.tools.list_ports 导入成功")
        except ImportError as e:
            print(f"✗ serial.tools.list_ports 导入失败: {e}")
            return False
            
    except ImportError as e:
        print(f"✗ serial 模块导入失败: {e}")
        return False
    
    return True

def test_port_detection():
    """测试串口检测"""
    print("\n" + "="*50)
    print("测试串口检测功能")
    print("="*50)
    
    try:
        import serial.tools.list_ports
        
        # 获取所有串口
        ports = list(serial.tools.list_ports.comports())
        print(f"发现 {len(ports)} 个串口设备:")
        
        if not ports:
            print("  (无串口设备)")
        else:
            for i, port in enumerate(ports, 1):
                print(f"  {i}. {port.device}")
                print(f"     描述: {port.description}")
                print(f"     硬件ID: {port.hwid}")
                print(f"     制造商: {getattr(port, 'manufacturer', '未知')}")
                print(f"     产品: {getattr(port, 'product', '未知')}")
                print(f"     序列号: {getattr(port, 'serial_number', '未知')}")
                print()
        
        return ports
        
    except Exception as e:
        print(f"✗ 串口检测失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_specific_port(port_name):
    """测试特定串口"""
    print(f"\n" + "="*50)
    print(f"测试特定串口: {port_name}")
    print("="*50)
    
    try:
        import serial
        
        # 尝试打开串口
        ser = serial.Serial()
        ser.port = port_name
        ser.baudrate = 115200
        ser.timeout = 1
        
        print(f"尝试连接 {port_name}...")
        ser.open()
        print(f"✓ {port_name} 连接成功")
        
        # 检查串口状态
        print(f"  端口: {ser.port}")
        print(f"  波特率: {ser.baudrate}")
        print(f"  超时: {ser.timeout}")
        print(f"  是否打开: {ser.is_open}")
        
        ser.close()
        print(f"✓ {port_name} 已关闭")
        return True
        
    except Exception as e:
        print(f"✗ {port_name} 连接失败: {e}")
        return False

def test_enhanced_serial():
    """测试增强串口管理器"""
    print("\n" + "="*50)
    print("测试增强串口管理器")
    print("="*50)
    
    try:
        # 添加当前目录到路径
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from enhanced_serial import EnhancedSerialManager
        
        manager = EnhancedSerialManager()
        print("✓ EnhancedSerialManager 创建成功")
        
        # 获取串口列表
        ports = manager.get_available_ports()
        print(f"✓ 通过管理器发现 {len(ports)} 个串口:")
        
        for port, desc in ports:
            print(f"  {port} - {desc}")
        
        return ports
        
    except Exception as e:
        print(f"✗ 增强串口管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def main():
    """主函数"""
    print("串口检测诊断工具")
    print("用于诊断 COM5 检测不到的问题")
    
    # 1. 测试模块导入
    if not test_serial_import():
        print("\n❌ pyserial 模块有问题，请重新安装:")
        print("pip uninstall pyserial")
        print("pip install pyserial")
        return
    
    # 2. 测试串口检测
    detected_ports = test_port_detection()
    
    # 3. 检查是否发现COM5
    com5_found = any(port.device == 'COM5' for port in detected_ports)
    
    if com5_found:
        print("✅ COM5 已检测到!")
        test_specific_port('COM5')
    else:
        print("❌ COM5 未检测到")
        print("\n可能的原因:")
        print("1. 设备未正确连接")
        print("2. 驱动程序未安装")
        print("3. 设备被其他程序占用")
        print("4. 设备管理器中显示错误")
        
        # 测试其他检测到的串口
        if detected_ports:
            print(f"\n尝试测试其他检测到的串口:")
            for port in detected_ports:
                test_specific_port(port.device)
    
    # 4. 测试增强串口管理器
    manager_ports = test_enhanced_serial()
    
    print("\n" + "="*50)
    print("诊断总结")
    print("="*50)
    print(f"系统检测到的串口数量: {len(detected_ports)}")
    print(f"管理器检测到的串口数量: {len(manager_ports)}")
    print(f"COM5 是否检测到: {'是' if com5_found else '否'}")
    
    if not com5_found:
        print("\n建议:")
        print("1. 检查设备管理器中的串口设备")
        print("2. 确认设备驱动正确安装")
        print("3. 尝试重新插拔设备")
        print("4. 检查设备是否被其他软件占用")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
