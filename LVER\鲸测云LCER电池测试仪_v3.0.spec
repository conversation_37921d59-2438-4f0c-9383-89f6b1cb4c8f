# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['main_fixed_complete.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['tkinter', 'matplotlib', 'serial', 'openpyxl', 'matplotlib.backends.backend_tkagg', 'PIL'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='鲸测云LCER电池测试仪_v3.0',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
