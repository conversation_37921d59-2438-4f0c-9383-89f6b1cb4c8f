#!/usr/bin/env python3
"""
检查应用程序运行状态
"""

import psutil
import sys

def check_python_processes():
    """检查Python进程"""
    print("="*50)
    print("检查Python进程")
    print("="*50)
    
    python_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['name'] and 'python' in proc.info['name'].lower():
                cmdline = proc.info['cmdline']
                if cmdline and any('main_final.py' in arg for arg in cmdline):
                    python_processes.append(proc.info)
                    print(f"✓ 找到LVER应用程序进程:")
                    print(f"  PID: {proc.info['pid']}")
                    print(f"  命令: {' '.join(cmdline)}")
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if python_processes:
        print(f"\n✅ 发现 {len(python_processes)} 个LVER应用程序进程正在运行")
        return True
    else:
        print("\n❌ 未发现LVER应用程序进程")
        return False

def check_gui_windows():
    """检查GUI窗口"""
    print("\n" + "="*50)
    print("检查GUI窗口")
    print("="*50)
    
    try:
        import tkinter as tk
        
        # 创建一个临时窗口来测试tkinter
        test_root = tk.Tk()
        test_root.withdraw()  # 隐藏窗口
        
        print("✓ tkinter 可用")
        
        # 检查是否有其他tkinter窗口
        try:
            # 这个方法可能不完全准确，但可以给出一些信息
            print("✓ GUI环境正常")
        except:
            print("⚠️  GUI环境检查失败")
        
        test_root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ tkinter 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("LVER 应用程序状态检查")
    
    # 检查进程
    process_running = check_python_processes()
    
    # 检查GUI
    gui_available = check_gui_windows()
    
    print("\n" + "="*50)
    print("状态总结")
    print("="*50)
    
    if process_running:
        print("✅ LVER应用程序正在运行")
        print("\n应用程序功能:")
        print("• 串口选择下拉菜单中应包含COM5")
        print("• 图表区域应显示'暂无测试数据'")
        print("• 窗口大小: 1400x1000")
        print("• 图表区域占83%屏幕空间")
        
        print("\n使用步骤:")
        print("1. 在串口下拉菜单中选择 'COM5 - 您的设备串口'")
        print("2. 设置波特率为 115200")
        print("3. 点击'连接'按钮")
        print("4. 进行测试获取数据")
        
    else:
        print("❌ LVER应用程序未运行")
        print("\n建议:")
        print("1. 检查是否有错误信息")
        print("2. 尝试重新启动应用程序")
        print("3. 检查依赖项是否正确安装")
    
    if gui_available:
        print("\n✅ GUI环境正常")
    else:
        print("\n❌ GUI环境有问题")

if __name__ == "__main__":
    main()
