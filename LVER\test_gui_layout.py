#!/usr/bin/env python3
"""
测试GUI布局的独立脚本 - 不依赖matplotlib
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    try:
        # 创建主窗口
        root = tk.Tk()
        root.title("LVER 布局测试 - 图表区域优化")
        root.geometry("1200x800")
        
        # 创建主容器 - 优化布局比例，给图表更多空间
        main_container = ttk.PanedWindow(root, orient=tk.VERTICAL)
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 上部分：控制区域 (减少权重)
        top_frame = ttk.Frame(main_container)
        main_container.add(top_frame, weight=1)

        # 下部分：图表区域 (增加权重，让图表占据更多空间)
        bottom_frame = ttk.Frame(main_container)
        main_container.add(bottom_frame, weight=3)

        # 创建上部分的左右分割
        top_paned = ttk.PanedWindow(top_frame, orient=tk.HORIZONTAL)
        top_paned.pack(fill=tk.BOTH, expand=True)

        # 左侧：连接和控制
        left_frame = ttk.Frame(top_paned)
        top_paned.add(left_frame, weight=1)

        # 右侧：实时数据显示
        right_frame = ttk.Frame(top_paned)
        top_paned.add(right_frame, weight=1)
        
        # 填充左侧内容
        conn_frame = ttk.LabelFrame(left_frame, text="串口连接", padding="10")
        conn_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(conn_frame, text="串口:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        port_combo = ttk.Combobox(conn_frame, values=["COM1", "COM2", "COM3"], state="readonly", width=15)
        port_combo.grid(row=0, column=1, padx=(0, 10))
        
        ttk.Label(conn_frame, text="波特率:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        baud_combo = ttk.Combobox(conn_frame, values=["9600", "38400", "115200"], state="readonly", width=10)
        baud_combo.grid(row=0, column=3, padx=(0, 10))
        baud_combo.set("115200")
        
        ttk.Button(conn_frame, text="连接").grid(row=0, column=4, padx=(10, 0))
        
        # 测试控制区域
        test_frame = ttk.LabelFrame(left_frame, text="测试控制", padding="10")
        test_frame.pack(fill=tk.X, pady=(5, 0))
        
        button_frame = ttk.Frame(test_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="测试命令").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="电压测量").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="重置命令").pack(side=tk.LEFT, padx=(0, 5))
        
        # 填充右侧内容
        data_paned = ttk.PanedWindow(right_frame, orient=tk.VERTICAL)
        data_paned.pack(fill=tk.BOTH, expand=True)

        # 实时数据显示
        realtime_frame = ttk.LabelFrame(data_paned, text="实时数据显示", padding="5")
        data_paned.add(realtime_frame, weight=2)
        
        # 创建数据卡片
        cards_frame = ttk.Frame(realtime_frame)
        cards_frame.pack(fill=tk.BOTH, expand=True)
        
        # 电压卡片
        voltage_card = ttk.LabelFrame(cards_frame, text="电压", padding="10")
        voltage_card.grid(row=0, column=0, padx=5, pady=5, sticky="nsew")
        ttk.Label(voltage_card, text="3.295", font=("Arial", 16, "bold"), foreground="blue").pack()
        ttk.Label(voltage_card, text="V", font=("Arial", 10)).pack()
        
        # 电阻卡片
        resistance_card = ttk.LabelFrame(cards_frame, text="电阻", padding="10")
        resistance_card.grid(row=0, column=1, padx=5, pady=5, sticky="nsew")
        ttk.Label(resistance_card, text="514", font=("Arial", 16, "bold"), foreground="green").pack()
        ttk.Label(resistance_card, text="μΩ", font=("Arial", 10)).pack()
        
        # SEI电阻卡片
        sei_card = ttk.LabelFrame(cards_frame, text="SEI电阻", padding="10")
        sei_card.grid(row=0, column=2, padx=5, pady=5, sticky="nsew")
        ttk.Label(sei_card, text="308", font=("Arial", 16, "bold"), foreground="orange").pack()
        ttk.Label(sei_card, text="μΩ", font=("Arial", 10)).pack()
        
        # CT电阻卡片
        ct_card = ttk.LabelFrame(cards_frame, text="CT电阻", padding="10")
        ct_card.grid(row=0, column=3, padx=5, pady=5, sticky="nsew")
        ttk.Label(ct_card, text="104", font=("Arial", 16, "bold"), foreground="red").pack()
        ttk.Label(ct_card, text="μΩ", font=("Arial", 10)).pack()
        
        # 配置网格权重
        for i in range(4):
            cards_frame.columnconfigure(i, weight=1)
        cards_frame.rowconfigure(0, weight=1)

        # 操作日志
        log_frame = ttk.LabelFrame(data_paned, text="操作日志", padding="5")
        data_paned.add(log_frame, weight=1)
        
        log_text = tk.Text(log_frame, height=4, font=("Consolas", 9))
        log_text.pack(fill=tk.BOTH, expand=True)
        log_text.insert(tk.END, "应用程序已启动\n")
        log_text.insert(tk.END, "图表组件已加载 (增强版 - 支持导出功能)\n")
        log_text.insert(tk.END, "布局优化：图表区域权重增加到3，控制区域权重为1\n")
        
        # 图表区域 - 模拟增强版图表
        chart_frame = ttk.LabelFrame(bottom_frame, text="实时数据图表 (增强版)", padding="5")
        chart_frame.pack(fill=tk.BOTH, expand=True)
        
        # 图表控制区域
        control_frame = ttk.Frame(chart_frame)
        control_frame.pack(fill=tk.X, pady=(5, 0))
        
        # 数据显示选择
        ttk.Label(control_frame, text="显示数据:").pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Checkbutton(control_frame, text="电压").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Checkbutton(control_frame, text="电阻").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Checkbutton(control_frame, text="SEI电阻").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Checkbutton(control_frame, text="CT电阻").pack(side=tk.LEFT, padx=(0, 5))
        
        # 导出功能按钮组
        export_frame = ttk.Frame(control_frame)
        export_frame.pack(side=tk.RIGHT, padx=(10, 0))
        
        def export_png():
            messagebox.showinfo("导出PNG", "图表导出为PNG功能\n分辨率: 1920x1080像素\nDPI: 300")
        
        def export_jpg():
            messagebox.showinfo("导出JPG", "图表导出为JPG功能\n分辨率: 1920x1080像素\nDPI: 300")
        
        ttk.Button(export_frame, text="导出PNG", command=export_png).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(export_frame, text="导出JPG", command=export_jpg).pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(export_frame, text="清空数据").pack(side=tk.RIGHT, padx=(5, 0))
        ttk.Button(export_frame, text="刷新图表").pack(side=tk.RIGHT, padx=(5, 0))
        
        # 模拟图表显示区域
        chart_display = ttk.Frame(chart_frame, relief="sunken", borderwidth=2)
        chart_display.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # 图表说明
        chart_info = ttk.Label(
            chart_display,
            text="增强版图表显示区域\n\n"
                 "✓ 图表尺寸: 14x8英寸 (从10x6增大)\n"
                 "✓ DPI: 120 (从100增大)\n"
                 "✓ 布局权重: 3 (图表区域占更多空间)\n"
                 "✓ 支持PNG/JPG导出\n"
                 "✓ 导出分辨率: 至少1920x1080像素\n"
                 "✓ 导出DPI: 300 (高分辨率)\n\n"
                 "在实际应用中，这里将显示matplotlib图表",
            font=("Arial", 12),
            justify=tk.CENTER,
            foreground="darkblue"
        )
        chart_info.pack(expand=True)
        
        print("GUI布局测试应用程序已启动")
        print("布局改进说明：")
        print("1. 图表区域权重从2增加到3，占据更多屏幕空间")
        print("2. 图表尺寸从10x6英寸增大到14x8英寸")
        print("3. DPI从100增大到120，提高显示清晰度")
        print("4. 添加了PNG/JPG导出功能按钮")
        print("5. 导出支持300 DPI高分辨率")
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        print(f"测试应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
