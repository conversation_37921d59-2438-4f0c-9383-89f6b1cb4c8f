#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鲸测云LCER电池测试仪 - 数据准确性诊断工具
专门用于诊断和修复电压值异常波动问题
"""

import serial
import time
import threading
import tkinter as tk
from tkinter import ttk, scrolledtext
import re
from datetime import datetime

class DataAccuracyDiagnostic:
    def __init__(self, root):
        self.root = root
        self.root.title("🔍 数据准确性诊断工具")
        self.root.geometry("1200x800")
        
        self.serial_conn = None
        self.is_monitoring = False
        self.raw_data_log = []
        self.parsed_data_log = []
        
        self.setup_gui()
        
    def setup_gui(self):
        """设置诊断界面"""
        # 控制面板
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(control_frame, text="串口:").pack(side=tk.LEFT)
        self.port_var = tk.StringVar(value="COM5")
        port_combo = ttk.Combobox(control_frame, textvariable=self.port_var, 
                                 values=[f"COM{i}" for i in range(1, 21)], width=10)
        port_combo.pack(side=tk.LEFT, padx=5)
        
        self.connect_btn = ttk.Button(control_frame, text="连接", command=self.toggle_connection)
        self.connect_btn.pack(side=tk.LEFT, padx=5)
        
        self.monitor_btn = ttk.Button(control_frame, text="开始监控", command=self.toggle_monitoring)
        self.monitor_btn.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(control_frame, text="发送测试命令", command=self.send_test_command).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清空日志", command=self.clear_logs).pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        status_frame = ttk.Frame(self.root)
        status_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.status_label = ttk.Label(status_frame, text="状态: 未连接", foreground="red")
        self.status_label.pack(side=tk.LEFT)
        
        self.data_count_label = ttk.Label(status_frame, text="数据包: 0")
        self.data_count_label.pack(side=tk.LEFT, padx=20)
        
        # 创建标签页
        notebook = ttk.Notebook(self.root)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 原始数据标签页
        raw_frame = ttk.Frame(notebook)
        notebook.add(raw_frame, text="原始串口数据")
        
        ttk.Label(raw_frame, text="原始串口接收数据 (未处理):").pack(anchor=tk.W, pady=5)
        self.raw_data_text = scrolledtext.ScrolledText(raw_frame, height=15, font=("Consolas", 10))
        self.raw_data_text.pack(fill=tk.BOTH, expand=True)
        
        # 解析数据标签页
        parsed_frame = ttk.Frame(notebook)
        notebook.add(parsed_frame, text="解析后数据")
        
        ttk.Label(parsed_frame, text="解析后的结构化数据:").pack(anchor=tk.W, pady=5)
        self.parsed_data_text = scrolledtext.ScrolledText(parsed_frame, height=15, font=("Consolas", 10))
        self.parsed_data_text.pack(fill=tk.BOTH, expand=True)
        
        # 数据分析标签页
        analysis_frame = ttk.Frame(notebook)
        notebook.add(analysis_frame, text="数据分析")
        
        ttk.Label(analysis_frame, text="数据统计和异常分析:").pack(anchor=tk.W, pady=5)
        self.analysis_text = scrolledtext.ScrolledText(analysis_frame, height=15, font=("Consolas", 10))
        self.analysis_text.pack(fill=tk.BOTH, expand=True)
        
        # 对比验证标签页
        compare_frame = ttk.Frame(notebook)
        notebook.add(compare_frame, text="对比验证")
        
        ttk.Label(compare_frame, text="与参考数据对比分析:").pack(anchor=tk.W, pady=5)
        self.compare_text = scrolledtext.ScrolledText(compare_frame, height=15, font=("Consolas", 10))
        self.compare_text.pack(fill=tk.BOTH, expand=True)
        
    def toggle_connection(self):
        """切换串口连接状态"""
        if self.serial_conn is None:
            self.connect_serial()
        else:
            self.disconnect_serial()
    
    def connect_serial(self):
        """连接串口"""
        try:
            port = self.port_var.get()
            self.serial_conn = serial.Serial(
                port=port,
                baudrate=115200,
                bytesize=8,
                parity='N',
                stopbits=1,
                timeout=1.0
            )
            
            self.status_label.config(text=f"状态: 已连接到 {port}", foreground="green")
            self.connect_btn.config(text="断开")
            self.log_message("原始数据", f"=== 串口连接成功: {port} ===")
            
        except Exception as e:
            self.log_message("原始数据", f"连接失败: {e}")
            self.status_label.config(text=f"状态: 连接失败", foreground="red")
    
    def disconnect_serial(self):
        """断开串口"""
        if self.serial_conn:
            self.serial_conn.close()
            self.serial_conn = None
        
        self.is_monitoring = False
        self.status_label.config(text="状态: 未连接", foreground="red")
        self.connect_btn.config(text="连接")
        self.monitor_btn.config(text="开始监控")
        self.log_message("原始数据", "=== 串口连接断开 ===")
    
    def toggle_monitoring(self):
        """切换数据监控状态"""
        if not self.serial_conn:
            self.log_message("原始数据", "错误: 请先连接串口")
            return
        
        if self.is_monitoring:
            self.is_monitoring = False
            self.monitor_btn.config(text="开始监控")
            self.log_message("原始数据", "=== 停止数据监控 ===")
        else:
            self.is_monitoring = True
            self.monitor_btn.config(text="停止监控")
            self.log_message("原始数据", "=== 开始数据监控 ===")
            threading.Thread(target=self.monitor_data, daemon=True).start()
    
    def send_test_command(self):
        """发送测试命令"""
        if not self.serial_conn:
            self.log_message("原始数据", "错误: 请先连接串口")
            return
        
        try:
            # 发送0xAA测试命令
            command = bytes([0xAA])
            self.serial_conn.write(command)
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            self.log_message("原始数据", f"[{timestamp}] 发送命令: 0xAA (测试命令)")
            
        except Exception as e:
            self.log_message("原始数据", f"发送命令失败: {e}")
    
    def monitor_data(self):
        """监控串口数据"""
        buffer = ""
        
        while self.is_monitoring and self.serial_conn:
            try:
                if self.serial_conn.in_waiting > 0:
                    # 读取原始数据
                    raw_data = self.serial_conn.read(self.serial_conn.in_waiting)
                    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
                    
                    # 记录原始字节数据
                    hex_data = ' '.join([f'{b:02X}' for b in raw_data])
                    self.log_message("原始数据", f"[{timestamp}] 原始字节: {hex_data}")
                    
                    # 转换为字符串
                    try:
                        text_data = raw_data.decode('utf-8', errors='replace')
                        self.log_message("原始数据", f"[{timestamp}] 文本数据: {repr(text_data)}")
                        
                        # 累积数据到缓冲区
                        buffer += text_data
                        
                        # 按行处理数据
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            line = line.strip()
                            if line:
                                self.process_data_line(timestamp, line)
                                
                    except Exception as e:
                        self.log_message("原始数据", f"[{timestamp}] 解码错误: {e}")
                
                time.sleep(0.01)  # 避免CPU占用过高
                
            except Exception as e:
                self.log_message("原始数据", f"监控错误: {e}")
                break
    
    def process_data_line(self, timestamp, line):
        """处理单行数据"""
        self.log_message("解析数据", f"[{timestamp}] 原始行: {line}")
        
        # 检查是否为调试信息
        if line.startswith('Start Voltage') or line.startswith('HF_current'):
            self.log_message("解析数据", f"[{timestamp}] 调试信息: {line}")
            return
        
        # 检查是否为CSV格式数据
        if ',' in line:
            self.parse_csv_data(timestamp, line)
        else:
            self.log_message("解析数据", f"[{timestamp}] 未知格式: {line}")
    
    def parse_csv_data(self, timestamp, line):
        """解析CSV格式数据"""
        try:
            parts = line.split(',')
            self.log_message("解析数据", f"[{timestamp}] CSV分割: {parts}")
            
            if len(parts) >= 3:
                param_id = parts[0].strip()
                param_type = parts[1].strip()
                param_value_str = parts[2].strip()
                
                # 尝试转换数值
                try:
                    param_value = int(param_value_str)
                    
                    # 记录解析结果
                    parsed_info = {
                        'timestamp': timestamp,
                        'param_id': param_id,
                        'param_type': param_type,
                        'param_value': param_value,
                        'raw_line': line
                    }
                    
                    self.parsed_data_log.append(parsed_info)
                    
                    # 显示解析结果
                    self.log_message("解析数据", 
                        f"[{timestamp}] 解析成功: ID={param_id}, 类型={param_type}, 值={param_value}")
                    
                    # 更新数据计数
                    self.root.after(0, self.update_data_count)
                    
                    # 进行数据分析
                    if param_type == 'V':  # 电压数据
                        self.analyze_voltage_data(parsed_info)
                    
                except ValueError as e:
                    self.log_message("解析数据", f"[{timestamp}] 数值转换失败: {param_value_str}, 错误: {e}")
            else:
                self.log_message("解析数据", f"[{timestamp}] CSV格式不完整: {parts}")
                
        except Exception as e:
            self.log_message("解析数据", f"[{timestamp}] CSV解析错误: {e}")
    
    def analyze_voltage_data(self, data_info):
        """分析电压数据"""
        voltage_data = [d for d in self.parsed_data_log if d['param_type'] == 'V']
        
        if len(voltage_data) >= 2:
            values = [d['param_value'] for d in voltage_data[-10:]]  # 最近10个值
            
            avg_value = sum(values) / len(values)
            min_value = min(values)
            max_value = max(values)
            variation = max_value - min_value
            
            analysis = f"""
=== 电压数据分析 (最近{len(values)}个值) ===
平均值: {avg_value:.1f} mV
最小值: {min_value} mV
最大值: {max_value} mV
波动范围: {variation} mV
当前值: {data_info['param_value']} mV
参考值: 3290 mV (其他软件)
偏差: {data_info['param_value'] - 3290:+d} mV
"""
            
            self.log_message("数据分析", analysis)
            
            # 异常检测
            if variation > 100:  # 波动超过100mV
                self.log_message("数据分析", f"⚠️ 警告: 电压波动过大 ({variation} mV)")
            
            if abs(data_info['param_value'] - 3290) > 50:  # 与参考值偏差超过50mV
                self.log_message("数据分析", f"⚠️ 警告: 与参考值偏差过大 ({data_info['param_value'] - 3290:+d} mV)")
    
    def update_data_count(self):
        """更新数据计数显示"""
        count = len(self.parsed_data_log)
        self.data_count_label.config(text=f"数据包: {count}")
    
    def log_message(self, tab_name, message):
        """记录日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        formatted_message = f"[{timestamp}] {message}\n"
        
        if tab_name == "原始数据":
            self.root.after(0, lambda: self._append_to_text(self.raw_data_text, formatted_message))
        elif tab_name == "解析数据":
            self.root.after(0, lambda: self._append_to_text(self.parsed_data_text, formatted_message))
        elif tab_name == "数据分析":
            self.root.after(0, lambda: self._append_to_text(self.analysis_text, formatted_message))
        elif tab_name == "对比验证":
            self.root.after(0, lambda: self._append_to_text(self.compare_text, formatted_message))
    
    def _append_to_text(self, text_widget, message):
        """安全地向文本控件添加内容"""
        text_widget.insert(tk.END, message)
        text_widget.see(tk.END)
        
        # 限制日志长度
        lines = text_widget.get("1.0", tk.END).split('\n')
        if len(lines) > 1000:
            text_widget.delete("1.0", f"{len(lines)-500}.0")
    
    def clear_logs(self):
        """清空所有日志"""
        self.raw_data_text.delete("1.0", tk.END)
        self.parsed_data_text.delete("1.0", tk.END)
        self.analysis_text.delete("1.0", tk.END)
        self.compare_text.delete("1.0", tk.END)
        
        self.raw_data_log.clear()
        self.parsed_data_log.clear()
        self.update_data_count()
        
        self.log_message("原始数据", "=== 日志已清空 ===")

def main():
    root = tk.Tk()
    app = DataAccuracyDiagnostic(root)
    root.mainloop()

if __name__ == "__main__":
    main()
