#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复COM5权限问题的诊断和解决方案
"""

import serial
import serial.tools.list_ports
import time
import sys
import subprocess

def check_com_ports():
    """检查可用的COM端口"""
    print("🔍 检查系统中的COM端口...")
    print("=" * 50)
    
    ports = serial.tools.list_ports.comports()
    available_ports = []
    
    for port in ports:
        print(f"📍 发现端口: {port.device}")
        print(f"   描述: {port.description}")
        print(f"   硬件ID: {port.hwid}")
        print(f"   制造商: {port.manufacturer}")
        print("-" * 40)
        available_ports.append(port.device)
    
    return available_ports

def test_com5_access():
    """测试COM5端口访问"""
    print("\n🔧 测试COM5端口访问...")
    print("=" * 50)
    
    try:
        # 尝试打开COM5端口
        ser = serial.Serial(
            port='COM5',
            baudrate=9600,
            timeout=1,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            bytesize=serial.EIGHTBITS
        )
        
        print("✅ COM5端口访问成功!")
        print(f"   端口状态: {ser.is_open}")
        print(f"   波特率: {ser.baudrate}")
        
        ser.close()
        return True
        
    except serial.SerialException as e:
        print(f"❌ COM5端口访问失败: {e}")
        return False
    except PermissionError as e:
        print(f"❌ COM5权限错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 未知错误: {e}")
        return False

def check_process_using_com5():
    """检查是否有其他进程占用COM5"""
    print("\n🔍 检查COM5端口占用情况...")
    print("=" * 50)
    
    try:
        # 使用handle.exe检查端口占用（如果可用）
        result = subprocess.run(['handle.exe', 'COM5'], 
                              capture_output=True, text=True, timeout=10)
        if result.stdout:
            print("📋 发现占用COM5的进程:")
            print(result.stdout)
        else:
            print("✅ 未发现明显的COM5占用进程")
    except:
        print("⚠️  无法检查端口占用（需要handle.exe工具）")

def provide_solutions():
    """提供解决方案"""
    print("\n💡 COM5权限问题解决方案:")
    print("=" * 50)
    
    print("🔧 方案1: 关闭占用COM5的程序")
    print("   • 关闭其他串口调试工具")
    print("   • 关闭设备管理器")
    print("   • 关闭其他可能使用COM5的应用程序")
    
    print("\n🔧 方案2: 以管理员身份运行")
    print("   • 右键点击命令提示符")
    print("   • 选择'以管理员身份运行'")
    print("   • 重新启动应用程序")
    
    print("\n🔧 方案3: 重新插拔USB设备")
    print("   • 拔出USB转串口设备")
    print("   • 等待5秒后重新插入")
    print("   • 等待驱动重新加载")
    
    print("\n🔧 方案4: 重启串口服务")
    print("   • 打开设备管理器")
    print("   • 找到'端口(COM和LPT)'")
    print("   • 右键COM5设备 → 禁用设备")
    print("   • 等待3秒后 → 启用设备")
    
    print("\n🔧 方案5: 检查驱动程序")
    print("   • 确保CP210x驱动已正确安装")
    print("   • 更新或重新安装USB转串口驱动")

def create_alternative_connection():
    """创建备用连接方案"""
    print("\n🔄 创建备用连接方案...")
    print("=" * 50)
    
    # 检查其他可用端口
    ports = check_com_ports()
    
    if 'COM5' not in ports:
        print("⚠️  COM5端口未在系统中检测到")
        print("💡 可能的原因:")
        print("   • USB设备未正确连接")
        print("   • 驱动程序未安装")
        print("   • 设备故障")
    
    # 提供其他端口选项
    available_ports = [p for p in ports if p != 'COM5']
    if available_ports:
        print(f"\n📍 发现其他可用端口: {available_ports}")
        print("💡 您可以尝试使用这些端口进行连接")

def main():
    """主函数"""
    print("🔋 鲸测云LCER电池测试仪 - COM5权限问题诊断")
    print("=" * 60)
    
    # 1. 检查COM端口
    available_ports = check_com_ports()
    
    # 2. 测试COM5访问
    com5_accessible = test_com5_access()
    
    # 3. 检查端口占用
    check_process_using_com5()
    
    # 4. 提供解决方案
    provide_solutions()
    
    # 5. 创建备用方案
    create_alternative_connection()
    
    print("\n" + "=" * 60)
    print("📋 诊断总结:")
    print(f"   COM5可访问: {'✅ 是' if com5_accessible else '❌ 否'}")
    print(f"   可用端口数: {len(available_ports)}")
    
    if not com5_accessible:
        print("\n⚠️  建议按照上述方案逐一尝试解决COM5权限问题")
        print("🔄 解决后重新启动应用程序")
    else:
        print("\n✅ COM5端口访问正常，可以启动应用程序")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断诊断")
    except Exception as e:
        print(f"\n❌ 诊断过程出现异常: {e}")
        import traceback
        traceback.print_exc()
