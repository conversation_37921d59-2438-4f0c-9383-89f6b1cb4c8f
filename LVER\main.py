#!/usr/bin/env python3
"""
LVER 串口通信应用程序主入口
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from gui import LVERApp
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保所有必要的模块都在正确的位置")
    sys.exit(1)


def check_dependencies():
    """检查依赖项"""
    try:
        import serial
        import serial.tools.list_ports
        return True
    except ImportError:
        return False


def main():
    """主函数"""
    # 检查依赖项
    if not check_dependencies():
        error_msg = (
            "缺少必要的依赖项！\n\n"
            "请运行以下命令安装依赖：\n"
            "pip install -r requirements.txt\n\n"
            "或者直接安装：\n"
            "pip install pyserial"
        )
        
        # 尝试显示图形错误对话框
        try:
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            messagebox.showerror("依赖项错误", error_msg)
            root.destroy()
        except:
            # 如果图形界面不可用，使用控制台输出
            print("错误:", error_msg)
        
        sys.exit(1)
    
    try:
        # 创建主窗口
        root = tk.Tk()
        app = LVERApp(root)
        
        # 设置关闭事件处理
        root.protocol("WM_DELETE_WINDOW", app.on_closing)
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        error_msg = f"应用程序启动失败: {e}"
        print(error_msg)
        
        # 尝试显示错误对话框
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("启动错误", error_msg)
            root.destroy()
        except:
            pass
        
        sys.exit(1)


if __name__ == "__main__":
    main()
