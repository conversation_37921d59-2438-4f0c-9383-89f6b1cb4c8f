#!/usr/bin/env python3
"""
测试Excel单表导出功能
验证新的单工作表Excel导出功能是否正常工作
"""

import tkinter as tk
from tkinter import messagebox
import random
import time
from datetime import datetime, timedelta

# 导入主应用程序
import sys
import os
sys.path.append(os.path.dirname(__file__))

try:
    from main_fixed_complete import LVERFixedApp
    print("✓ 成功导入增强版应用程序")
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    exit(1)

def generate_realistic_test_data():
    """生成更真实的测试数据，模拟实际测量场景"""
    test_data = []
    base_time = datetime.now()
    
    # 模拟不同的测量场景
    scenarios = [
        # 场景1: 完整的4参数测量
        {
            'name': '完整测量',
            'params': ['V', 'R_sei', 'R_ct', 'R_ohm'],
            'count': 5
        },
        # 场景2: 只有电压测量
        {
            'name': '电压测量',
            'params': ['V'],
            'count': 3
        },
        # 场景3: 电阻测量
        {
            'name': '电阻测量',
            'params': ['R_sei', 'R_ct', 'R_ohm'],
            'count': 4
        },
        # 场景4: 部分参数缺失
        {
            'name': '部分测量',
            'params': ['V', 'R_sei'],
            'count': 3
        }
    ]
    
    time_offset = 0
    for scenario in scenarios:
        print(f"生成 {scenario['name']} 数据...")
        
        for i in range(scenario['count']):
            # 为每个时间点生成数据
            current_time = (base_time + timedelta(seconds=time_offset)).strftime('%H:%M:%S')
            
            for param_type in scenario['params']:
                if param_type == 'V':
                    value = random.randint(3200, 3400)  # 电压范围
                elif param_type == 'R_sei':
                    value = random.randint(270, 290)    # SEI电阻范围
                elif param_type == 'R_ct':
                    value = random.randint(150, 170)    # CT电阻范围
                elif param_type == 'R_ohm':
                    value = random.randint(500, 530)    # 欧姆电阻范围
                
                test_data.append((current_time, param_type, value))
            
            time_offset += 2  # 每次测量间隔2秒
    
    return test_data

def test_excel_single_export():
    """测试Excel单表导出功能"""
    print("=" * 60)
    print("LVER Excel单表导出功能测试")
    print("=" * 60)
    
    # 创建测试应用
    root = tk.Tk()
    app = LVERFixedApp(root)
    
    # 生成测试数据
    print("生成真实测试数据...")
    test_measurements = generate_realistic_test_data()
    
    # 添加测试数据到应用
    for time_str, measure_type, value in test_measurements:
        # 模拟实际的数据添加过程
        data_entry = {
            'time': time_str,
            'type': measure_type,
            'value': value,
            'unit': 'mV' if measure_type == 'V' else 'μΩ'
        }
        app.test_data.append(data_entry)
        
        # 更新实时显示
        if hasattr(app, 'update_realtime_display'):
            app.update_realtime_display(measure_type, value)
        
        time.sleep(0.05)  # 模拟测量间隔
    
    print(f"✓ 生成了 {len(test_measurements)} 条测试数据")
    
    # 分析数据分布
    time_groups = {}
    param_counts = {'V': 0, 'R_sei': 0, 'R_ct': 0, 'R_ohm': 0}
    
    for data in app.test_data:
        time_key = data['time']
        param_type = data['type']
        
        if time_key not in time_groups:
            time_groups[time_key] = set()
        time_groups[time_key].add(param_type)
        param_counts[param_type] += 1
    
    print("\n数据分布分析:")
    print(f"• 时间点数: {len(time_groups)} 个")
    print(f"• 电压测量 (V): {param_counts['V']} 条")
    print(f"• SEI电阻 (R_sei): {param_counts['R_sei']} 条")
    print(f"• CT电阻 (R_ct): {param_counts['R_ct']} 条")
    print(f"• 欧姆电阻 (R_ohm): {param_counts['R_ohm']} 条")
    print(f"• 总记录数: {len(app.test_data)} 条")
    
    # 分析数据完整性
    complete_rows = 0
    partial_rows = 0
    
    for time_key, params in time_groups.items():
        if len(params) == 4:
            complete_rows += 1
        else:
            partial_rows += 1
    
    print(f"\n数据完整性:")
    print(f"• 完整行 (4个参数): {complete_rows} 行")
    print(f"• 部分行 (缺少参数): {partial_rows} 行")
    
    print("\n" + "=" * 60)
    print("Excel单表导出功能测试")
    print("=" * 60)
    
    print("新的Excel导出选项:")
    print("1. 📊 Excel表格导出 (单表格式) - 新功能")
    print("2. 📊 Excel格式导出 (多工作表) - 原有功能")
    
    print("\n单表格式特点:")
    print("• 所有数据在一个工作表中")
    print("• 按时间组织，每行包含同一时间点的所有参数")
    print("• 缺失数据显示为 'N/A'")
    print("• 包含详细的统计信息")
    print("• 专业的表格格式和样式")
    
    # 显示应用程序
    root.deiconify()
    root.lift()
    root.focus_force()
    
    # 显示测试说明
    messagebox.showinfo("Excel单表导出测试", 
                       f"""测试数据已生成完成！

数据概况:
• 时间点数: {len(time_groups)} 个
• 完整数据行: {complete_rows} 行
• 部分数据行: {partial_rows} 行
• 总记录数: {len(app.test_data)} 条

请测试新的Excel导出功能:
📊 Excel表格导出 (单表格式)

特点:
• 单工作表格式
• 按时间组织数据
• 缺失值显示为 N/A
• 包含统计信息

点击 '导出数据 ▼' → '📊 Excel表格导出 (单表格式)' 开始测试！""")
    
    print("✓ 测试应用程序已启动")
    print("✓ 测试数据已加载")
    print("✓ 请在GUI中测试Excel单表导出功能")
    
    root.mainloop()

def show_excel_single_features():
    """显示Excel单表导出功能特性"""
    print("\n" + "=" * 60)
    print("Excel单表导出功能特性")
    print("=" * 60)
    
    features = [
        "📊 Excel表格导出 (单表格式) - 新功能",
        "  • 单个工作表包含所有数据",
        "  • 表格结构: 时间 | V电压 | R_sei电阻 | R_ct电阻 | R_ohm电阻",
        "  • 按时间戳组织数据行",
        "  • 缺失数据显示为 'N/A'",
        "  • 专业的表格样式和格式",
        "  • 包含详细的统计信息",
        "",
        "📊 Excel格式导出 (多工作表) - 原有功能",
        "  • 多个工作表，每种参数独立",
        "  • 包含汇总工作表",
        "  • 专业的格式和样式",
        "",
        "🔧 技术特点",
        "  • 自动数据分组和对齐",
        "  • 智能处理缺失数据",
        "  • 专业的Excel样式",
        "  • 完整的统计分析",
        "  • 自动列宽调整",
        "  • 边框和对齐格式",
        "",
        "📋 文件格式",
        "  • 文件名: LVER_综合数据表_时间戳.xlsx",
        "  • 编码: Excel标准格式",
        "  • 兼容性: Excel 2007+",
        "  • 大小: 根据数据量动态"
    ]
    
    for feature in features:
        print(feature)
    
    print("=" * 60)

def main():
    print("LVER Excel单表导出功能测试工具")
    show_excel_single_features()
    
    # 询问是否开始测试
    root = tk.Tk()
    root.withdraw()
    
    if messagebox.askyesno("开始测试", 
                          "是否要启动LVER应用程序并生成测试数据？\n\n这将创建一个包含多种数据场景的测试数据集，\n包括完整数据行和部分数据行，\n然后您可以测试新的Excel单表导出功能。"):
        root.destroy()
        test_excel_single_export()
    else:
        print("用户取消了测试")
        root.destroy()

if __name__ == "__main__":
    main()
