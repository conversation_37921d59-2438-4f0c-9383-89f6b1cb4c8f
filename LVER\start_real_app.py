#!/usr/bin/env python3
"""
智能启动脚本 - 自动检测Python环境并启动真实的串口通信应用程序
"""

import sys
import os
import subprocess
import importlib.util

def find_python_with_serial():
    """查找安装了pyserial的Python解释器"""
    python_candidates = [
        sys.executable,  # 当前Python
        "python",
        "python3",
        "py -3",
        r"C:\Program Files\Python38\python.exe",
        r"C:\Program Files\Python39\python.exe",
        r"C:\Program Files\Python310\python.exe",
        r"C:\Program Files\Python311\python.exe",
        r"C:\Program Files\Python312\python.exe",
        r"C:\Program Files\Python313\python.exe",
        r"C:\Python38\python.exe",
        r"C:\Python39\python.exe",
        r"C:\Python310\python.exe",
        r"C:\Python311\python.exe",
        r"C:\Python312\python.exe",
        r"C:\Python313\python.exe",
    ]
    
    print("正在搜索可用的Python环境...")
    
    for python_cmd in python_candidates:
        try:
            # 测试Python是否可用
            result = subprocess.run(
                [python_cmd, "-c", "import sys; print(sys.version)"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                python_version = result.stdout.strip()
                print(f"✓ 找到Python: {python_cmd}")
                print(f"  版本: {python_version}")
                
                # 测试pyserial是否可用
                result = subprocess.run(
                    [python_cmd, "-c", "import serial; import serial.tools.list_ports; print('pyserial可用')"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    print(f"✓ pyserial 在此Python环境中可用")
                    return python_cmd
                else:
                    print(f"✗ pyserial 在此Python环境中不可用")
                    
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            continue
    
    return None

def install_pyserial(python_cmd):
    """尝试安装pyserial"""
    print(f"\n尝试为 {python_cmd} 安装 pyserial...")
    
    try:
        # 尝试使用pip安装
        result = subprocess.run(
            [python_cmd, "-m", "pip", "install", "pyserial"],
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            print("✓ pyserial 安装成功")
            return True
        else:
            print(f"✗ pip安装失败: {result.stderr}")
            
            # 尝试使用用户安装
            result = subprocess.run(
                [python_cmd, "-m", "pip", "install", "--user", "pyserial"],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                print("✓ pyserial 用户安装成功")
                return True
            else:
                print(f"✗ 用户安装也失败: {result.stderr}")
                return False
                
    except subprocess.TimeoutExpired:
        print("✗ 安装超时")
        return False
    except Exception as e:
        print(f"✗ 安装过程中出错: {e}")
        return False

def check_serial_ports(python_cmd):
    """检查可用的串口"""
    try:
        result = subprocess.run(
            [python_cmd, "-c", """
import serial.tools.list_ports
ports = list(serial.tools.list_ports.comports())
print(f'发现 {len(ports)} 个串口设备:')
for port in ports:
    print(f'  {port.device} - {port.description}')
if not ports:
    print('  未发现任何串口设备')
"""],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        if result.returncode == 0:
            print("\n串口设备检测结果:")
            print(result.stdout)
            return True
        else:
            print(f"串口检测失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"串口检测出错: {e}")
        return False

def start_main_app(python_cmd):
    """启动主应用程序"""
    print(f"\n启动真实串口通信应用程序...")
    print("=" * 50)
    
    try:
        # 启动主应用程序
        subprocess.run([python_cmd, "main.py"], cwd=os.path.dirname(os.path.abspath(__file__)))
    except KeyboardInterrupt:
        print("\n应用程序被用户中断")
    except Exception as e:
        print(f"启动应用程序时出错: {e}")

def main():
    """主函数"""
    print("LVER 真实串口通信应用程序启动器")
    print("=" * 50)
    
    # 查找可用的Python环境
    python_cmd = find_python_with_serial()
    
    if python_cmd:
        print(f"\n✓ 将使用Python: {python_cmd}")
        
        # 检查串口设备
        check_serial_ports(python_cmd)
        
        # 启动应用程序
        start_main_app(python_cmd)
        
    else:
        print("\n✗ 未找到安装了pyserial的Python环境")
        
        # 尝试安装pyserial
        current_python = sys.executable
        print(f"\n尝试为当前Python环境安装pyserial: {current_python}")
        
        if install_pyserial(current_python):
            print("\n✓ pyserial安装成功，重新检测...")
            python_cmd = find_python_with_serial()
            
            if python_cmd:
                check_serial_ports(python_cmd)
                start_main_app(python_cmd)
            else:
                print("✗ 安装后仍无法使用pyserial")
        else:
            print("\n❌ 无法安装pyserial，请手动安装:")
            print("1. 打开命令提示符（管理员权限）")
            print("2. 运行: pip install pyserial")
            print("3. 或者运行: python -m pip install pyserial")
            print("4. 如果有多个Python版本，请指定具体版本")
            
            input("\n按回车键退出...")

if __name__ == "__main__":
    main()
