#!/usr/bin/env python3
"""
测试新的左右分栏布局功能
验证实时曲线图和数据记录表格的显示效果
"""

import tkinter as tk
from tkinter import ttk, messagebox
import random
from datetime import datetime, timedelta
import time

def test_layout_functionality():
    """测试新布局功能的演示"""
    print("=" * 60)
    print("LVER 新布局功能测试")
    print("=" * 60)
    
    print("新布局特点:")
    print("✓ 左侧：实时曲线图 (70%空间)")
    print("✓ 右侧：数据记录表格 (30%空间)")
    print("✓ 表格按时间组织数据")
    print("✓ 缺失数据显示为'N/A'")
    print("✓ 支持表格操作功能")
    
    print("\n表格列结构:")
    columns = ['时间', 'V电压(mV)', 'R_sei电阻(μΩ)', 'R_ct电阻(μΩ)', 'R_ohm电阻(μΩ)']
    for i, col in enumerate(columns, 1):
        print(f"  {i}. {col}")
    
    print("\n数据组织方式:")
    print("• 按时间戳分组")
    print("• 每行代表一个时间点的所有参数")
    print("• 缺失参数显示为'N/A'")
    print("• 最新数据在顶部")
    
    # 模拟数据示例
    print("\n" + "=" * 60)
    print("数据记录表格示例")
    print("=" * 60)
    
    # 生成示例数据
    base_time = datetime.now()
    sample_data = []
    
    for i in range(8):
        time_point = (base_time - timedelta(seconds=i*2)).strftime("%H:%M:%S")
        
        # 模拟不同的数据完整性场景
        if i == 0:  # 完整数据
            data = {'V': 3250, 'R_sei': 275, 'R_ct': 165, 'R_ohm': 520}
        elif i == 1:  # 只有电压
            data = {'V': 3240, 'R_sei': 'N/A', 'R_ct': 'N/A', 'R_ohm': 'N/A'}
        elif i == 2:  # 只有电阻
            data = {'V': 'N/A', 'R_sei': 280, 'R_ct': 170, 'R_ohm': 525}
        elif i == 3:  # 部分数据
            data = {'V': 3255, 'R_sei': 270, 'R_ct': 'N/A', 'R_ohm': 'N/A'}
        else:  # 随机完整数据
            data = {
                'V': 3200 + random.randint(0, 100),
                'R_sei': 250 + random.randint(0, 50),
                'R_ct': 150 + random.randint(0, 30),
                'R_ohm': 500 + random.randint(0, 50)
            }
        
        sample_data.append((time_point, data))
    
    # 显示表格格式
    header = f"{'时间':<10} | {'V电压(mV)':<10} | {'R_sei电阻(μΩ)':<15} | {'R_ct电阻(μΩ)':<14} | {'R_ohm电阻(μΩ)':<16}"
    print(header)
    print("-" * len(header))
    
    for time_point, data in sample_data:
        row = f"{time_point:<10} | {str(data['V']):<10} | {str(data['R_sei']):<15} | {str(data['R_ct']):<14} | {str(data['R_ohm']):<16}"
        print(row)
    
    print("\n" + "=" * 60)
    print("布局优势")
    print("=" * 60)
    
    advantages = [
        "1. 直观对比：左侧图表显示趋势，右侧表格显示精确数值",
        "2. 空间优化：7:3比例分配，图表有足够显示空间",
        "3. 数据完整性：表格清晰显示每个时间点的数据完整情况",
        "4. 操作便利：支持复制选中数据、清空表格等操作",
        "5. 实时更新：数据添加时自动更新表格和图表",
        "6. 时间对齐：相同时间点的数据在同一行显示"
    ]
    
    for advantage in advantages:
        print(f"✓ {advantage}")
    
    print("\n" + "=" * 60)
    print("表格操作功能")
    print("=" * 60)
    
    operations = [
        "• 复制选中：选择数据行后点击'复制选中'按钮",
        "• 清空表格：点击'清空表格'按钮清除所有数据",
        "• 滚动查看：支持垂直滚动查看历史数据",
        "• 列宽调整：各列宽度已优化适应数据显示",
        "• 居中对齐：所有数据居中显示，便于阅读"
    ]
    
    for operation in operations:
        print(operation)
    
    return sample_data

def create_layout_demo():
    """创建布局演示窗口"""
    demo_root = tk.Tk()
    demo_root.title("LVER 新布局演示")
    demo_root.geometry("1000x600")
    
    # 创建左右分栏
    paned = ttk.PanedWindow(demo_root, orient=tk.HORIZONTAL)
    paned.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
    
    # 左侧：模拟图表区域
    chart_frame = ttk.LabelFrame(paned, text="实时曲线图 (左侧)", padding=10)
    paned.add(chart_frame, weight=7)
    
    chart_demo = tk.Text(chart_frame, bg='lightblue', font=('Arial', 12))
    chart_demo.pack(fill=tk.BOTH, expand=True)
    chart_demo.insert('1.0', """
    📈 实时曲线图区域 (70%空间)
    
    特点：
    • 4条参数曲线实时显示
    • V电压 - 蓝色线
    • R_sei电阻 - 绿色线  
    • R_ct电阻 - 紫色线
    • R_ohm电阻 - 红色线
    
    功能：
    • 实时数据更新
    • 图表缩放和导出
    • 趋势分析
    • 数据可视化
    
    优化：
    • 更大的显示空间
    • 更清晰的数据趋势
    • 更好的用户体验
    """)
    chart_demo.config(state='disabled')
    
    # 右侧：模拟数据表格区域
    data_frame = ttk.LabelFrame(paned, text="数据记录表格 (右侧)", padding=5)
    paned.add(data_frame, weight=3)
    
    # 创建演示表格
    columns = ('时间', 'V电压(mV)', 'R_sei电阻(μΩ)', 'R_ct电阻(μΩ)', 'R_ohm电阻(μΩ)')
    demo_tree = ttk.Treeview(data_frame, columns=columns, show='headings', height=15)
    
    # 设置列
    column_widths = {'时间': 80, 'V电压(mV)': 80, 'R_sei电阻(μΩ)': 90, 'R_ct电阻(μΩ)': 85, 'R_ohm电阻(μΩ)': 95}
    for col in columns:
        demo_tree.heading(col, text=col)
        demo_tree.column(col, width=column_widths.get(col, 80), anchor='center')
    
    # 添加演示数据
    demo_data = [
        ("10:45:30", "3250", "275", "165", "520"),
        ("10:45:28", "3240", "N/A", "N/A", "N/A"),
        ("10:45:26", "N/A", "280", "170", "525"),
        ("10:45:24", "3255", "270", "N/A", "N/A"),
        ("10:45:22", "3245", "285", "175", "530"),
        ("10:45:20", "3260", "290", "180", "535"),
    ]
    
    for data in demo_data:
        demo_tree.insert('', 'end', values=data)
    
    # 滚动条
    scrollbar = ttk.Scrollbar(data_frame, orient=tk.VERTICAL, command=demo_tree.yview)
    demo_tree.configure(yscrollcommand=scrollbar.set)
    
    demo_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 操作按钮
    button_frame = ttk.Frame(data_frame)
    button_frame.pack(fill=tk.X, pady=(5, 0))
    
    def demo_copy():
        messagebox.showinfo("演示", "复制选中数据功能")
    
    def demo_clear():
        if messagebox.askyesno("演示", "清空表格数据？"):
            for item in demo_tree.get_children():
                demo_tree.delete(item)
    
    ttk.Button(button_frame, text="复制选中", command=demo_copy).pack(side=tk.LEFT, padx=(0, 5))
    ttk.Button(button_frame, text="清空表格", command=demo_clear).pack(side=tk.LEFT)
    
    # 说明标签
    info_label = ttk.Label(demo_root, text="演示：左侧实时曲线图 (70%) + 右侧数据记录表格 (30%)", 
                          font=('Arial', 10, 'bold'))
    info_label.pack(pady=5)
    
    return demo_root

def main():
    print("LVER 新布局功能测试工具")
    print("=" * 60)
    
    # 测试布局功能
    sample_data = test_layout_functionality()
    
    print("\n" + "=" * 60)
    print("启动布局演示")
    print("=" * 60)
    
    try:
        # 创建演示窗口
        demo_window = create_layout_demo()
        
        print("✓ 布局演示窗口已启动")
        print("✓ 可以查看新的左右分栏布局效果")
        print("✓ 左侧：实时曲线图区域")
        print("✓ 右侧：数据记录表格区域")
        
        # 运行演示
        demo_window.mainloop()
        
    except Exception as e:
        print(f"❌ 演示启动失败: {e}")
    
    print("\n测试完成！")
    print("请在LVER主应用程序中验证新布局功能。")

if __name__ == "__main__":
    main()
