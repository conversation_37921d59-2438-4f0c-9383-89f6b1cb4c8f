#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复版本 - 验证数据准确性修复效果
简化版本，专门用于测试串口数据处理功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import time
from datetime import datetime
import re

class MockSerial:
    """模拟串口类，包含完整的 in_waiting 支持"""
    
    def __init__(self, port=None, baudrate=9600, timeout=None):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.is_open = False
        self.in_waiting = 0
        self.mock_response = ""
        
    def open(self):
        self.is_open = True
        print(f"✓ 模拟连接到 {self.port}")
        
    def close(self):
        self.is_open = False
        print(f"✓ 模拟断开 {self.port}")
        
    def write(self, data):
        print(f"📤 发送命令: {data.hex().upper()}")
        
        # 模拟真实设备响应
        if data == b'\xAA':  # 测试命令
            self.mock_response = "Start Voltage : 3290\nHF_current:0 mA zeroPoint:21\n1,V,3290\n2,R_sei,281\n3,R_ct,165\n4,R_ohm,502\n"
            print("📥 模拟设备响应: 测试命令数据")
        elif data == b'\x55':  # 电压命令
            self.mock_response = "Start Voltage : 3290\n1,V,3290\n"
            print("📥 模拟设备响应: 电压测量数据")
        else:
            self.mock_response = ""
        
        self.in_waiting = len(self.mock_response.encode('utf-8'))
        return len(data)
        
    def read(self, size=1):
        if self.mock_response:
            data = self.mock_response.encode('utf-8')[:size]
            consumed = data.decode('utf-8', errors='ignore')
            self.mock_response = self.mock_response[len(consumed):]
            self.in_waiting = len(self.mock_response.encode('utf-8'))
            return data
        return b''

class DataProcessor:
    """数据处理器 - 修复版"""
    
    def __init__(self):
        self.debug_mode = True
        self.last_measurements = {}
        
    def process_serial_response(self, serial_conn, timeout=3.0):
        """处理串口响应数据"""
        if not serial_conn or not serial_conn.is_open:
            return []
        
        start_time = time.time()
        response_data = ""
        
        # 读取串口数据
        while time.time() - start_time < timeout:
            if serial_conn.in_waiting > 0:
                try:
                    chunk = serial_conn.read(serial_conn.in_waiting).decode('utf-8', errors='ignore')
                    response_data += chunk
                    
                    if self.debug_mode:
                        print(f"🔍 接收数据: {repr(chunk)}")
                    
                    # 检查是否接收完整
                    if self._is_response_complete(response_data):
                        break
                        
                except Exception as e:
                    print(f"❌ 串口读取错误: {e}")
                    break
            
            time.sleep(0.01)
        
        if self.debug_mode:
            print(f"📋 完整响应: {repr(response_data)}")
        
        return self._parse_response_data(response_data)
    
    def _is_response_complete(self, data):
        """检查响应数据是否完整"""
        csv_lines = [line for line in data.split('\n') if ',' in line and line.strip()]
        return len(csv_lines) >= 1
    
    def _parse_response_data(self, response_data):
        """解析响应数据"""
        measurements = []
        
        if not response_data.strip():
            return measurements
        
        lines = response_data.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            
            # 跳过调试信息
            if self._is_debug_line(line):
                if self.debug_mode:
                    print(f"⏭️  跳过调试行: {line}")
                continue
            
            # 解析CSV格式数据
            parsed_data = self._parse_csv_line(line)
            if parsed_data:
                measurements.append(parsed_data)
                if self.debug_mode:
                    print(f"✅ 解析成功: {parsed_data}")
        
        return measurements
    
    def _is_debug_line(self, line):
        """判断是否为调试信息行"""
        debug_patterns = [
            r'^Start Voltage\s*:',
            r'^HF_current\s*:',
            r'^.*zeroPoint\s*:',
            r'^\s*$'
        ]
        
        for pattern in debug_patterns:
            if re.match(pattern, line, re.IGNORECASE):
                return True
        return False
    
    def _parse_csv_line(self, line):
        """解析CSV格式行"""
        if ',' not in line:
            return None
        
        try:
            parts = line.split(',')
            if len(parts) < 3:
                return None
            
            param_id = parts[0].strip()
            param_type = parts[1].strip()
            param_value_str = parts[2].strip()
            
            if not param_id.isdigit():
                return None
            
            valid_types = ['V', 'R_sei', 'R_ct', 'R_ohm']
            if param_type not in valid_types:
                return None
            
            param_value = int(param_value_str)
            
            return {
                'param_id': int(param_id),
                'param_type': param_type,
                'param_value': param_value,
                'timestamp': datetime.now(),
                'raw_line': line
            }
            
        except Exception as e:
            if self.debug_mode:
                print(f"❌ CSV解析异常: {e}")
            return None

class TestApp:
    """测试应用程序"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🔋 数据准确性修复测试")
        self.root.geometry("600x400")
        
        self.serial_conn = None
        self.processor = DataProcessor()
        
        self.create_widgets()
        
    def create_widgets(self):
        """创建界面"""
        # 标题
        title_label = tk.Label(self.root, text="🔋 鲸测云LCER电池测试仪 - 修复测试", 
                              font=("Arial", 14, "bold"))
        title_label.pack(pady=10)
        
        # 连接区域
        conn_frame = ttk.Frame(self.root)
        conn_frame.pack(pady=10)
        
        ttk.Label(conn_frame, text="串口:").pack(side=tk.LEFT)
        self.port_var = tk.StringVar(value="COM5")
        ttk.Entry(conn_frame, textvariable=self.port_var, width=10).pack(side=tk.LEFT, padx=5)
        
        self.connect_btn = ttk.Button(conn_frame, text="连接", command=self.connect)
        self.connect_btn.pack(side=tk.LEFT, padx=5)
        
        self.disconnect_btn = ttk.Button(conn_frame, text="断开", command=self.disconnect, state="disabled")
        self.disconnect_btn.pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        self.status_label = tk.Label(self.root, text="状态: 未连接", fg="red")
        self.status_label.pack(pady=5)
        
        # 测试按钮
        test_frame = ttk.Frame(self.root)
        test_frame.pack(pady=10)
        
        ttk.Button(test_frame, text="🧪 测试命令 (0xAA)", command=self.test_command).pack(side=tk.LEFT, padx=5)
        ttk.Button(test_frame, text="⚡ 电压命令 (0x55)", command=self.voltage_command).pack(side=tk.LEFT, padx=5)
        
        # 结果显示
        result_frame = ttk.Frame(self.root)
        result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        ttk.Label(result_frame, text="测试结果:").pack(anchor=tk.W)
        
        self.result_text = tk.Text(result_frame, height=15, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        self.result_text.configure(yscrollcommand=scrollbar.set)
        
        self.result_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 清空按钮
        ttk.Button(self.root, text="清空日志", command=self.clear_log).pack(pady=5)
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}\n"
        self.result_text.insert(tk.END, log_message)
        self.result_text.see(tk.END)
        print(log_message.strip())
        
    def connect(self):
        """连接串口"""
        try:
            port = self.port_var.get()
            self.serial_conn = MockSerial(port=port, baudrate=9600, timeout=1)
            self.serial_conn.open()
            
            self.status_label.config(text=f"状态: 已连接到 {port}", fg="green")
            self.connect_btn.config(state="disabled")
            self.disconnect_btn.config(state="normal")
            
            self.log(f"✅ 成功连接到 {port}")
            
        except Exception as e:
            self.log(f"❌ 连接失败: {e}")
            messagebox.showerror("连接失败", str(e))
    
    def disconnect(self):
        """断开连接"""
        if self.serial_conn:
            self.serial_conn.close()
            self.serial_conn = None
            
        self.status_label.config(text="状态: 未连接", fg="red")
        self.connect_btn.config(state="normal")
        self.disconnect_btn.config(state="disabled")
        
        self.log("🔌 已断开连接")
    
    def test_command(self):
        """测试命令"""
        if not self.serial_conn or not self.serial_conn.is_open:
            messagebox.showwarning("警告", "请先连接串口")
            return
        
        try:
            self.log("🧪 执行测试命令 (0xAA)...")
            
            # 发送命令
            self.serial_conn.write(b'\xAA')
            
            # 处理响应
            measurements = self.processor.process_serial_response(self.serial_conn, timeout=3.0)
            
            if measurements:
                self.log(f"📊 获得 {len(measurements)} 个测量值:")
                for measurement in measurements:
                    param_type = measurement['param_type']
                    param_value = measurement['param_value']
                    self.log(f"   {param_type}: {param_value}")
                
                # 验证电压值
                voltage_measurements = [m for m in measurements if m['param_type'] == 'V']
                if voltage_measurements:
                    voltage = voltage_measurements[0]['param_value']
                    if voltage == 3290:
                        self.log("✅ 电压值正确: 3290 mV (与参考软件一致)")
                    else:
                        self.log(f"⚠️  电压值: {voltage} mV (期望: 3290 mV)")
                
                self.log("✅ 测试命令执行成功")
            else:
                self.log("❌ 未接收到有效数据")
                
        except Exception as e:
            self.log(f"❌ 测试命令失败: {e}")
    
    def voltage_command(self):
        """电压命令"""
        if not self.serial_conn or not self.serial_conn.is_open:
            messagebox.showwarning("警告", "请先连接串口")
            return
        
        try:
            self.log("⚡ 执行电压命令 (0x55)...")
            
            # 发送命令
            self.serial_conn.write(b'\x55')
            
            # 处理响应
            measurements = self.processor.process_serial_response(self.serial_conn, timeout=2.0)
            
            voltage_measurement = None
            for measurement in measurements:
                if measurement['param_type'] == 'V':
                    voltage_measurement = measurement
                    break
            
            if voltage_measurement:
                voltage = voltage_measurement['param_value']
                self.log(f"⚡ 电压测量: {voltage} mV")
                
                if voltage == 3290:
                    self.log("✅ 电压值稳定: 3290 mV (修复成功)")
                else:
                    self.log(f"⚠️  电压值: {voltage} mV")
                
                self.log("✅ 电压命令执行成功")
            else:
                self.log("❌ 未接收到电压数据")
                
        except Exception as e:
            self.log(f"❌ 电压命令失败: {e}")
    
    def clear_log(self):
        """清空日志"""
        self.result_text.delete(1.0, tk.END)
    
    def run(self):
        """运行应用"""
        self.log("🚀 数据准确性修复测试程序启动")
        self.log("📝 请连接串口并执行测试命令验证修复效果")
        self.root.mainloop()

if __name__ == "__main__":
    app = TestApp()
    app.run()
