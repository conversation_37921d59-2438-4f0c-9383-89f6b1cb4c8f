#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查所有羽毛图标脚本
搜索目录中所有Python文件，查找可能的羽毛图标或类似图标
"""

import os
import glob

def check_all_feather_icons():
    """检查所有文件中的羽毛图标"""
    print("=" * 80)
    print("🔍 检查所有文件中的羽毛图标和相关图标")
    print("=" * 80)
    
    # 可能的羽毛图标和相关图标
    feather_patterns = [
        '🪶',  # 羽毛
        '✏️',  # 铅笔
        '🖊️',  # 钢笔
        '🖋️',  # 钢笔尖
        '📝',  # 备忘录
        '✍️',  # 写字
        '🖍️',  # 蜡笔
    ]
    
    # 获取所有Python文件
    python_files = glob.glob('*.py')
    
    total_found = 0
    
    for filename in python_files:
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.split('\n')
            
            file_found = False
            
            for pattern in feather_patterns:
                if pattern in content:
                    if not file_found:
                        print(f"\n📁 文件: {filename}")
                        file_found = True
                    
                    print(f"  发现图标: {pattern}")
                    
                    # 显示包含该图标的行
                    for i, line in enumerate(lines, 1):
                        if pattern in line:
                            print(f"    第{i}行: {line.strip()}")
                            total_found += 1
            
        except Exception as e:
            print(f"❌ 读取文件 {filename} 时出错: {e}")
    
    print(f"\n📊 总计发现: {total_found} 处图标")
    
    if total_found == 0:
        print("✅ 未发现任何羽毛图标或相关图标")
    else:
        print("⚠️  发现图标，需要进一步检查")
    
    print("=" * 80)
    
    # 特别检查main_fixed_complete.py
    print("\n🎯 特别检查 main_fixed_complete.py:")
    try:
        with open('main_fixed_complete.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查所有可能的图标
        all_icons = ['🪶', '✏️', '🖊️', '🖋️', '📝', '✍️', '🖍️', '📊', '📄', '📁', '🔋']
        
        for icon in all_icons:
            count = content.count(icon)
            if count > 0:
                print(f"  {icon}: {count} 次")
            else:
                print(f"  {icon}: 0 次 ✅")
        
    except Exception as e:
        print(f"❌ 检查main_fixed_complete.py时出错: {e}")

if __name__ == "__main__":
    check_all_feather_icons()
