# LVER Excel单表导出功能说明

## 📋 功能概述

新增了Excel单表导出功能，提供更直观的数据查看方式。与原有的多工作表Excel导出功能并存，用户可根据需要选择合适的导出格式。

## 🎯 新功能特点

### 📊 Excel表格导出 (单表格式)
- **单工作表设计**: 所有数据集中在一个工作表中
- **按时间组织**: 每行代表一个时间点的所有参数测量值
- **智能数据对齐**: 自动将同一时间点的不同参数对齐到同一行
- **缺失值处理**: 缺少的参数显示为"N/A"
- **专业格式**: 包含表头样式、边框、对齐等专业格式

## 📊 表格结构

### 列结构
| 时间 | V电压(mV) | R_sei电阻(μΩ) | R_ct电阻(μΩ) | R_ohm欧姆电阻(μΩ) |
|------|-----------|---------------|--------------|-------------------|
| 10:15:30 | 3250 | 275 | 165 | 520 |
| 10:15:32 | 3240 | N/A | N/A | N/A |
| 10:15:34 | N/A | 280 | 170 | 525 |

### 数据组织方式
1. **时间分组**: 按时间戳对数据进行分组
2. **参数对齐**: 同一时间点的所有参数排列在同一行
3. **缺失处理**: 如果某个时间点缺少某种参数，该单元格显示"N/A"
4. **时间排序**: 按时间顺序排列数据行

## 🎨 格式特性

### 表格样式
- **标题行**: 蓝色背景，白色粗体字
- **数据行**: 居中对齐，带边框
- **列宽**: 自动调整，确保内容完整显示
- **数字格式**: 整数格式，便于阅读

### 统计信息
- **数据概况**: 时间点数、总记录数
- **参数统计**: 每种参数的数据点数、最大值、最小值、平均值
- **完整性分析**: 自动计算数据完整性

## 🚀 使用方法

### 1. 启动导出
1. 在LVER应用程序中点击"导出数据 ▼"按钮
2. 选择"📊 Excel表格导出 (单表格式)"
3. 选择保存位置和文件名

### 2. 文件命名
- **默认格式**: `LVER_综合数据表_YYYYMMDD_HHMMSS.xlsx`
- **示例**: `LVER_综合数据表_20250630_101530.xlsx`

### 3. 查看结果
- 导出成功后会显示详细的统计信息
- 可选择自动打开Excel文件查看结果

## 📋 导出选项对比

### 现有导出选项
1. **📄 统一导出 (单文件)** - CSV格式，所有数据按时间顺序
2. **📁 分类导出 (多文件)** - 每种参数独立的CSV文件
3. **📊 Excel表格导出 (单表格式)** - 🆕 新增，单工作表Excel格式
4. **📊 Excel格式导出 (多工作表)** - 原有，多工作表Excel格式

### 选择建议
- **快速查看**: 使用Excel单表导出
- **专业分析**: 使用Excel多工作表导出
- **数据处理**: 使用分类导出
- **简单备份**: 使用统一导出

## 🔧 技术实现

### 数据处理流程
```python
# 1. 按时间戳分组数据
time_data = {}
for data in self.test_data:
    time_key = data['time']
    if time_key not in time_data:
        time_data[time_key] = {'V': None, 'R_sei': None, 'R_ct': None, 'R_ohm': None}
    time_data[time_key][data['type']] = data['value']

# 2. 按时间排序
sorted_times = sorted(time_data.keys())

# 3. 生成Excel表格
for time_key in sorted_times:
    # 写入时间和各参数值，缺失值显示为"N/A"
```

### 样式设置
- **表头**: 蓝色背景 (#366092)，白色粗体字
- **边框**: 全表格细线边框
- **对齐**: 居中对齐
- **列宽**: 自动调整 (时间20字符，参数15-20字符)

## 📊 示例输出

### 文件内容示例
```
LVER 多参数综合数据表

导出时间: 2025-06-30 10:15:30
数据点数: 15 个时间点
总记录数: 45 条

时间      | V电压(mV) | R_sei电阻(μΩ) | R_ct电阻(μΩ) | R_ohm欧姆电阻(μΩ)
----------|-----------|---------------|--------------|-------------------
10:15:30  | 3250      | 275           | 165          | 520
10:15:32  | 3240      | N/A           | N/A          | N/A
10:15:34  | N/A       | 280           | 170          | 525
...

数据统计
参数类型          | 数据点数 | 最大值 | 最小值 | 平均值
V电压(mV)         | 12       | 3400   | 3200   | 3325.5
R_sei电阻(μΩ)     | 15       | 290    | 270    | 280.2
R_ct电阻(μΩ)      | 14       | 170    | 150    | 160.8
R_ohm欧姆电阻(μΩ) | 13       | 530    | 500    | 515.3
```

## ⚠️ 注意事项

### 数据要求
- 需要有测量数据才能导出
- 支持部分数据缺失的情况
- 时间戳格式需要一致

### 系统要求
- 需要安装openpyxl库 (应用程序会自动检查)
- Excel 2007或更高版本 (查看文件)
- 足够的磁盘空间

### 兼容性
- 与现有导出功能完全兼容
- 不影响其他功能的使用
- 支持所有Windows版本

## 🔍 故障排除

### 常见问题
1. **openpyxl库缺失**: 应用程序会自动提示并降级到CSV导出
2. **文件权限问题**: 确保目标目录有写入权限
3. **Excel文件被占用**: 关闭Excel后重试
4. **数据为空**: 先进行测量获取数据

### 错误处理
- 完整的异常捕获和用户提示
- 自动降级到其他导出方式
- 详细的错误信息显示

---

**更新时间**: 2025-06-30  
**版本**: v2.1 增强版  
**新增功能**: Excel单表导出
