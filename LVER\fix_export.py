#!/usr/bin/env python3
"""
修复导出功能 - 解决文件对话框参数错误
"""

import tkinter as tk
from tkinter import filedialog, messagebox
import csv
import os
from datetime import datetime

def test_export_dialog():
    """测试导出对话框功能"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    try:
        # 生成默认文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_filename = f"LVER_多参数数据_{timestamp}.csv"
        
        print(f"默认文件名: {default_filename}")
        
        # 测试文件保存对话框 - 使用正确的参数名
        filename = filedialog.asksaveasfilename(
            title="导出测量数据",
            defaultextension=".csv",
            filetypes=[
                ("CSV 文件", "*.csv"),
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ],
            initialfile=default_filename  # 使用 initialfile 而不是 initialname
        )
        
        if filename:
            print(f"✓ 用户选择的文件: {filename}")
            
            # 创建测试数据
            test_data = [
                {'time': '09:57:00', 'type': 'V', 'value': 3327, 'unit': 'mV'},
                {'time': '09:57:00', 'type': 'R_sei', 'value': 285, 'unit': 'μΩ'},
                {'time': '09:57:00', 'type': 'R_ct', 'value': 304, 'unit': 'μΩ'},
                {'time': '09:57:00', 'type': 'R_ohm', 'value': 270, 'unit': 'μΩ'},
            ]
            
            # 写入CSV文件
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['时间', '参数类型', '测量值', '单位'])
                
                for data in test_data:
                    writer.writerow([data['time'], data['type'], data['value'], data['unit']])
            
            print(f"✓ 文件写入成功: {filename}")
            
            # 验证文件
            if os.path.exists(filename):
                file_size = os.path.getsize(filename)
                print(f"✓ 文件验证成功，大小: {file_size} 字节")
                
                # 读取并显示内容
                with open(filename, 'r', encoding='utf-8-sig') as f:
                    content = f.read()
                    print("文件内容:")
                    print("-" * 40)
                    print(content)
                    print("-" * 40)
                
                messagebox.showinfo("导出成功", f"测试导出成功！\n\n文件: {filename}\n大小: {file_size} 字节")
                
                # 询问是否打开文件
                if messagebox.askyesno("打开文件", "是否要打开导出的文件？"):
                    try:
                        os.startfile(filename)
                        print("✓ 文件已打开")
                    except Exception as e:
                        print(f"❌ 无法打开文件: {e}")
                        messagebox.showerror("打开失败", f"无法打开文件: {e}")
            else:
                print("❌ 文件验证失败")
                messagebox.showerror("导出失败", "文件创建失败")
        else:
            print("用户取消了文件选择")
            messagebox.showinfo("取消", "用户取消了导出操作")
            
    except Exception as e:
        print(f"❌ 导出测试失败: {e}")
        messagebox.showerror("导出失败", f"导出测试失败:\n\n{str(e)}")
    
    root.destroy()

def create_fixed_export_function():
    """创建修复后的导出函数代码"""
    fixed_code = '''
def export_data(self):
    """导出数据到CSV文件 - 修复版"""
    if not self.test_data:
        messagebox.showwarning("警告", "没有数据可导出\\n\\n请先进行测量以获取数据。")
        return
    
    try:
        # 生成默认文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        default_filename = f"LVER_多参数数据_{timestamp}.csv"
        
        # 打开文件保存对话框 - 使用正确的参数
        filename = filedialog.asksaveasfilename(
            title="导出测量数据",
            defaultextension=".csv",
            filetypes=[
                ("CSV 文件", "*.csv"),
                ("文本文件", "*.txt"),
                ("所有文件", "*.*")
            ],
            initialfile=default_filename  # 修复: 使用 initialfile 而不是 initialname
        )
        
        if not filename:
            self.log_message("用户取消了数据导出")
            return
        
        # 确保文件扩展名
        if not filename.lower().endswith(('.csv', '.txt')):
            filename += '.csv'
        
        # 导出数据
        self.log_message(f"开始导出数据到: {filename}")
        
        # 写入CSV文件
        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['时间', '参数类型', '测量值', '单位'])
            
            for data in self.test_data:
                writer.writerow([data['time'], data['type'], data['value'], data['unit']])
        
        # 生成统计信息
        stats = {
            'total': len(self.test_data),
            'voltage': len([d for d in self.test_data if d['type'] == 'V']),
            'r_sei': len([d for d in self.test_data if d['type'] == 'R_sei']),
            'r_ct': len([d for d in self.test_data if d['type'] == 'R_ct']),
            'r_ohm': len([d for d in self.test_data if d['type'] == 'R_ohm'])
        }
        
        # 显示成功消息
        success_msg = f"""数据导出成功！

文件位置: {filename}

导出统计:
• 总记录数: {stats['total']} 条
• 电压测量: {stats['voltage']} 条
• SEI电阻: {stats['r_sei']} 条  
• CT电阻: {stats['r_ct']} 条
• 欧姆电阻: {stats['r_ohm']} 条

文件可以在Excel中打开查看。"""
        
        messagebox.showinfo("导出成功", success_msg)
        self.log_message(f"✓ 数据导出成功: {filename} ({stats['total']} 条记录)")
        
        # 询问是否打开文件
        if messagebox.askyesno("打开文件", "是否要打开导出的文件？"):
            try:
                import os
                os.startfile(filename)
            except Exception as e:
                self.log_message(f"无法打开文件: {e}")
                
    except PermissionError:
        messagebox.showerror("导出失败", 
                           f"文件访问被拒绝！\\n\\n可能原因：\\n• 文件正在被其他程序使用\\n• 没有写入权限\\n• 文件被锁定\\n\\n请关闭相关程序后重试。")
        self.log_message("❌ 导出失败: 文件访问被拒绝")
        
    except Exception as e:
        error_msg = f"导出过程中发生错误：\\n\\n{str(e)}\\n\\n请检查：\\n• 文件路径是否有效\\n• 磁盘空间是否充足\\n• 是否有写入权限"
        messagebox.showerror("导出失败", error_msg)
        self.log_message(f"❌ 导出失败: {e}")
'''
    
    print("修复后的导出函数代码:")
    print("=" * 60)
    print(fixed_code)
    print("=" * 60)
    
    return fixed_code

def main():
    print("=" * 60)
    print("导出功能修复工具")
    print("=" * 60)
    print("问题: 文件对话框参数错误 'bad option \"-initialname\"'")
    print("解决: 将 initialname 改为 initialfile")
    print("=" * 60)
    
    # 显示修复代码
    create_fixed_export_function()
    
    print("\\n现在测试修复后的导出功能...")
    test_export_dialog()

if __name__ == "__main__":
    main()
