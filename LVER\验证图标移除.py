#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证图标移除脚本
检查main_fixed_complete.py中的Excel导出菜单项是否还包含图标
"""

def check_excel_export_icons():
    """检查Excel导出相关的图标"""
    print("=" * 60)
    print("🔍 验证Excel导出图标移除状态")
    print("=" * 60)
    
    try:
        with open('main_fixed_complete.py', 'r', encoding='utf-8') as f:
            content = f.read()
            lines = content.split('\n')
        
        print("📋 检查导出菜单相关代码:")
        print()
        
        # 查找导出菜单相关行
        for i, line in enumerate(lines, 1):
            if 'export_menu.add_command' in line and 'Excel' in line:
                print(f"第{i}行: {line.strip()}")
                
                # 检查是否包含图标
                if '📊' in line:
                    print("  ❌ 发现📊图标")
                elif '🪶' in line:
                    print("  ❌ 发现🪶羽毛图标")
                else:
                    print("  ✅ 无图标，文本简洁")
                print()
        
        # 统计检查
        excel_lines = [line for line in lines if 'export_menu.add_command' in line and 'Excel' in line]
        icon_lines = [line for line in excel_lines if '📊' in line or '🪶' in line]
        
        print("📊 统计结果:")
        print(f"  • Excel导出菜单项总数: {len(excel_lines)}")
        print(f"  • 包含图标的菜单项: {len(icon_lines)}")
        print(f"  • 无图标的菜单项: {len(excel_lines) - len(icon_lines)}")
        print()
        
        if len(icon_lines) == 0:
            print("🎉 验证通过: 所有Excel导出菜单项都已移除图标!")
        else:
            print("⚠️  警告: 仍有Excel导出菜单项包含图标")
            for line in icon_lines:
                print(f"    {line.strip()}")
        
        print("=" * 60)
        
        # 显示当前的导出菜单代码
        print("📄 当前导出菜单代码:")
        print()
        for i, line in enumerate(lines, 1):
            if 'export_menu.add_command' in line:
                print(f"第{i}行: {line.strip()}")
        
        print("=" * 60)
        
    except FileNotFoundError:
        print("❌ 错误: 找不到main_fixed_complete.py文件")
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    check_excel_export_icons()
