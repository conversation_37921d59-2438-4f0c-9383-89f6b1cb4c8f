@echo off
echo LVER 串口通信应用程序
echo ========================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7+
    pause
    exit /b 1
)

REM 检查依赖项
echo 检查依赖项...
pip show pyserial >nul 2>&1
if errorlevel 1 (
    echo 安装依赖项...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖项安装失败
        pause
        exit /b 1
    )
)

REM 运行测试
echo 运行测试...
python test_app.py
if errorlevel 1 (
    echo 测试失败，请检查错误信息
    pause
    exit /b 1
)

REM 启动应用程序
echo 启动应用程序...
python main.py

pause
