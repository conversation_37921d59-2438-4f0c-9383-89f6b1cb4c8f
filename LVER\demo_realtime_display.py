#!/usr/bin/env python3
"""
实时显示组件演示程序
展示新的专业数值显示界面效果
"""

import tkinter as tk
from tkinter import ttk
import random
import time
from datetime import datetime
from realtime_display import RealtimeDisplayWidget
from data_manager import DataManager


class RealtimeDisplayDemo:
    """实时显示演示程序"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("LVER 实时数值显示演示")
        self.root.geometry("800x600")
        
        # 创建数据管理器
        self.data_manager = DataManager()
        
        # 创建主框架
        main_frame = ttk.Frame(root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建控制区域
        control_frame = ttk.LabelFrame(main_frame, text="演示控制", padding="10")
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 控制按钮
        ttk.But<PERSON>(
            control_frame, text="开始模拟数据",
            command=self.start_simulation
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            control_frame, text="停止模拟",
            command=self.stop_simulation
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            control_frame, text="清空数据",
            command=self.clear_data
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(
            control_frame, text="单次更新",
            command=self.single_update
        ).pack(side=tk.LEFT, padx=(0, 5))
        
        # 创建实时显示组件
        self.realtime_display = RealtimeDisplayWidget(main_frame, self.data_manager)
        self.realtime_display.pack(fill=tk.BOTH, expand=True)
        
        # 模拟数据相关
        self.simulation_running = False
        self.simulation_job = None
        self.test_count = 0
        
        # 基础数值（模拟真实设备的基准值）
        self.base_voltage = 5075  # mV
        self.base_r_ohm = 101.260  # μΩ
        self.base_r_sei = 234.567  # μΩ
        self.base_r_ct = 345.678  # μΩ
    
    def start_simulation(self):
        """开始模拟数据更新"""
        if not self.simulation_running:
            self.simulation_running = True
            self.simulate_data_update()
    
    def stop_simulation(self):
        """停止模拟数据更新"""
        self.simulation_running = False
        if self.simulation_job:
            self.root.after_cancel(self.simulation_job)
            self.simulation_job = None
    
    def simulate_data_update(self):
        """模拟数据更新"""
        if self.simulation_running:
            # 生成模拟数据（在基础值附近波动）
            voltage = self.base_voltage + random.uniform(-50, 50)  # ±50mV波动
            r_ohm = self.base_r_ohm + random.uniform(-0.1, 0.1)    # ±0.1μΩ波动
            r_sei = self.base_r_sei + random.uniform(-5, 5)        # ±5μΩ波动
            r_ct = self.base_r_ct + random.uniform(-10, 10)        # ±10μΩ波动
            
            self.test_count += 1
            
            # 更新显示
            self.realtime_display.update_data(
                voltage_mv=voltage,
                r_ohm=r_ohm,
                r_sei=r_sei,
                r_ct=r_ct,
                test_count=self.test_count
            )
            
            # 随机间隔（0.5-3秒）模拟真实测试
            interval = random.randint(500, 3000)
            self.simulation_job = self.root.after(interval, self.simulate_data_update)
    
    def single_update(self):
        """单次数据更新"""
        # 生成一组模拟数据
        voltage = self.base_voltage + random.uniform(-30, 30)
        r_ohm = self.base_r_ohm + random.uniform(-0.05, 0.05)
        r_sei = self.base_r_sei + random.uniform(-3, 3)
        r_ct = self.base_r_ct + random.uniform(-8, 8)
        
        self.test_count += 1
        
        # 更新显示
        self.realtime_display.update_data(
            voltage_mv=voltage,
            r_ohm=r_ohm,
            r_sei=r_sei,
            r_ct=r_ct,
            test_count=self.test_count
        )
    
    def clear_data(self):
        """清空数据"""
        self.stop_simulation()
        self.test_count = 0
        self.realtime_display.clear_data()


def main():
    """主函数"""
    print("LVER 实时显示组件演示程序")
    print("=" * 40)
    print("功能演示:")
    print("1. 专业的大字体数值显示")
    print("2. 颜色区分不同类型数据")
    print("3. 实时数据更新动画")
    print("4. 趋势指示器（上升/下降箭头）")
    print("5. 数据有效性指示")
    print("6. 更新频率统计")
    print("=" * 40)
    
    root = tk.Tk()
    
    # 设置窗口图标和样式
    try:
        # 设置窗口样式
        style = ttk.Style()
        style.theme_use('clam')  # 使用现代主题
    except:
        pass
    
    # 创建演示应用
    app = RealtimeDisplayDemo(root)
    
    # 启动主循环
    root.mainloop()


if __name__ == "__main__":
    main()
