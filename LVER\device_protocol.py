"""
设备命令协议模块
定义和解析设备通信协议
"""

from typing import Dict, List, Optional, Tuple
import re


class DeviceProtocol:
    """设备协议处理类"""
    
    # 命令常量
    TEST_COMMAND = 0xAA
    RESET_COMMAND = 0xA0
    VOLTAGE_COMMAND = 0x55
    
    def __init__(self):
        pass
    
    @staticmethod
    def parse_test_response(responses: List[str]) -> Optional[Dict[str, float]]:
        """
        解析测试命令响应
        
        Args:
            responses: 四行响应数据
            
        Returns:
            Dict: 解析后的数据字典，包含voltage, r_ohm, r_sei, r_ct
        """
        if not responses or len(responses) != 4:
            return None
            
        try:
            result = {}
            
            # 解析第一行: "1,V,{voltage_value}"
            voltage_line = responses[0].strip()
            voltage_match = DeviceProtocol._parse_csv_line(voltage_line, "V")
            if voltage_match:
                result['voltage'] = voltage_match
            
            # 解析第二行: "1,R_ohm,{resistance_ohm_value}"
            r_ohm_line = responses[1].strip()
            r_ohm_match = DeviceProtocol._parse_csv_line(r_ohm_line, "R_ohm")
            if r_ohm_match:
                result['r_ohm'] = r_ohm_match
            
            # 解析第三行: "1,R_sei,{resistance_sei_value}"
            r_sei_line = responses[2].strip()
            r_sei_match = DeviceProtocol._parse_csv_line(r_sei_line, "R_sei")
            if r_sei_match:
                result['r_sei'] = r_sei_match
            
            # 解析第四行: "1,R_ct,{resistance_ct_value}"
            r_ct_line = responses[3].strip()
            r_ct_match = DeviceProtocol._parse_csv_line(r_ct_line, "R_ct")
            if r_ct_match:
                result['r_ct'] = r_ct_match
            
            return result if len(result) == 4 else None
            
        except Exception as e:
            print(f"解析测试响应失败: {e}")
            return None
    
    @staticmethod
    def parse_voltage_response(response: str) -> Optional[float]:
        """
        解析电压测量响应
        
        Args:
            response: 响应字符串 "1,B2_Voltage,{voltage_value}"
            
        Returns:
            float: 电压值，如果解析失败返回None
        """
        if not response:
            return None
            
        try:
            return DeviceProtocol._parse_csv_line(response.strip(), "B2_Voltage")
        except Exception as e:
            print(f"解析电压响应失败: {e}")
            return None
    
    @staticmethod
    def _parse_csv_line(line: str, expected_type: str) -> Optional[float]:
        """
        解析CSV格式的行
        
        Args:
            line: CSV行字符串
            expected_type: 期望的类型字段
            
        Returns:
            float: 解析出的数值
        """
        try:
            parts = line.split(',')
            if len(parts) != 3:
                return None
                
            # 检查格式: "1,{type},{value}"
            if parts[0].strip() != "1":
                return None
                
            if parts[1].strip() != expected_type:
                return None
                
            value = float(parts[2].strip())
            return value
            
        except (ValueError, IndexError) as e:
            print(f"解析CSV行失败: {line}, 错误: {e}")
            return None
    
    @staticmethod
    def format_test_results(data: Dict[str, float]) -> str:
        """
        格式化测试结果为可读字符串
        
        Args:
            data: 测试结果数据
            
        Returns:
            str: 格式化后的字符串
        """
        if not data:
            return "无有效数据"
            
        result_lines = []
        
        if 'voltage' in data:
            # 电压值转换：假设原始值是毫伏(mV)，转换为伏特(V)
            voltage_v = data['voltage'] / 1000.0
            result_lines.append(f"电压 (V): {voltage_v:.3f} V ({data['voltage']:.0f} mV)")

        if 'r_ohm' in data:
            # 电阻值单位：微欧(μΩ)
            result_lines.append(f"电阻 (R_ohm): {data['r_ohm']:.3f} μΩ")

        if 'r_sei' in data:
            # SEI电阻值单位：微欧(μΩ)
            result_lines.append(f"SEI电阻 (R_sei): {data['r_sei']:.3f} μΩ")

        if 'r_ct' in data:
            # CT电阻值单位：微欧(μΩ)
            result_lines.append(f"CT电阻 (R_ct): {data['r_ct']:.3f} μΩ")
        
        return "\n".join(result_lines)
    
    @staticmethod
    def format_voltage_result(voltage: float) -> str:
        """
        格式化电压结果为可读字符串

        Args:
            voltage: 电压值

        Returns:
            str: 格式化后的字符串
        """
        # 电压值转换：假设原始值是毫伏(mV)，转换为伏特(V)
        voltage_v = voltage / 1000.0
        return f"B2电压: {voltage_v:.3f} V ({voltage:.0f} mV)"
    
    @staticmethod
    def validate_response_format(response: str, command_type: str) -> bool:
        """
        验证响应格式是否正确
        
        Args:
            response: 响应字符串
            command_type: 命令类型 ('test', 'voltage')
            
        Returns:
            bool: 格式是否正确
        """
        if not response:
            return False
            
        try:
            if command_type == 'test':
                lines = response.strip().split('\n')
                if len(lines) != 4:
                    return False
                    
                expected_types = ['V', 'R_ohm', 'R_sei', 'R_ct']
                for i, line in enumerate(lines):
                    if not DeviceProtocol._validate_csv_line(line, expected_types[i]):
                        return False
                return True
                
            elif command_type == 'voltage':
                return DeviceProtocol._validate_csv_line(response, 'B2_Voltage')
                
        except Exception:
            return False
            
        return False
    
    @staticmethod
    def _validate_csv_line(line: str, expected_type: str) -> bool:
        """验证CSV行格式"""
        try:
            parts = line.strip().split(',')
            if len(parts) != 3:
                return False
                
            # 检查第一部分是否为"1"
            if parts[0].strip() != "1":
                return False
                
            # 检查第二部分是否为期望类型
            if parts[1].strip() != expected_type:
                return False
                
            # 检查第三部分是否为有效数字
            float(parts[2].strip())
            return True
            
        except (ValueError, IndexError):
            return False
