#!/usr/bin/env python3
"""
LVER 串口通信应用程序 - 最终纯净版本
✅ 无任何模拟数据
✅ 仅支持真实串口连接
✅ 完整图表功能
✅ 增大图表区域
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查必要的依赖项"""
    missing_deps = []
    
    try:
        import serial
        import serial.tools.list_ports
        print("✓ pyserial 可用")
    except ImportError:
        missing_deps.append("pyserial")
        print("✗ pyserial 未安装")
    
    try:
        import matplotlib
        print("✓ matplotlib 可用")
    except ImportError:
        missing_deps.append("matplotlib")
        print("✗ matplotlib 未安装 (图表功能将受限)")
    
    if missing_deps:
        print(f"\n缺少依赖: {', '.join(missing_deps)}")
        print("安装命令:")
        for dep in missing_deps:
            print(f"  pip install {dep}")
        
        # 如果只是缺少matplotlib，可以继续运行
        if missing_deps == ["matplotlib"]:
            print("\n可以继续运行，但图表功能受限")
            return True
        else:
            return False
    
    return True

def main():
    """主函数"""
    print("="*60)
    print("LVER 串口通信应用程序 - 最终纯净版")
    print("="*60)
    print("特性：")
    print("✅ 无任何模拟数据")
    print("✅ 仅真实串口连接")
    print("✅ 完整图表功能")
    print("✅ 增大图表区域")
    print("✅ 高清导出功能")
    print("="*60)
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    try:
        # 导入GUI模块
        from gui import LVERApp
        print("✓ GUI模块导入成功")
        
        # 创建主窗口
        root = tk.Tk()
        root.title("LVER 串口通信工具 - 最终版")
        root.geometry("1400x1000")
        
        # 设置窗口图标（如果存在）
        try:
            root.iconbitmap("icon.ico")
        except:
            pass
        
        print("✓ 主窗口创建成功")
        
        # 创建应用程序实例
        app = LVERApp(root)
        print("✓ 应用程序实例创建成功")
        
        # 验证并确保数据管理器是空的
        initial_data_count = len(app.data_manager.get_all_data())
        if initial_data_count > 0:
            print(f"⚠️  发现 {initial_data_count} 条模拟数据，正在清除...")
            app.data_manager.test_data.clear()
            print("✓ 模拟数据已清除")
        else:
            print("✓ 数据管理器为空（无模拟数据）")
        
        # 延迟启动串口监控
        def delayed_setup():
            try:
                app.setup_port_monitoring()
                print("✓ 串口监控已启动")
                
                # 刷新串口列表
                app.refresh_ports()
                print("✓ 串口列表已刷新")
                
                # 更新图表显示（应该显示"暂无数据"）
                if hasattr(app, 'chart_widget') and app.chart_widget:
                    app.chart_widget.update_chart()
                    print("✓ 图表已更新（应显示'暂无数据'）")
                
            except Exception as e:
                print(f"⚠️  串口监控启动失败: {e}")
        
        # 在主循环开始后启动
        root.after(1000, delayed_setup)
        
        print("\n" + "="*60)
        print("应用程序启动成功！")
        print("\n使用说明：")
        print("1. 连接您的串口设备")
        print("2. 在'串口选择'中选择正确的串口")
        print("3. 选择合适的波特率（默认115200）")
        print("4. 点击'连接'建立串口连接")
        print("5. 使用测试命令获取数据")
        print("6. 查看实时图表和数据统计")
        print("7. 使用导出功能保存数据")
        print("="*60)
        
        # 启动主循环
        root.mainloop()
        
    except ImportError as e:
        error_msg = f"模块导入失败: {e}\n\n请确保所有依赖都已正确安装。"
        print(f"❌ {error_msg}")
        messagebox.showerror("导入错误", error_msg)
        
    except Exception as e:
        error_msg = f"应用程序启动失败: {e}"
        print(f"❌ {error_msg}")
        import traceback
        traceback.print_exc()
        messagebox.showerror("启动错误", error_msg)

if __name__ == "__main__":
    main()
