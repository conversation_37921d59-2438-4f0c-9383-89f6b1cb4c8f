"""
数据管理模块
处理测试数据的存储、管理和导出
"""

import csv
import os
from datetime import datetime
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from error_handler import global_error_handler


@dataclass
class TestData:
    """测试数据结构"""
    timestamp: datetime
    voltage: float  # 电压 (mV)
    r_ohm: float   # 电阻 (μΩ)
    r_sei: float   # SEI电阻 (μΩ)
    r_ct: float    # CT电阻 (μΩ)
    test_number: int  # 测试序号


class DataManager:
    """数据管理器"""
    
    def __init__(self):
        self.test_data: List[TestData] = []
        self.error_handler = global_error_handler
        self.max_data_points = 1000  # 最大数据点数
        
    def add_test_data(self, voltage: float, r_ohm: float, r_sei: float, r_ct: float) -> TestData:
        """
        添加测试数据
        
        Args:
            voltage: 电压值 (mV)
            r_ohm: 电阻值 (μΩ)
            r_sei: SEI电阻值 (μΩ)
            r_ct: CT电阻值 (μΩ)
            
        Returns:
            TestData: 添加的数据对象
        """
        test_number = len(self.test_data) + 1
        data = TestData(
            timestamp=datetime.now(),
            voltage=voltage,
            r_ohm=r_ohm,
            r_sei=r_sei,
            r_ct=r_ct,
            test_number=test_number
        )
        
        self.test_data.append(data)
        
        # 限制数据点数量
        if len(self.test_data) > self.max_data_points:
            self.test_data.pop(0)
            # 重新编号
            for i, item in enumerate(self.test_data):
                item.test_number = i + 1
        
        self.error_handler.log_debug(f"添加测试数据: 测试#{test_number}, 电压={voltage}mV")
        return data
    
    def get_latest_data(self) -> Optional[TestData]:
        """获取最新的测试数据"""
        return self.test_data[-1] if self.test_data else None
    
    def get_all_data(self) -> List[TestData]:
        """获取所有测试数据"""
        return self.test_data.copy()
    
    def get_data_for_chart(self, data_type: str) -> tuple:
        """
        获取用于图表显示的数据
        
        Args:
            data_type: 数据类型 ('voltage', 'r_ohm', 'r_sei', 'r_ct')
            
        Returns:
            tuple: (x_data, y_data, label)
        """
        if not self.test_data:
            return [], [], ""
        
        x_data = [data.test_number for data in self.test_data]
        
        if data_type == 'voltage':
            y_data = [data.voltage / 1000.0 for data in self.test_data]  # 转换为V
            label = "电压 (V)"
        elif data_type == 'r_ohm':
            y_data = [data.r_ohm for data in self.test_data]
            label = "电阻 (μΩ)"
        elif data_type == 'r_sei':
            y_data = [data.r_sei for data in self.test_data]
            label = "SEI电阻 (μΩ)"
        elif data_type == 'r_ct':
            y_data = [data.r_ct for data in self.test_data]
            label = "CT电阻 (μΩ)"
        else:
            return [], [], ""
        
        return x_data, y_data, label
    
    def get_statistics(self) -> Dict[str, Any]:
        """
        获取数据统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        if not self.test_data:
            return {
                'total_tests': 0,
                'voltage_avg': 0,
                'voltage_min': 0,
                'voltage_max': 0,
                'r_ohm_avg': 0,
                'r_sei_avg': 0,
                'r_ct_avg': 0
            }
        
        voltages = [data.voltage for data in self.test_data]
        r_ohms = [data.r_ohm for data in self.test_data]
        r_seis = [data.r_sei for data in self.test_data]
        r_cts = [data.r_ct for data in self.test_data]
        
        return {
            'total_tests': len(self.test_data),
            'voltage_avg': sum(voltages) / len(voltages),
            'voltage_min': min(voltages),
            'voltage_max': max(voltages),
            'r_ohm_avg': sum(r_ohms) / len(r_ohms),
            'r_sei_avg': sum(r_seis) / len(r_seis),
            'r_ct_avg': sum(r_cts) / len(r_cts)
        }
    
    def export_to_csv(self, filename: Optional[str] = None) -> str:
        """
        导出数据到CSV文件
        
        Args:
            filename: 文件名，如果为None则自动生成
            
        Returns:
            str: 导出的文件路径
        """
        if not self.test_data:
            raise ValueError("没有数据可导出")
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"LVER_测试数据_{timestamp}.csv"
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                
                # 写入表头
                writer.writerow([
                    '测试序号',
                    '时间戳',
                    '电压(mV)',
                    '电压(V)',
                    '电阻(μΩ)',
                    'SEI电阻(μΩ)',
                    'CT电阻(μΩ)'
                ])
                
                # 写入数据
                for data in self.test_data:
                    writer.writerow([
                        data.test_number,
                        data.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                        f"{data.voltage:.0f}",
                        f"{data.voltage / 1000.0:.3f}",
                        f"{data.r_ohm:.3f}",
                        f"{data.r_sei:.3f}",
                        f"{data.r_ct:.3f}"
                    ])
            
            self.error_handler.log_info(f"数据已导出到: {filename}")
            return os.path.abspath(filename)
            
        except Exception as e:
            self.error_handler.handle_error("数据导出", f"导出失败: {e}")
            raise
    
    def clear_data(self):
        """清空所有数据"""
        self.test_data.clear()
        self.error_handler.log_info("测试数据已清空")
    
    def get_data_count(self) -> int:
        """获取数据点数量"""
        return len(self.test_data)


class ContinuousTestManager:
    """连续测试管理器"""
    
    def __init__(self, data_manager: DataManager):
        self.data_manager = data_manager
        self.is_running = False
        self.test_interval = 2.0  # 默认2秒间隔
        self.test_thread = None
        self.test_callback = None
        self.error_handler = global_error_handler
        
    def start_continuous_test(self, test_callback, interval: float = 2.0):
        """
        开始连续测试
        
        Args:
            test_callback: 执行单次测试的回调函数
            interval: 测试间隔（秒）
        """
        if self.is_running:
            return
        
        self.test_callback = test_callback
        self.test_interval = interval
        self.is_running = True
        
        import threading
        self.test_thread = threading.Thread(target=self._test_worker, daemon=True)
        self.test_thread.start()
        
        self.error_handler.log_info(f"开始连续测试，间隔: {interval}秒")
    
    def stop_continuous_test(self):
        """停止连续测试"""
        self.is_running = False
        if self.test_thread and self.test_thread.is_alive():
            self.test_thread.join(timeout=2)
        
        self.error_handler.log_info("连续测试已停止")
    
    def _test_worker(self):
        """测试工作线程"""
        import time
        
        while self.is_running:
            try:
                if self.test_callback:
                    self.test_callback()
                
                # 等待指定间隔
                start_time = time.time()
                while self.is_running and (time.time() - start_time) < self.test_interval:
                    time.sleep(0.1)
                    
            except Exception as e:
                self.error_handler.handle_error("连续测试", f"测试执行错误: {e}")
                time.sleep(1)  # 出错时短暂等待
    
    def set_interval(self, interval: float):
        """设置测试间隔"""
        self.test_interval = max(0.5, interval)  # 最小0.5秒
        self.error_handler.log_debug(f"测试间隔设置为: {self.test_interval}秒")
    
    def get_status(self) -> Dict[str, Any]:
        """获取测试状态"""
        return {
            'is_running': self.is_running,
            'interval': self.test_interval,
            'total_tests': self.data_manager.get_data_count()
        }
