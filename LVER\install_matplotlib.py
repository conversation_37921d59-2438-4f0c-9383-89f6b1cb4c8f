#!/usr/bin/env python3
"""
安装matplotlib依赖的脚本
"""

import sys
import os
import subprocess
import importlib.util

def find_python_with_serial():
    """查找安装了pyserial的Python解释器"""
    python_candidates = [
        sys.executable,  # 当前Python
        "python",
        "python3",
        "py -3",
        r"C:\Program Files\Python38\python.exe",
        r"C:\Program Files\Python39\python.exe",
        r"C:\Program Files\Python310\python.exe",
        r"C:\Program Files\Python311\python.exe",
        r"C:\Program Files\Python312\python.exe",
        r"C:\Program Files\Python313\python.exe",
        r"C:\Python38\python.exe",
        r"C:\Python39\python.exe",
        r"C:\Python310\python.exe",
        r"C:\Python311\python.exe",
        r"C:\Python312\python.exe",
        r"C:\Python313\python.exe",
    ]
    
    # 添加用户目录下的Python
    user_profile = os.environ.get('USERPROFILE', '')
    if user_profile:
        for version in ['38', '39', '310', '311', '312', '313']:
            python_candidates.extend([
                os.path.join(user_profile, f'AppData\\Local\\Programs\\Python\\Python{version}\\python.exe'),
                os.path.join(user_profile, f'AppData\\Local\\Microsoft\\WindowsApps\\python{version[0]}.{version[1:]}.exe'),
            ])
    
    print("正在查找可用的Python解释器...")
    
    for python_cmd in python_candidates:
        try:
            # 检查Python是否存在
            result = subprocess.run([python_cmd, "--version"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"找到Python: {python_cmd} ({version})")
                
                # 检查是否有pyserial
                result = subprocess.run([python_cmd, "-c", "import serial; print('pyserial available')"], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    print(f"✓ {python_cmd} 有pyserial支持")
                    return python_cmd
                else:
                    print(f"✗ {python_cmd} 没有pyserial")
                    
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            continue
    
    return None

def install_matplotlib(python_cmd):
    """安装matplotlib"""
    print(f"\n正在使用 {python_cmd} 安装matplotlib...")
    
    try:
        # 首先检查是否已经安装
        result = subprocess.run([python_cmd, "-c", "import matplotlib; print('matplotlib already installed')"], 
                              capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            print("matplotlib 已经安装")
            return True
        
        # 安装matplotlib
        print("开始安装matplotlib...")
        result = subprocess.run([python_cmd, "-m", "pip", "install", "matplotlib"], 
                              capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("matplotlib 安装成功!")
            print(result.stdout)
            return True
        else:
            print(f"matplotlib 安装失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("安装超时")
        return False
    except Exception as e:
        print(f"安装过程中出错: {e}")
        return False

def main():
    """主函数"""
    print("LVER matplotlib 依赖安装工具")
    print("=" * 40)
    
    # 查找合适的Python解释器
    python_cmd = find_python_with_serial()
    
    if not python_cmd:
        print("\n❌ 错误: 未找到安装了pyserial的Python解释器")
        print("请先安装Python和pyserial")
        input("按回车键退出...")
        return False
    
    print(f"\n使用Python解释器: {python_cmd}")
    
    # 安装matplotlib
    if install_matplotlib(python_cmd):
        print("\n✅ matplotlib 安装完成!")
        print("现在可以运行带图表功能的LVER应用程序了")
        
        # 测试导入
        try:
            result = subprocess.run([python_cmd, "-c", 
                                   "import matplotlib; import matplotlib.pyplot; print('matplotlib 测试成功')"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ matplotlib 功能测试通过")
            else:
                print("⚠️ matplotlib 导入测试失败，但安装可能成功")
        except:
            print("⚠️ 无法测试matplotlib功能")
        
    else:
        print("\n❌ matplotlib 安装失败")
        print("可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 尝试手动运行: pip install matplotlib")
        print("3. 使用管理员权限运行此脚本")
    
    input("\n按回车键退出...")
    return True

if __name__ == "__main__":
    main()
