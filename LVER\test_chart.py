#!/usr/bin/env python3
"""
测试图表功能的独立脚本
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 模拟serial模块
class MockSerial:
    def __init__(self, *args, **kwargs):
        pass
    def close(self):
        pass
    def write(self, data):
        return len(data)
    def read(self, size):
        return b''
    def readline(self):
        return b''

class MockListPorts:
    @staticmethod
    def comports():
        return []

# 创建模拟模块
import types
serial_module = types.ModuleType('serial')
serial_module.Serial = MockSerial
serial_module.SerialException = Exception
serial_module.tools = types.ModuleType('tools')
serial_module.tools.list_ports = MockListPorts

# 将模拟模块添加到sys.modules
sys.modules['serial'] = serial_module
sys.modules['serial.tools'] = serial_module.tools
sys.modules['serial.tools.list_ports'] = MockListPorts

# 现在导入我们的模块
from data_manager import DataManager, TestData
from chart_widget import ChartWidget
from datetime import datetime

def create_test_data(data_manager):
    """创建一些测试数据"""
    test_data = [
        TestData(1, 3295, 514, 308, 104),
        TestData(2, 3290, 527, 295, 129),
        TestData(3, 3285, 535, 283, 112),
        TestData(4, 3280, 522, 291, 108),
        TestData(5, 3275, 531, 300, 115),
        TestData(6, 3270, 519, 285, 98),
        TestData(7, 3265, 542, 276, 125),
        TestData(8, 3260, 528, 289, 103),
        TestData(9, 3255, 536, 294, 118),
        TestData(10, 3250, 524, 281, 107),
    ]
    
    for data in test_data:
        data_manager.add_test_data(data)

def main():
    """主函数"""
    try:
        # 创建主窗口
        root = tk.Tk()
        root.title("LVER 图表功能测试")
        root.geometry("1200x800")
        
        # 创建数据管理器
        data_manager = DataManager()
        
        # 创建测试数据
        create_test_data(data_manager)
        
        # 创建主框架
        main_frame = ttk.Frame(root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建标题
        title_label = ttk.Label(
            main_frame, 
            text="LVER 图表功能测试 - 增强版图表组件",
            font=("Arial", 14, "bold")
        )
        title_label.pack(pady=(0, 10))
        
        # 创建图表组件
        chart_widget = ChartWidget(main_frame, data_manager)
        chart_widget.pack(fill=tk.BOTH, expand=True)
        
        # 创建控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        def add_random_data():
            """添加随机测试数据"""
            import random
            last_test = data_manager.get_latest_data()
            test_number = last_test.test_number + 1 if last_test else 1
            
            # 生成随机数据
            voltage = random.randint(3200, 3350)
            r_ohm = random.randint(500, 600)
            r_sei = random.randint(250, 350)
            r_ct = random.randint(80, 150)
            
            new_data = TestData(test_number, voltage, r_ohm, r_sei, r_ct)
            data_manager.add_test_data(new_data)
            chart_widget.update_chart()
        
        def clear_data():
            """清空数据"""
            data_manager.clear_data()
            chart_widget.update_chart()
        
        def reset_test_data():
            """重置为测试数据"""
            data_manager.clear_data()
            create_test_data(data_manager)
            chart_widget.update_chart()
        
        # 添加控制按钮
        ttk.Button(button_frame, text="添加随机数据", command=add_random_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="清空数据", command=clear_data).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="重置测试数据", command=reset_test_data).pack(side=tk.LEFT, padx=(0, 5))
        
        # 添加说明文本
        info_label = ttk.Label(
            button_frame,
            text="测试图表导出功能：点击图表区域的'导出PNG'或'导出JPG'按钮",
            font=("Arial", 9),
            foreground="blue"
        )
        info_label.pack(side=tk.RIGHT)
        
        print("图表测试应用程序已启动")
        print("功能说明：")
        print("1. 图表显示区域已增大 (14x8英寸，120 DPI)")
        print("2. 支持PNG/JPG高分辨率导出 (300 DPI)")
        print("3. 导出图片分辨率至少为1920x1080像素")
        print("4. 可以使用控制按钮测试数据更新")
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        print(f"测试应用程序启动失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
