#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据准确性修复效果
验证串口数据解析和稳定性处理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from datetime import datetime
import time

# 模拟串口数据处理器测试
class MockSerialConnection:
    """模拟串口连接，用于测试数据解析功能"""
    
    def __init__(self):
        self.is_open = True
        self.test_responses = [
            # 测试命令响应 (0xAA)
            "Start Voltage : 5075\nHF_current:0 mA zeroPoint:21\n1,V,5075\n2,R_sei,281\n3,R_ct,165\n4,R_ohm,502\n",
            
            # 电压命令响应 (0x55)
            "Start Voltage : 3290\n1,V,3290\n",
            
            # 带噪声的响应
            "HF_current:0 mA zeroPoint:21\n1,V,3295\n2,R_sei,275\n",
            
            # 异常数据测试
            "1,V,3285\n2,R_sei,285\n3,R_ct,160\n4,R_ohm,510\n",
            
            # 边界值测试
            "1,V,3300\n2,R_sei,290\n3,R_ct,170\n4,R_ohm,520\n"
        ]
        self.response_index = 0
        self.in_waiting = 0
        self.current_response = ""
        
    def write(self, data):
        """模拟写入命令"""
        print(f"[MOCK] 发送命令: {data.hex()}")
        
        # 根据命令设置响应
        if data == b'\xAA':  # 测试命令
            self.current_response = self.test_responses[0]
        elif data == b'\x55':  # 电压命令
            self.current_response = self.test_responses[1]
        else:
            self.current_response = ""
        
        self.in_waiting = len(self.current_response.encode('utf-8'))
        return len(data)
    
    def read(self, size):
        """模拟读取数据"""
        if self.current_response:
            data = self.current_response.encode('utf-8')[:size]
            self.current_response = self.current_response[len(data.decode('utf-8', errors='ignore')):]
            self.in_waiting = len(self.current_response.encode('utf-8'))
            return data
        return b''


def test_data_processor():
    """测试数据处理器功能"""
    print("=== 数据处理器功能测试 ===\n")
    
    # 导入修复后的数据处理器
    try:
        from 数据准确性修复方案 import SerialDataProcessor
        processor = SerialDataProcessor()
        print("✓ 数据处理器导入成功")
    except ImportError as e:
        print(f"✗ 数据处理器导入失败: {e}")
        return False
    
    # 测试数据样例
    test_cases = [
        {
            "name": "标准测试命令响应",
            "data": "Start Voltage : 5075\nHF_current:0 mA zeroPoint:21\n1,V,5075\n2,R_sei,281\n3,R_ct,165\n4,R_ohm,502\n",
            "expected_count": 4
        },
        {
            "name": "电压命令响应",
            "data": "Start Voltage : 3290\n1,V,3290\n",
            "expected_count": 1
        },
        {
            "name": "带噪声数据",
            "data": "HF_current:0 mA zeroPoint:21\n1,V,3295\n2,R_sei,275\n",
            "expected_count": 2
        },
        {
            "name": "无效数据测试",
            "data": "Start Voltage : 5075\nInvalid line\n1,X,999999\n2,V,3290\n",
            "expected_count": 1  # 只有V参数有效
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"--- 测试 {i}: {test_case['name']} ---")
        print(f"输入数据: {repr(test_case['data'])}")
        
        try:
            measurements = processor._parse_response_data(test_case['data'])
            actual_count = len(measurements)
            expected_count = test_case['expected_count']
            
            print(f"解析结果: {actual_count} 个测量值 (期望: {expected_count})")
            
            if actual_count == expected_count:
                print("✓ 测试通过")
                success_count += 1
            else:
                print("✗ 测试失败")
            
            for measurement in measurements:
                param_type = measurement['param_type']
                param_value = measurement['param_value']
                print(f"  {param_type}: {param_value}")
                
        except Exception as e:
            print(f"✗ 测试异常: {e}")
        
        print()
    
    print(f"测试总结: {success_count}/{len(test_cases)} 个测试通过")
    return success_count == len(test_cases)


def test_stability_processing():
    """测试数据稳定性处理功能"""
    print("=== 数据稳定性处理测试 ===\n")
    
    try:
        from 数据准确性修复方案 import SerialDataProcessor
        processor = SerialDataProcessor()
        print("✓ 数据处理器导入成功")
    except ImportError as e:
        print(f"✗ 数据处理器导入失败: {e}")
        return False
    
    # 模拟电压值波动
    voltage_readings = [3290, 3295, 3285, 3300, 3280, 3292, 3288, 3296]
    
    print("原始电压读数:", voltage_readings)
    print("稳定化处理结果:")
    
    stable_values = []
    for i, reading in enumerate(voltage_readings):
        stable_value = processor.get_stable_measurement('V', reading)
        stable_values.append(stable_value)
        print(f"  读数 {i+1}: {reading} mV → {stable_value} mV")
    
    # 计算波动减少效果
    original_range = max(voltage_readings) - min(voltage_readings)
    stable_range = max(stable_values) - min(stable_values)
    
    print(f"\n波动分析:")
    print(f"  原始数据波动范围: {original_range} mV")
    print(f"  稳定化后波动范围: {stable_range} mV")
    print(f"  波动减少: {((original_range - stable_range) / original_range * 100):.1f}%")
    
    return stable_range < original_range


def test_integration():
    """集成测试 - 模拟完整的串口通信流程"""
    print("=== 集成测试 ===\n")
    
    try:
        from 数据准确性修复方案 import FixedSerialCommunication
        comm = FixedSerialCommunication()
        print("✓ 串口通信类导入成功")
    except ImportError as e:
        print(f"✗ 串口通信类导入失败: {e}")
        return False
    
    # 创建模拟串口连接
    mock_serial = MockSerialConnection()
    
    print("1. 测试命令 (0xAA) 测试:")
    try:
        measurements = comm.send_test_command_fixed(mock_serial)
        print(f"   获得 {len(measurements)} 个测量值")
        for measurement in measurements:
            param_type = measurement['param_type']
            param_value = measurement['param_value']
            raw_value = measurement.get('raw_value', param_value)
            print(f"   {param_type}: {raw_value} → {param_value}")
        print("   ✓ 测试命令执行成功")
    except Exception as e:
        print(f"   ✗ 测试命令执行失败: {e}")
        return False
    
    print("\n2. 电压命令 (0x55) 测试:")
    try:
        voltage_measurement = comm.send_voltage_command_fixed(mock_serial)
        if voltage_measurement:
            param_value = voltage_measurement['param_value']
            raw_value = voltage_measurement.get('raw_value', param_value)
            print(f"   电压: {raw_value} → {param_value} mV")
            print("   ✓ 电压命令执行成功")
        else:
            print("   ✗ 未获得电压测量数据")
            return False
    except Exception as e:
        print(f"   ✗ 电压命令执行失败: {e}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("鲸测云LCER电池测试仪 - 数据准确性修复验证")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    test_results = []
    
    # 执行各项测试
    test_results.append(("数据处理器功能", test_data_processor()))
    test_results.append(("数据稳定性处理", test_stability_processing()))
    test_results.append(("集成测试", test_integration()))
    
    # 输出测试总结
    print("=" * 50)
    print("测试总结:")
    
    passed_tests = 0
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n总体结果: {passed_tests}/{len(test_results)} 项测试通过")
    
    if passed_tests == len(test_results):
        print("🎉 所有测试通过！数据准确性修复成功。")
        print("\n修复要点:")
        print("1. ✓ 替换随机数据生成为真实串口数据解析")
        print("2. ✓ 实现调试信息过滤和CSV格式解析")
        print("3. ✓ 添加数据合理性验证")
        print("4. ✓ 实现滑动平均稳定性处理")
        print("5. ✓ 保持向后兼容性（无串口时使用固定参考值）")
    else:
        print("⚠️  部分测试失败，需要进一步检查修复代码。")
    
    return passed_tests == len(test_results)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
