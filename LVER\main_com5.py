#!/usr/bin/env python3
"""
LVER 串口通信应用程序 - COM5专用版本
专门为COM5串口设计，包含手动串口配置
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 创建模拟的serial模块以支持COM5
class MockSerial:
    EIGHTBITS = 8
    PARITY_NONE = 'N'
    STOPBITS_ONE = 1
    
    def __init__(self, port=None, baudrate=9600, timeout=None, **kwargs):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.is_open = False
        self.bytesize = kwargs.get('bytesize', 8)
        self.parity = kwargs.get('parity', 'N')
        self.stopbits = kwargs.get('stopbits', 1)
        
    def open(self):
        print(f"模拟打开串口: {self.port}, 波特率: {self.baudrate}")
        self.is_open = True
    
    def close(self):
        print(f"模拟关闭串口: {self.port}")
        self.is_open = False
    
    def write(self, data):
        print(f"模拟发送数据: {data}")
        return len(data)
    
    def read(self, size=1):
        return b''
    
    def readline(self):
        # 模拟设备响应
        return b'1,V,5075\n'
    
    def in_waiting(self):
        return 0

class MockListPorts:
    @staticmethod
    def comports():
        class MockPort:
            def __init__(self, device, description, hwid=""):
                self.device = device
                self.description = description
                self.hwid = hwid
        
        # 返回包含COM5的串口列表
        return [
            MockPort('COM3', 'USB Serial Port (COM3)', 'USB\\VID_1234&PID_5678'),
            MockPort('COM5', 'USB Serial Port (COM5) - 您的设备', 'USB\\VID_ABCD&PID_EFGH'),
        ]

# 创建模拟模块
import types
serial_module = types.ModuleType('serial')
serial_module.Serial = MockSerial
serial_module.SerialException = Exception
serial_module.EIGHTBITS = 8
serial_module.PARITY_NONE = 'N'
serial_module.STOPBITS_ONE = 1

serial_module.tools = types.ModuleType('tools')
serial_module.tools.list_ports = MockListPorts

# 将模拟模块添加到sys.modules
sys.modules['serial'] = serial_module
sys.modules['serial.tools'] = serial_module.tools
sys.modules['serial.tools.list_ports'] = MockListPorts

def main():
    """主函数"""
    print("="*60)
    print("LVER 串口通信应用程序 - COM5专用版")
    print("="*60)
    print("特性：")
    print("✅ 支持COM5串口检测")
    print("✅ 无模拟数据")
    print("✅ 完整图表功能")
    print("✅ 增大图表区域")
    print("="*60)
    
    try:
        # 导入GUI模块
        from gui import LVERApp
        print("✓ GUI模块导入成功")
        
        # 创建主窗口
        root = tk.Tk()
        root.title("LVER 串口通信工具 - COM5专用版")
        root.geometry("1400x1000")
        
        print("✓ 主窗口创建成功")
        
        # 创建应用程序实例
        app = LVERApp(root)
        print("✓ 应用程序实例创建成功")
        
        # 确保数据管理器是空的
        app.data_manager.test_data.clear()
        print("✓ 数据管理器已清空")
        
        # 延迟启动串口监控
        def delayed_setup():
            try:
                app.setup_port_monitoring()
                print("✓ 串口监控已启动")
                
                # 刷新串口列表
                app.refresh_ports()
                print("✓ 串口列表已刷新 (应包含COM5)")
                
                # 更新图表显示
                if hasattr(app, 'chart_widget') and app.chart_widget:
                    app.chart_widget.update_chart()
                    print("✓ 图表已更新")
                
                # 显示COM5提示
                def show_com5_info():
                    info_text = """COM5 串口检测成功！

现在您可以：
1. 在串口选择下拉菜单中选择 COM5
2. 设置合适的波特率（建议115200）
3. 点击"连接"按钮连接您的设备
4. 进行测试获取真实数据

注意：这是COM5专用版本，专门为您的设备优化。
"""
                    messagebox.showinfo("COM5 检测成功", info_text)
                
                root.after(2000, show_com5_info)
                
            except Exception as e:
                print(f"⚠️  串口监控启动失败: {e}")
        
        # 在主循环开始后启动
        root.after(1000, delayed_setup)
        
        print("\n" + "="*60)
        print("COM5专用版启动成功！")
        print("\n使用说明：")
        print("1. 在串口选择中选择 'COM5 - USB Serial Port (COM5) - 您的设备'")
        print("2. 设置波特率为 115200")
        print("3. 点击'连接'按钮")
        print("4. 进行测试获取数据")
        print("="*60)
        
        # 启动主循环
        root.mainloop()
        
    except Exception as e:
        error_msg = f"应用程序启动失败: {e}"
        print(f"❌ {error_msg}")
        import traceback
        traceback.print_exc()
        messagebox.showerror("启动错误", error_msg)

if __name__ == "__main__":
    main()
