#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
释放COM5端口的专用工具
"""

import subprocess
import time
import sys

def kill_serial_processes():
    """结束可能占用串口的进程"""
    print("🔧 正在结束可能占用COM5的进程...")
    
    # 常见的串口工具进程名
    process_names = [
        'sscom32.exe',
        'sscom.exe', 
        'putty.exe',
        'teraterm.exe',
        'hyperterminal.exe',
        'serialport.exe',
        'comtool.exe',
        'uart.exe',
        'serial.exe'
    ]
    
    killed_count = 0
    for process_name in process_names:
        try:
            result = subprocess.run(['taskkill', '/f', '/im', process_name], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ 已结束进程: {process_name}")
                killed_count += 1
        except:
            pass
    
    if killed_count > 0:
        print(f"✅ 共结束了 {killed_count} 个进程")
        time.sleep(2)  # 等待进程完全结束
    else:
        print("ℹ️  未发现需要结束的进程")

def reset_com5_port():
    """重置COM5端口"""
    print("🔄 正在重置COM5端口...")
    
    try:
        # 使用PowerShell重置COM5端口
        ps_command = """
        Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like '*COM5*'} | ForEach-Object {
            Write-Host "找到设备: $($_.Name)"
            $_.Disable()
            Start-Sleep -Seconds 1
            $_.Enable()
            Write-Host "设备已重置"
        }
        """
        
        result = subprocess.run(['powershell', '-Command', ps_command], 
                              capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            print("✅ COM5端口重置完成")
            if result.stdout.strip():
                print(f"输出: {result.stdout.strip()}")
        else:
            print("⚠️  端口重置可能失败")
            
    except subprocess.TimeoutExpired:
        print("⚠️  端口重置超时")
    except Exception as e:
        print(f"❌ 端口重置失败: {e}")

def test_com5_access():
    """测试COM5访问"""
    print("🧪 测试COM5端口访问...")
    
    try:
        import serial
        
        # 尝试打开COM5
        ser = serial.Serial('COM5', 9600, timeout=1)
        print("✅ COM5端口访问成功!")
        ser.close()
        return True
        
    except PermissionError:
        print("❌ COM5端口仍然被拒绝访问")
        return False
    except Exception as e:
        print(f"❌ COM5端口访问失败: {e}")
        return False

def main():
    """主函数"""
    print("🔋 鲸测云LCER电池测试仪 - COM5端口释放工具")
    print("=" * 50)
    
    # 步骤1: 结束占用进程
    kill_serial_processes()
    
    # 步骤2: 重置端口
    reset_com5_port()
    
    # 步骤3: 测试访问
    print("\n" + "=" * 50)
    success = test_com5_access()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 COM5端口释放成功!")
        print("💡 现在可以启动应用程序并连接COM5")
    else:
        print("⚠️  COM5端口仍有问题")
        print("💡 建议尝试以下操作:")
        print("   1. 以管理员身份运行此工具")
        print("   2. 重新插拔USB设备")
        print("   3. 重启计算机")
    
    print("\n按回车键退出...")
    input()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  用户中断操作")
    except Exception as e:
        print(f"\n❌ 工具运行异常: {e}")
        import traceback
        traceback.print_exc()
        input("\n按回车键退出...")
