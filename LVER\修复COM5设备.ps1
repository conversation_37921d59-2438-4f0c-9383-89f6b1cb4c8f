# COM5设备自动修复脚本

Write-Host "COM5 Device Auto-Fix Script" -ForegroundColor Green
Write-Host "===========================" -ForegroundColor Gray

# 检查管理员权限
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")

if (-not $isAdmin) {
    Write-Host "Requesting administrator privileges..." -ForegroundColor Yellow
    Start-Process PowerShell -Verb RunAs -ArgumentList "-File `"$PSCommandPath`""
    exit
}

Write-Host "Running with administrator privileges" -ForegroundColor Green

# 查找COM5设备
Write-Host "`nStep 1: Finding COM5 device..." -ForegroundColor Cyan
$com5Device = Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like "*COM5*"}

if ($com5Device) {
    Write-Host "Found COM5 device: $($com5Device.Name)" -ForegroundColor Yellow
    Write-Host "Status: $($com5Device.Status)" -ForegroundColor Yellow
    Write-Host "Config Error Code: $($com5Device.ConfigManagerErrorCode)" -ForegroundColor Yellow
} else {
    Write-Host "COM5 device not found!" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit
}

# 尝试启用设备
Write-Host "`nStep 2: Attempting to enable COM5 device..." -ForegroundColor Cyan
try {
    $result = $com5Device.Enable()
    if ($result.ReturnValue -eq 0) {
        Write-Host "Device enable command successful" -ForegroundColor Green
    } else {
        Write-Host "Device enable failed with code: $($result.ReturnValue)" -ForegroundColor Red
    }
} catch {
    Write-Host "Enable attempt failed: $($_.Exception.Message)" -ForegroundColor Red
}

Start-Sleep -Seconds 3

# 重新检查设备状态
Write-Host "`nStep 3: Checking device status..." -ForegroundColor Cyan
$com5Device = Get-WmiObject -Class Win32_PnPEntity | Where-Object {$_.Name -like "*COM5*"}
if ($com5Device) {
    Write-Host "Updated Status: $($com5Device.Status)" -ForegroundColor Yellow
    Write-Host "Updated Error Code: $($com5Device.ConfigManagerErrorCode)" -ForegroundColor Yellow
}

# 如果仍有问题，尝试重新安装驱动
if ($com5Device.Status -eq "Error") {
    Write-Host "`nStep 4: Attempting driver reinstall..." -ForegroundColor Cyan
    
    $choice = Read-Host "Device still has errors. Attempt driver reinstall? (y/n)"
    if ($choice -eq "y" -or $choice -eq "Y") {
        try {
            # 卸载设备
            Write-Host "Uninstalling device..." -ForegroundColor Yellow
            $com5Device.Delete()
            
            Start-Sleep -Seconds 2
            
            # 扫描硬件变化
            Write-Host "Scanning for hardware changes..." -ForegroundColor Yellow
            $devcon = Get-Command "pnputil" -ErrorAction SilentlyContinue
            if ($devcon) {
                & pnputil /scan-devices
            }
            
            Write-Host "Driver reinstall attempted. Please unplug and replug the USB device." -ForegroundColor Green
            
        } catch {
            Write-Host "Driver reinstall failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 测试COM5访问
Write-Host "`nStep 5: Testing COM5 access..." -ForegroundColor Cyan
try {
    $pythonPath = "D:\code\haha\.venv\Scripts\python.exe"
    if (Test-Path $pythonPath) {
        $testScript = @"
import serial
try:
    ser = serial.Serial('COM5', 9600, timeout=1)
    print('SUCCESS: COM5 access restored!')
    ser.close()
except Exception as e:
    print(f'STILL FAILED: {e}')
"@
        
        $testResult = & $pythonPath -c $testScript
        Write-Host "Test result: $testResult" -ForegroundColor $(if ($testResult -like "*SUCCESS*") { "Green" } else { "Red" })
    } else {
        Write-Host "Python not found, skipping access test" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Access test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n===========================" -ForegroundColor Gray
Write-Host "Fix attempt completed." -ForegroundColor Green
Write-Host "If COM5 still doesn't work:" -ForegroundColor Yellow
Write-Host "1. Unplug USB device for 10 seconds" -ForegroundColor White
Write-Host "2. Plug it back in" -ForegroundColor White
Write-Host "3. Check Device Manager" -ForegroundColor White
Write-Host "4. Update/reinstall CP210x driver" -ForegroundColor White
Write-Host "`nPress Enter to exit..." -ForegroundColor Green
Read-Host
