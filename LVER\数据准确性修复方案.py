#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
鲸测云LCER电池测试仪 - 数据准确性修复方案
修复电压值异常波动问题的核心代码
"""

import serial
import time
import threading
import re
from datetime import datetime

class SerialDataProcessor:
    """真实串口数据处理器 - 替换模拟数据生成"""
    
    def __init__(self):
        self.data_buffer = ""
        self.last_measurements = {}
        self.debug_mode = True
        
    def process_serial_response(self, serial_conn, timeout=3.0):
        """
        处理串口响应数据 - 核心修复函数
        替换原有的模拟数据生成逻辑
        """
        if not serial_conn or not serial_conn.is_open:
            return []
        
        start_time = time.time()
        response_data = ""
        
        # 读取串口数据直到超时
        while time.time() - start_time < timeout:
            if serial_conn.in_waiting > 0:
                try:
                    chunk = serial_conn.read(serial_conn.in_waiting).decode('utf-8', errors='ignore')
                    response_data += chunk
                    
                    if self.debug_mode:
                        print(f"[DEBUG] 接收数据块: {repr(chunk)}")
                    
                    # 检查是否接收完整
                    if self._is_response_complete(response_data):
                        break
                        
                except Exception as e:
                    print(f"串口读取错误: {e}")
                    break
            
            time.sleep(0.01)  # 短暂等待
        
        if self.debug_mode:
            print(f"[DEBUG] 完整响应数据: {repr(response_data)}")
        
        # 解析响应数据
        return self._parse_response_data(response_data)
    
    def _is_response_complete(self, data):
        """检查响应数据是否完整"""
        # 检查是否包含预期的CSV行数
        csv_lines = [line for line in data.split('\n') if ',' in line and line.strip()]
        
        # 对于测试命令(0xAA)，期望4行CSV数据
        # 对于电压命令(0x55)，期望1行CSV数据
        return len(csv_lines) >= 1  # 至少有一行有效数据
    
    def _parse_response_data(self, response_data):
        """
        解析响应数据 - 核心数据解析逻辑
        """
        measurements = []
        
        if not response_data.strip():
            return measurements
        
        lines = response_data.strip().split('\n')
        
        for line in lines:
            line = line.strip()
            
            if self.debug_mode:
                print(f"[DEBUG] 处理行: {repr(line)}")
            
            # 跳过调试信息
            if self._is_debug_line(line):
                if self.debug_mode:
                    print(f"[DEBUG] 跳过调试行: {line}")
                continue
            
            # 解析CSV格式数据
            parsed_data = self._parse_csv_line(line)
            if parsed_data:
                measurements.append(parsed_data)
                if self.debug_mode:
                    print(f"[DEBUG] 解析成功: {parsed_data}")
        
        return measurements
    
    def _is_debug_line(self, line):
        """判断是否为调试信息行"""
        debug_patterns = [
            r'^Start Voltage\s*:',
            r'^HF_current\s*:',
            r'^.*zeroPoint\s*:',
            r'^.*mA\s+zeroPoint',
            r'^\s*$'  # 空行
        ]
        
        for pattern in debug_patterns:
            if re.match(pattern, line, re.IGNORECASE):
                return True
        
        return False
    
    def _parse_csv_line(self, line):
        """
        解析CSV格式行 - 关键数据解析函数
        期望格式: "1,V,5075" 或 "2,R_sei,281" 等
        """
        if ',' not in line:
            return None
        
        try:
            parts = line.split(',')
            
            if len(parts) < 3:
                if self.debug_mode:
                    print(f"[DEBUG] CSV格式不完整: {parts}")
                return None
            
            # 解析各部分
            param_id = parts[0].strip()
            param_type = parts[1].strip()
            param_value_str = parts[2].strip()
            
            # 验证参数ID
            if not param_id.isdigit():
                if self.debug_mode:
                    print(f"[DEBUG] 无效参数ID: {param_id}")
                return None
            
            # 验证参数类型
            valid_types = ['V', 'R_sei', 'R_ct', 'R_ohm']
            if param_type not in valid_types:
                if self.debug_mode:
                    print(f"[DEBUG] 未知参数类型: {param_type}")
                return None
            
            # 转换数值
            try:
                param_value = int(param_value_str)
            except ValueError:
                if self.debug_mode:
                    print(f"[DEBUG] 数值转换失败: {param_value_str}")
                return None
            
            # 数据合理性检查
            if not self._validate_measurement_value(param_type, param_value):
                if self.debug_mode:
                    print(f"[DEBUG] 数值超出合理范围: {param_type}={param_value}")
                return None
            
            return {
                'param_id': int(param_id),
                'param_type': param_type,
                'param_value': param_value,
                'timestamp': datetime.now(),
                'raw_line': line
            }
            
        except Exception as e:
            if self.debug_mode:
                print(f"[DEBUG] CSV解析异常: {e}, 行: {line}")
            return None
    
    def _validate_measurement_value(self, param_type, value):
        """验证测量值的合理性"""
        # 定义各参数的合理范围
        valid_ranges = {
            'V': (1000, 5000),      # 电压: 1V-5V (mV)
            'R_sei': (50, 500),     # SEI阻抗: 50-500 μΩ
            'R_ct': (50, 300),      # 电荷转移阻抗: 50-300 μΩ
            'R_ohm': (200, 800)     # 欧姆阻抗: 200-800 μΩ
        }
        
        if param_type in valid_ranges:
            min_val, max_val = valid_ranges[param_type]
            return min_val <= value <= max_val
        
        return True  # 未知类型暂时通过
    
    def get_stable_measurement(self, param_type, new_value):
        """
        获取稳定的测量值 - 数据稳定性处理
        使用简单的滑动平均来减少波动
        """
        if param_type not in self.last_measurements:
            self.last_measurements[param_type] = []
        
        # 保存最近的5个值
        self.last_measurements[param_type].append(new_value)
        if len(self.last_measurements[param_type]) > 5:
            self.last_measurements[param_type].pop(0)
        
        # 计算平均值
        values = self.last_measurements[param_type]
        stable_value = sum(values) / len(values)
        
        return int(stable_value)


class FixedSerialCommunication:
    """修复后的串口通信类"""
    
    def __init__(self):
        self.processor = SerialDataProcessor()
        self.serial_conn = None
        
    def send_test_command_fixed(self, serial_conn):
        """
        修复后的测试命令发送函数
        替换原有的 send_test_command 方法
        """
        if not serial_conn or not serial_conn.is_open:
            raise Exception("串口未连接")
        
        try:
            # 发送0xAA命令
            command = bytes([0xAA])
            serial_conn.write(command)
            
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 发送测试命令: 0xAA")
            
            # 处理响应数据
            measurements = self.processor.process_serial_response(serial_conn, timeout=3.0)
            
            if not measurements:
                print("警告: 未接收到有效的测量数据")
                return []
            
            # 应用数据稳定性处理
            stable_measurements = []
            for measurement in measurements:
                param_type = measurement['param_type']
                raw_value = measurement['param_value']
                stable_value = self.processor.get_stable_measurement(param_type, raw_value)
                
                stable_measurement = measurement.copy()
                stable_measurement['param_value'] = stable_value
                stable_measurement['raw_value'] = raw_value
                stable_measurements.append(stable_measurement)
                
                print(f"✓ {param_type}: {raw_value} → {stable_value} (稳定化)")
            
            return stable_measurements
            
        except Exception as e:
            print(f"测试命令执行失败: {e}")
            raise
    
    def send_voltage_command_fixed(self, serial_conn):
        """
        修复后的电压测量命令发送函数
        替换原有的 send_voltage_command 方法
        """
        if not serial_conn or not serial_conn.is_open:
            raise Exception("串口未连接")
        
        try:
            # 发送0x55命令
            command = bytes([0x55])
            serial_conn.write(command)
            
            print(f"[{datetime.now().strftime('%H:%M:%S')}] 发送电压测量命令: 0x55")
            
            # 处理响应数据
            measurements = self.processor.process_serial_response(serial_conn, timeout=2.0)
            
            # 查找电压数据
            voltage_measurement = None
            for measurement in measurements:
                if measurement['param_type'] == 'V':
                    voltage_measurement = measurement
                    break
            
            if not voltage_measurement:
                print("警告: 未接收到电压测量数据")
                return None
            
            # 应用数据稳定性处理
            raw_value = voltage_measurement['param_value']
            stable_value = self.processor.get_stable_measurement('V', raw_value)
            
            voltage_measurement['param_value'] = stable_value
            voltage_measurement['raw_value'] = raw_value
            
            print(f"✓ 电压测量: {raw_value} → {stable_value} mV (稳定化)")
            
            return voltage_measurement
            
        except Exception as e:
            print(f"电压测量命令执行失败: {e}")
            raise


# 使用示例和测试代码
def test_data_processor():
    """测试数据处理器"""
    processor = SerialDataProcessor()
    
    # 测试数据样例
    test_responses = [
        "Start Voltage : 5075\nHF_current:0 mA zeroPoint:21\n1,V,5075\n2,R_sei,281\n3,R_ct,165\n4,R_ohm,502\n",
        "1,V,3290\n",
        "Start Voltage : 3290\n1,V,3290\n2,R_sei,275\n",
        "HF_current:0 mA zeroPoint:21\n1,V,3295\n"
    ]
    
    print("=== 数据处理器测试 ===")
    
    for i, response in enumerate(test_responses, 1):
        print(f"\n--- 测试 {i} ---")
        print(f"原始数据: {repr(response)}")
        
        measurements = processor._parse_response_data(response)
        print(f"解析结果: {len(measurements)} 个测量值")
        
        for measurement in measurements:
            print(f"  {measurement['param_type']}: {measurement['param_value']} (ID: {measurement['param_id']})")

if __name__ == "__main__":
    test_data_processor()
