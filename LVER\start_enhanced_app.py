#!/usr/bin/env python3
"""
启动增强版LVER串口通信应用程序
"""

import sys
import os
import subprocess
import importlib.util

def find_python_with_dependencies():
    """查找安装了所有必要依赖的Python解释器"""
    python_candidates = [
        sys.executable,  # 当前Python
        "python",
        "python3",
        "py -3",
        r"C:\Program Files\Python38\python.exe",
        r"C:\Program Files\Python39\python.exe",
        r"C:\Program Files\Python310\python.exe",
        r"C:\Program Files\Python311\python.exe",
        r"C:\Program Files\Python312\python.exe",
        r"C:\Program Files\Python313\python.exe",
        r"C:\Python38\python.exe",
        r"C:\Python39\python.exe",
        r"C:\Python310\python.exe",
        r"C:\Python311\python.exe",
        r"C:\Python312\python.exe",
        r"C:\Python313\python.exe",
    ]
    
    # 添加用户目录下的Python
    user_profile = os.environ.get('USERPROFILE', '')
    if user_profile:
        for version in ['38', '39', '310', '311', '312', '313']:
            python_candidates.extend([
                os.path.join(user_profile, f'AppData\\Local\\Programs\\Python\\Python{version}\\python.exe'),
                os.path.join(user_profile, f'AppData\\Local\\Microsoft\\WindowsApps\\python{version[0]}.{version[1:]}.exe'),
            ])
    
    print("正在查找合适的Python解释器...")
    
    required_modules = ['serial', 'matplotlib', 'tkinter']
    
    for python_cmd in python_candidates:
        try:
            # 检查Python是否存在
            result = subprocess.run([python_cmd, "--version"], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"检查Python: {python_cmd} ({version})")
                
                # 检查所有必要模块
                all_modules_available = True
                missing_modules = []
                
                for module in required_modules:
                    if module == 'tkinter':
                        # tkinter的导入名称可能不同
                        test_cmd = "import tkinter"
                    else:
                        test_cmd = f"import {module}"
                    
                    result = subprocess.run([python_cmd, "-c", test_cmd], 
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode != 0:
                        all_modules_available = False
                        missing_modules.append(module)
                
                if all_modules_available:
                    print(f"✓ {python_cmd} 具备所有必要依赖")
                    return python_cmd
                else:
                    print(f"✗ {python_cmd} 缺少模块: {', '.join(missing_modules)}")
                    
        except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
            continue
    
    return None

def main():
    """主函数"""
    print("LVER 增强版串口通信应用程序启动器")
    print("=" * 50)
    
    # 查找合适的Python解释器
    python_cmd = find_python_with_dependencies()
    
    if not python_cmd:
        print("\n❌ 错误: 未找到安装了所有必要依赖的Python解释器")
        print("\n缺少的依赖可能包括:")
        print("- pyserial (串口通信)")
        print("- matplotlib (图表显示)")
        print("- tkinter (GUI界面)")
        print("\n请运行以下命令安装依赖:")
        print("pip install pyserial matplotlib")
        print("\n或者运行 install_matplotlib.py 脚本")
        input("\n按回车键退出...")
        return False
    
    print(f"\n使用Python解释器: {python_cmd}")
    print("正在启动LVER增强版应用程序...")
    
    # 启动应用程序
    try:
        # 获取脚本目录
        script_dir = os.path.dirname(os.path.abspath(__file__))
        gui_script = os.path.join(script_dir, "gui.py")
        
        if not os.path.exists(gui_script):
            print(f"❌ 错误: 找不到GUI脚本文件: {gui_script}")
            input("按回车键退出...")
            return False
        
        # 启动GUI应用程序
        subprocess.run([python_cmd, gui_script])
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"\n❌ 启动应用程序时出错: {e}")
        input("按回车键退出...")
        return False
    
    return True

if __name__ == "__main__":
    main()
