#!/usr/bin/env python3
"""
测试多参数显示功能
"""

import tkinter as tk
from tkinter import ttk
import time
import threading
import random

class MultiParamTestApp:
    def __init__(self, root):
        self.root = root
        self.root.title("多参数测试 - 模拟真实设备响应")
        self.root.geometry("800x600")
        
        self.test_data = []
        self.create_widgets()
        
    def create_widgets(self):
        # 控制区域
        control_frame = ttk.LabelFrame(self.root, text="测试控制", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(control_frame, text="模拟测试命令 (0xAA)", command=self.simulate_test_command).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="模拟电压测量 (0x55)", command=self.simulate_voltage_command).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="清空数据", command=self.clear_data).pack(side=tk.LEFT, padx=5)
        
        # 数据显示区域
        display_frame = ttk.LabelFrame(self.root, text="多参数实时显示", padding=10)
        display_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 创建6个参数卡片
        cards_frame = ttk.Frame(display_frame)
        cards_frame.pack(fill=tk.X, pady=10)
        
        # 第一行
        self.voltage_card = self.create_data_card(cards_frame, "电压 (V)", "mV", "blue")
        self.voltage_card.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        
        self.r_sei_card = self.create_data_card(cards_frame, "SEI电阻", "μΩ", "green")
        self.r_sei_card.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        self.r_ct_card = self.create_data_card(cards_frame, "CT电阻", "μΩ", "purple")
        self.r_ct_card.grid(row=0, column=2, padx=5, pady=5, sticky="ew")
        
        # 第二行
        self.r_ohm_card = self.create_data_card(cards_frame, "欧姆电阻", "μΩ", "red")
        self.r_ohm_card.grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        
        self.count_card = self.create_data_card(cards_frame, "测试次数", "次", "orange")
        self.count_card.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        
        self.status_card = self.create_data_card(cards_frame, "状态", "", "gray")
        self.status_card.grid(row=1, column=2, padx=5, pady=5, sticky="ew")
        
        # 配置列权重
        for i in range(3):
            cards_frame.columnconfigure(i, weight=1)
        
        # 数据列表
        list_frame = ttk.Frame(display_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        columns = ('时间', '参数类型', '测量值', '单位')
        self.data_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=15)
        
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=100)
        
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        self.data_tree.configure(yscrollcommand=scrollbar.set)
        
        self.data_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 初始化状态
        self.status_card.value_label.config(text="就绪")
        
    def create_data_card(self, parent, title, unit, color):
        """创建数据显示卡片"""
        card_frame = ttk.LabelFrame(parent, text=title, padding=10)
        
        # 数值显示
        value_label = tk.Label(card_frame, text="--", font=("Arial", 20, "bold"), fg=color)
        value_label.pack()
        
        # 单位显示
        unit_label = tk.Label(card_frame, text=unit, font=("Arial", 10))
        unit_label.pack()
        
        # 存储引用
        card_frame.value_label = value_label
        card_frame.unit_label = unit_label
        
        return card_frame
    
    def simulate_test_command(self):
        """模拟测试命令 (0xAA) - 返回多个参数"""
        print("模拟发送: 0xAA")
        
        # 模拟真实设备的多种测量值（基于您提供的截图）
        measurements = [
            ("R_sei", random.randint(270, 290), "μΩ"),
            ("R_ct", random.randint(150, 170), "μΩ"),
            ("V", random.randint(3200, 3400), "mV"),
            ("R_ohm", random.randint(500, 530), "μΩ")
        ]
        
        for measure_type, value, unit in measurements:
            self.add_measurement_data(measure_type, value, unit)
            time.sleep(0.1)  # 模拟设备响应间隔
        
        print(f"模拟响应: 4行数据 (R_sei, R_ct, V, R_ohm)")
    
    def simulate_voltage_command(self):
        """模拟电压测量命令 (0x55) - 返回单个电压值"""
        print("模拟发送: 0x55")
        
        voltage = random.randint(3200, 3400)
        self.add_measurement_data("V", voltage, "mV")
        
        print(f"模拟响应: 1行数据 (V={voltage})")
    
    def add_measurement_data(self, measure_type, value, unit):
        """添加测量数据"""
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")
        
        # 添加到数据列表
        self.test_data.append({
            'time': timestamp,
            'type': measure_type,
            'value': value,
            'unit': unit
        })
        
        # 更新数据表格
        self.data_tree.insert('', 0, values=(timestamp, measure_type, value, unit))
        
        # 更新对应的实时显示卡片
        if measure_type == "V":
            self.voltage_card.value_label.config(text=str(value))
        elif measure_type == "R_sei":
            self.r_sei_card.value_label.config(text=str(value))
        elif measure_type == "R_ct":
            self.r_ct_card.value_label.config(text=str(value))
        elif measure_type == "R_ohm":
            self.r_ohm_card.value_label.config(text=str(value))
        
        # 更新测试次数
        self.count_card.value_label.config(text=str(len(self.test_data)))
        
        # 更新状态
        self.status_card.value_label.config(text="数据更新")
        
        print(f"数据记录: {measure_type} = {value} {unit}")
    
    def clear_data(self):
        """清空数据"""
        self.test_data.clear()
        
        # 清空数据表格
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)
        
        # 重置显示卡片
        self.voltage_card.value_label.config(text="--")
        self.r_sei_card.value_label.config(text="--")
        self.r_ct_card.value_label.config(text="--")
        self.r_ohm_card.value_label.config(text="--")
        self.count_card.value_label.config(text="0")
        self.status_card.value_label.config(text="已清空")
        
        print("数据已清空")

def main():
    print("="*50)
    print("多参数测试应用程序")
    print("="*50)
    print("模拟真实设备的多参数响应:")
    print("- 测试命令 (0xAA): 返回 R_sei, R_ct, V, R_ohm")
    print("- 电压测量 (0x55): 返回 V")
    print("="*50)
    
    root = tk.Tk()
    app = MultiParamTestApp(root)
    
    print("✓ 多参数测试应用启动成功")
    print("✓ 点击按钮测试多参数显示功能")
    
    root.mainloop()

if __name__ == "__main__":
    main()
