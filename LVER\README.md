# LVER 串口通信应用程序

这是一个用于与设备进行串口通信的Windows桌面应用程序，实现了完整的设备命令协议和友好的用户界面。

## 功能特性

- 支持串口设备连接和通信
- 实现三种设备命令协议：
  - **Test Command (0xAA)** - 获取电压和电阻值（返回4行CSV数据）
  - **Reset Command (0xA0)** - 重置设备（无响应）
  - **Voltage Measurement Command (0x55)** - 电压测量（返回单行CSV数据）
- 友好的图形用户界面
- 实时显示通信状态和响应数据
- 完善的错误处理和超时保护
- 响应数据验证和格式化显示
- 日志记录功能

## 文件结构

```
LVER/
├── main.py                 # 主程序入口
├── demo_mode.py           # 演示模式（无需真实设备）
├── gui.py                 # GUI界面模块
├── serial_communication.py # 串口通信核心模块
├── device_protocol.py     # 设备协议处理模块
├── error_handler.py       # 错误处理和日志模块
├── test_app.py           # 应用程序测试脚本
├── run.bat               # Windows启动脚本
├── requirements.txt      # Python依赖列表
└── README.md            # 说明文档
```

## 安装和运行

### 方法1：使用启动脚本（推荐）
1. 双击运行 `run.bat`
2. 脚本会自动检查依赖并启动应用程序

### 方法2：手动安装
1. 确保已安装Python 3.7+
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
3. 运行测试：
   ```bash
   python test_app.py
   ```
4. 启动应用程序：
   ```bash
   python main.py
   ```

### 方法3：演示模式
如果没有真实设备或遇到串口问题，可以使用演示模式：
```bash
python demo_mode.py
```

## 使用说明

### 基本操作流程
1. **选择串口**：从下拉列表中选择正确的串口设备
2. **设置波特率**：选择合适的波特率（默认9600）
3. **建立连接**：点击"连接"按钮建立串口连接
4. **发送命令**：使用相应按钮发送设备命令
5. **查看响应**：在响应区域查看设备返回的数据

### 命令说明

#### 测试命令 (0xAA)
- **功能**：获取设备的电压和电阻测量值
- **响应格式**：
  ```
  1,V,{voltage_value}
  1,R_ohm,{resistance_ohm_value}
  1,R_sei,{resistance_sei_value}
  1,R_ct,{resistance_ct_value}
  ```
- **显示**：应用程序会解析并格式化显示各项数值

#### 重置命令 (0xA0)
- **功能**：重置设备状态
- **响应**：无响应数据
- **状态**：显示命令发送成功/失败状态

#### 电压测量命令 (0x55)
- **功能**：测量B2电压
- **响应格式**：`1,B2_Voltage,{voltage_value}`
- **显示**：解析并显示电压值

### 界面说明

#### 串口配置区域
- **串口选择**：显示系统中可用的串口设备
- **刷新按钮**：重新扫描可用串口
- **波特率**：设置通信波特率
- **连接按钮**：建立/断开串口连接

#### 设备命令区域
- **测试命令按钮**：发送0xAA命令
- **重置命令按钮**：发送0xA0命令
- **电压测量按钮**：发送0x55命令
- **清空显示按钮**：清空响应显示区域

#### 连接状态区域
- **状态指示器**：显示当前连接状态
- **最后操作**：显示最近一次操作的时间

#### 设备响应区域
- **时间戳**：每条消息的时间戳
- **命令日志**：发送的命令记录
- **响应数据**：设备返回的格式化数据
- **错误信息**：通信错误和异常信息

## 技术栈

- **Python 3.7+** - 主要编程语言
- **tkinter** - GUI框架（Python内置）
- **pyserial** - 串口通信库
- **threading** - 多线程支持
- **logging** - 日志记录

## 错误处理

应用程序包含完善的错误处理机制：

- **连接错误**：串口不存在、权限不足、设备占用等
- **通信错误**：发送失败、接收超时、数据损坏等
- **协议错误**：响应格式不正确、数据验证失败等
- **硬件错误**：设备断开、串口故障等

所有错误都会在界面上显示，并记录到日志文件中。

## 故障排除

### 常见问题

1. **找不到串口设备**
   - 检查设备是否正确连接
   - 确认设备驱动已安装
   - 尝试刷新串口列表

2. **连接失败**
   - 检查串口是否被其他程序占用
   - 确认波特率设置正确
   - 尝试重新插拔设备

3. **通信超时**
   - 检查设备是否正常工作
   - 确认命令协议是否正确
   - 尝试增加超时时间

4. **依赖安装失败**
   - 确保Python版本为3.7+
   - 尝试使用管理员权限安装
   - 检查网络连接

### 调试模式

使用演示模式进行功能测试：
```bash
python demo_mode.py
```

查看详细日志：
- 日志文件：`lver_error.log`
- 控制台输出：运行时的详细信息

## 开发说明

### 代码结构
- **模块化设计**：各功能模块独立，便于维护
- **错误处理**：统一的错误处理和日志记录
- **线程安全**：GUI操作在主线程，串口通信在后台线程
- **协议验证**：严格的数据格式验证

### 扩展功能
- 添加新命令：在`device_protocol.py`中定义新的协议
- 修改界面：在`gui.py`中调整界面布局
- 增强错误处理：在`error_handler.py`中添加新的错误类型

## 许可证

本项目仅供学习和开发使用。
