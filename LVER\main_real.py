#!/usr/bin/env python3
"""
LVER 串口通信应用程序主入口 - 真实串口版本
支持真实串口连接和完整图表功能
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_dependencies():
    """检查并安装依赖项"""
    missing_deps = []
    
    try:
        import serial
        import serial.tools.list_ports
        print("✓ pyserial 依赖检查通过")
    except ImportError:
        missing_deps.append("pyserial")
    
    try:
        import matplotlib
        print("✓ matplotlib 依赖检查通过")
    except ImportError:
        missing_deps.append("matplotlib")
    
    if missing_deps:
        print(f"缺少依赖: {', '.join(missing_deps)}")
        print("请运行以下命令安装:")
        for dep in missing_deps:
            print(f"  pip install {dep}")
        return False
    
    return True

def main():
    """主函数"""
    print("正在启动LVER应用程序 (真实串口版本)...")
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    try:
        # 导入GUI模块
        from gui import LVERApp
        print("✓ GUI模块导入成功")
        
        # 创建主窗口
        root = tk.Tk()
        root.title("LVER 串口通信应用程序 - 完整版")
        root.geometry("1400x1000")  # 使用更大的窗口
        
        # 设置窗口图标（如果存在）
        try:
            root.iconbitmap("icon.ico")
        except:
            pass
        
        print("✓ 主窗口创建成功")
        
        # 创建应用程序实例
        app = LVERApp(root)
        print("✓ 应用程序实例创建成功")
        
        # 延迟启动串口监控，避免主循环未开始的问题
        def delayed_setup():
            try:
                app.setup_port_monitoring()
                print("✓ 串口监控启动成功")
            except Exception as e:
                print(f"串口监控启动失败: {e}")
        
        # 在主循环开始后启动串口监控
        root.after(1000, delayed_setup)
        
        print("\n" + "="*60)
        print("LVER应用程序启动成功！")
        print("功能特性：")
        print("1. ✓ 真实串口连接支持")
        print("2. ✓ 完整图表显示功能 (matplotlib)")
        print("3. ✓ 增大的图表显示区域")
        print("4. ✓ 高清图表导出 (PNG)")
        print("5. ✓ CSV数据导出功能")
        print("6. ✓ 实时数据监控")
        print("="*60)
        
        # 显示使用说明
        def show_startup_info():
            info_text = """
LVER 应用程序已启动！

修复内容：
✓ 图表显示问题已修复
✓ 图表区域已增大 (占83%屏幕空间)
✓ 导出功能已优化 (图表+数据)
✓ 串口连接错误已修复

使用说明：
1. 连接串口设备
2. 选择正确的串口和波特率
3. 点击"连接"建立连接
4. 进行测试并查看实时图表
5. 使用导出功能保存数据

注意：如果图表区域仍显示错误，请重启应用程序。
            """
            messagebox.showinfo("启动信息", info_text)
        
        # 延迟显示启动信息
        root.after(2000, show_startup_info)
        
        # 启动主循环
        print("启动GUI主循环...")
        root.mainloop()
        
    except ImportError as e:
        error_msg = f"模块导入失败: {e}\n\n请确保所有依赖都已正确安装。"
        print(error_msg)
        messagebox.showerror("导入错误", error_msg)
        
    except Exception as e:
        error_msg = f"应用程序启动失败: {e}"
        print(error_msg)
        import traceback
        traceback.print_exc()
        messagebox.showerror("启动错误", error_msg)

if __name__ == "__main__":
    main()
