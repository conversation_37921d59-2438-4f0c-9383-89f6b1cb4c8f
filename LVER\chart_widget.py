"""
图表组件模块
使用matplotlib创建实时数据可视化图表
增强版：支持更大显示区域、图表导出功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
from matplotlib.figure import Figure
import matplotlib.dates as mdates
from datetime import datetime
import os
from typing import Dict, List, Optional, Tuple
from data_manager import DataManager


class ChartWidget:
    """增强版图表组件 - 支持更大显示区域和图表导出"""

    def __init__(self, parent, data_manager: DataManager):
        self.parent = parent
        self.data_manager = data_manager

        # 创建图表框架 - 增大尺寸
        self.chart_frame = ttk.LabelFrame(parent, text="实时数据图表 (增强版)", padding="5")

        # 图表配置 - 增大图表尺寸和DPI
        self.figure = Figure(figsize=(14, 8), dpi=120)  # 从(10,6)增大到(14,8)，DPI从100增大到120
        self.ax = self.figure.add_subplot(111)

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False

        # 创建画布
        self.canvas = FigureCanvasTkAgg(self.figure, self.chart_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # 创建工具栏
        self.toolbar = NavigationToolbar2Tk(self.canvas, self.chart_frame)
        self.toolbar.update()

        # 图表控制区域
        self.create_chart_controls()

        # 数据线配置
        self.lines = {}
        self.line_colors = {
            'voltage': '#1f77b4',    # 蓝色
            'r_ohm': '#ff7f0e',      # 橙色
            'r_sei': '#2ca02c',      # 绿色
            'r_ct': '#d62728'        # 红色
        }

        # 显示配置
        self.show_voltage = tk.BooleanVar(value=True)
        self.show_r_ohm = tk.BooleanVar(value=True)
        self.show_r_sei = tk.BooleanVar(value=True)
        self.show_r_ct = tk.BooleanVar(value=True)

        # 初始化图表
        self.setup_chart()
        
    def create_chart_controls(self):
        """创建图表控制区域 - 增强版，包含导出功能"""
        control_frame = ttk.Frame(self.chart_frame)
        control_frame.pack(fill=tk.X, pady=(5, 0))

        # 数据显示选择
        ttk.Label(control_frame, text="显示数据:").pack(side=tk.LEFT, padx=(0, 10))

        self.voltage_check = ttk.Checkbutton(
            control_frame, text="电压", variable=self.show_voltage,
            command=self.update_chart
        )
        self.voltage_check.pack(side=tk.LEFT, padx=(0, 5))

        self.r_ohm_check = ttk.Checkbutton(
            control_frame, text="电阻", variable=self.show_r_ohm,
            command=self.update_chart
        )
        self.r_ohm_check.pack(side=tk.LEFT, padx=(0, 5))

        self.r_sei_check = ttk.Checkbutton(
            control_frame, text="SEI电阻", variable=self.show_r_sei,
            command=self.update_chart
        )
        self.r_sei_check.pack(side=tk.LEFT, padx=(0, 5))

        self.r_ct_check = ttk.Checkbutton(
            control_frame, text="CT电阻", variable=self.show_r_ct,
            command=self.update_chart
        )
        self.r_ct_check.pack(side=tk.LEFT, padx=(0, 5))

        # 控制按钮组
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(side=tk.RIGHT, padx=(10, 0))

        # 导出图表按钮
        ttk.Button(
            button_frame, text="导出图表",
            command=self.export_chart
        ).pack(side=tk.RIGHT, padx=(5, 0))

        # 导出数据按钮
        ttk.Button(
            button_frame, text="导出数据",
            command=self.export_data
        ).pack(side=tk.RIGHT, padx=(5, 0))

        # 清空数据按钮
        ttk.Button(
            button_frame, text="清空数据",
            command=self.clear_chart_data
        ).pack(side=tk.RIGHT, padx=(5, 0))

        # 刷新按钮
        ttk.Button(
            button_frame, text="刷新图表",
            command=self.update_chart
        ).pack(side=tk.RIGHT, padx=(5, 0))
    
    def setup_chart(self):
        """设置图表基本配置"""
        self.ax.clear()
        self.ax.set_xlabel('测试序号')
        self.ax.set_ylabel('数值')
        self.ax.set_title('LVER 实时测试数据')
        self.ax.grid(True, alpha=0.3)
        
        # 设置图例
        self.ax.legend(loc='upper right')
        
        self.canvas.draw()
    
    def update_chart(self):
        """更新图表数据"""
        self.ax.clear()

        # 清除之前的第二个Y轴
        if hasattr(self, '_ax2'):
            self._ax2.remove()
            delattr(self, '_ax2')

        # 设置基本属性
        self.ax.set_xlabel('测试序号')
        self.ax.set_title('LVER 实时测试数据')
        self.ax.grid(True, alpha=0.3)

        # 检查是否有数据
        if not self.data_manager.get_all_data():
            self.ax.text(0.5, 0.5, '暂无数据\n请先进行测试',
                        transform=self.ax.transAxes,
                        ha='center', va='center', fontsize=12)
            self.canvas.draw()
            return

        # 分别处理电压和电阻数据
        voltage_plotted = False
        resistance_plotted = False

        # 绘制电压数据（左Y轴）
        if self.show_voltage.get():
            x_data, y_data, label = self.data_manager.get_data_for_chart('voltage')
            if x_data and y_data:
                self.ax.plot(x_data, y_data,
                           color=self.line_colors['voltage'],
                           marker='o', markersize=4, linewidth=2,
                           label=label)
                self.ax.set_ylabel('电压 (V)', color=self.line_colors['voltage'])
                voltage_plotted = True

        # 创建第二个Y轴用于电阻数据
        ax2 = None
        resistance_lines = []
        resistance_labels = []

        if self.show_r_ohm.get():
            x_data, y_data, label = self.data_manager.get_data_for_chart('r_ohm')
            if x_data and y_data:
                if ax2 is None:
                    ax2 = self.ax.twinx()
                    self._ax2 = ax2
                line = ax2.plot(x_data, y_data,
                               color=self.line_colors['r_ohm'],
                               marker='s', markersize=4, linewidth=2,
                               label=label)
                resistance_lines.extend(line)
                resistance_labels.append(label)
                resistance_plotted = True

        if self.show_r_sei.get():
            x_data, y_data, label = self.data_manager.get_data_for_chart('r_sei')
            if x_data and y_data:
                if ax2 is None:
                    ax2 = self.ax.twinx()
                    self._ax2 = ax2
                line = ax2.plot(x_data, y_data,
                               color=self.line_colors['r_sei'],
                               marker='^', markersize=4, linewidth=2,
                               label=label)
                resistance_lines.extend(line)
                resistance_labels.append(label)
                resistance_plotted = True

        if self.show_r_ct.get():
            x_data, y_data, label = self.data_manager.get_data_for_chart('r_ct')
            if x_data and y_data:
                if ax2 is None:
                    ax2 = self.ax.twinx()
                    self._ax2 = ax2
                line = ax2.plot(x_data, y_data,
                               color=self.line_colors['r_ct'],
                               marker='d', markersize=4, linewidth=2,
                               label=label)
                resistance_lines.extend(line)
                resistance_labels.append(label)
                resistance_plotted = True

        # 设置第二个Y轴标签
        if ax2 is not None and resistance_plotted:
            ax2.set_ylabel('电阻 (μΩ)', color='green')

        # 设置图例
        lines1, labels1 = self.ax.get_legend_handles_labels()
        lines2, labels2 = [], []
        if ax2 is not None:
            lines2, labels2 = ax2.get_legend_handles_labels()

        if lines1 or lines2:
            self.ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

        # 自动调整布局
        self.figure.tight_layout()
        self.canvas.draw()
    
    def export_chart(self):
        """导出图表为PNG图片"""
        try:
            # 获取当前时间作为默认文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"LVER_Chart_{timestamp}.png"

            # 选择保存路径
            file_path = filedialog.asksaveasfilename(
                defaultextension=".png",
                filetypes=[
                    ("PNG files", "*.png"),
                    ("All files", "*.*")
                ],
                initialvalue=default_filename,
                title="导出图表"
            )

            if file_path:
                # 设置高分辨率导出参数
                export_dpi = 300  # 高分辨率DPI

                # 导出图表
                self.figure.savefig(
                    file_path,
                    format='png',
                    dpi=export_dpi,
                    bbox_inches='tight',
                    facecolor='white',
                    edgecolor='none'
                )

                messagebox.showinfo(
                    "导出成功",
                    f"图表已成功导出为:\n{file_path}\n\n"
                    f"DPI: {export_dpi}"
                )

        except Exception as e:
            messagebox.showerror("导出失败", f"导出图表时发生错误:\n{str(e)}")

    def export_data(self):
        """导出数据为CSV文件"""
        try:
            # 获取当前时间作为默认文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            default_filename = f"LVER_Data_{timestamp}.csv"

            # 选择保存路径
            file_path = filedialog.asksaveasfilename(
                defaultextension=".csv",
                filetypes=[
                    ("CSV files", "*.csv"),
                    ("All files", "*.*")
                ],
                initialvalue=default_filename,
                title="导出数据"
            )

            if file_path:
                # 导出数据
                success = self.data_manager.export_to_csv(file_path)
                if success:
                    messagebox.showinfo(
                        "导出成功",
                        f"数据已成功导出为:\n{file_path}"
                    )
                else:
                    messagebox.showerror("导出失败", "数据导出失败")

        except Exception as e:
            messagebox.showerror("导出失败", f"导出数据时发生错误:\n{str(e)}")

    def clear_chart_data(self):
        """清空图表数据"""
        self.data_manager.clear_data()
        self.setup_chart()

    def pack(self, **kwargs):
        """打包图表框架"""
        self.chart_frame.pack(**kwargs)

    def grid(self, **kwargs):
        """网格布局图表框架"""
        self.chart_frame.grid(**kwargs)


class SimpleChartWidget:
    """简化版图表组件（如果matplotlib不可用）"""
    
    def __init__(self, parent, data_manager: DataManager):
        self.parent = parent
        self.data_manager = data_manager
        
        # 创建简单的文本显示区域
        self.chart_frame = ttk.LabelFrame(parent, text="数据统计", padding="5")
        
        self.text_widget = tk.Text(
            self.chart_frame, 
            height=10, 
            width=50,
            state=tk.DISABLED
        )
        self.text_widget.pack(fill=tk.BOTH, expand=True)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(self.chart_frame, orient=tk.VERTICAL, command=self.text_widget.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.text_widget.config(yscrollcommand=scrollbar.set)
        
        # 控制按钮
        button_frame = ttk.Frame(self.chart_frame)
        button_frame.pack(fill=tk.X, pady=(5, 0))
        
        ttk.Button(button_frame, text="刷新统计", command=self.update_stats).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="清空数据", command=self.clear_data).pack(side=tk.LEFT, padx=(5, 0))
    
    def update_chart(self):
        """更新统计信息"""
        self.update_stats()
    
    def update_stats(self):
        """更新统计信息显示"""
        stats = self.data_manager.get_statistics()
        latest = self.data_manager.get_latest_data()
        
        self.text_widget.config(state=tk.NORMAL)
        self.text_widget.delete(1.0, tk.END)
        
        content = f"""数据统计信息
{'='*30}
总测试次数: {stats['total_tests']}

电压统计:
  平均值: {stats['voltage_avg']/1000:.3f} V ({stats['voltage_avg']:.0f} mV)
  最小值: {stats['voltage_min']/1000:.3f} V ({stats['voltage_min']:.0f} mV)
  最大值: {stats['voltage_max']/1000:.3f} V ({stats['voltage_max']:.0f} mV)

电阻统计:
  平均电阻: {stats['r_ohm_avg']:.3f} μΩ
  平均SEI电阻: {stats['r_sei_avg']:.3f} μΩ
  平均CT电阻: {stats['r_ct_avg']:.3f} μΩ
"""
        
        if latest:
            content += f"""
最新测试数据:
  时间: {latest.timestamp.strftime('%H:%M:%S')}
  电压: {latest.voltage/1000:.3f} V ({latest.voltage:.0f} mV)
  电阻: {latest.r_ohm:.3f} μΩ
  SEI电阻: {latest.r_sei:.3f} μΩ
  CT电阻: {latest.r_ct:.3f} μΩ
"""
        
        self.text_widget.insert(1.0, content)
        self.text_widget.config(state=tk.DISABLED)
    
    def clear_data(self):
        """清空数据"""
        self.data_manager.clear_data()
        self.update_stats()
    
    def pack(self, **kwargs):
        """打包图表框架"""
        self.chart_frame.pack(**kwargs)
    
    def grid(self, **kwargs):
        """网格布局图表框架"""
        self.chart_frame.grid(**kwargs)
